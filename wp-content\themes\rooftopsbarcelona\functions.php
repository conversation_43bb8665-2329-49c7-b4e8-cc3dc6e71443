<?php
/**
 * Rooftops in Barcelona Child Theme functions and definitions
 */

// Enqueue parent theme stylesheet and custom styles
add_action( 'wp_enqueue_scripts', 'rooftopsbarcelona_enqueue_styles', 20 );
function rooftopsbarcelona_enqueue_styles() {
    // Enqueue Google Fonts
    wp_enqueue_style( 'google-fonts', 'https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap', array(), null );

    // Enqueue Font Awesome
    wp_enqueue_style( 'font-awesome', 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css', array(), '5.15.4' );

    // Enqueue parent theme stylesheet
    wp_enqueue_style( 'generatepress-style', get_template_directory_uri() . '/style.css' );

    // Enqueue child theme style.css
    wp_enqueue_style( 'rooftopsbarcelona-style',
        get_stylesheet_directory_uri() . '/style.css',
        array( 'generatepress-style' ),
        wp_get_theme()->get('Version')
    );

    // Enqueue main CSS file which imports all other CSS files
    wp_enqueue_style( 'rooftopsbarcelona-main',
        get_stylesheet_directory_uri() . '/css/main.css',
        array( 'rooftopsbarcelona-style' ),
        wp_get_theme()->get('Version')
    );

    // Enqueue header styles
    wp_enqueue_style( 'rooftopsbarcelona-header',
        get_stylesheet_directory_uri() . '/css/header.css',
        array( 'rooftopsbarcelona-main' ),
        wp_get_theme()->get('Version')
    );

    // Enqueue footer styles
    wp_enqueue_style( 'rooftopsbarcelona-footer',
        get_stylesheet_directory_uri() . '/css/footer.css',
        array( 'rooftopsbarcelona-main' ),
        wp_get_theme()->get('Version')
    );

    // Enqueue homepage-specific styles
    wp_enqueue_style( 'rooftopsbarcelona-homepage',
        get_stylesheet_directory_uri() . '/css/homepage.css',
        array( 'rooftopsbarcelona-main' ),
        wp_get_theme()->get('Version')
    );

    // Enqueue rooftop archive styles
    wp_enqueue_style( 'rooftopsbarcelona-rooftop-archive',
        get_stylesheet_directory_uri() . '/css/spa-archive.css',
        array( 'rooftopsbarcelona-main' ),
        wp_get_theme()->get('Version')
    );

    // Enqueue rooftop card styles
    wp_enqueue_style( 'rooftopsbarcelona-rooftop-card',
        get_stylesheet_directory_uri() . '/css/spa-card.css',
        array( 'rooftopsbarcelona-main' ),
        wp_get_theme()->get('Version')
    );

    // Enqueue rooftop single page styles
    wp_enqueue_style( 'rooftopsbarcelona-rooftop-single',
        get_stylesheet_directory_uri() . '/css/spa-single.css',
        array( 'rooftopsbarcelona-main' ),
        wp_get_theme()->get('Version')
    );

    // Enqueue testimonials styles
    wp_enqueue_style( 'rooftopsbarcelona-testimonials',
        get_stylesheet_directory_uri() . '/css/testimonials.css',
        array( 'rooftopsbarcelona-rooftop-single' ),
        wp_get_theme()->get('Version')
    );

    // Enqueue pros & cons styles
    wp_enqueue_style( 'rooftopsbarcelona-pros-cons',
        get_stylesheet_directory_uri() . '/css/pros-cons.css',
        array( 'rooftopsbarcelona-rooftop-single' ),
        wp_get_theme()->get('Version')
    );

    // Enqueue enhanced page title styles
    wp_enqueue_style( 'rooftopsbarcelona-page-title',
        get_stylesheet_directory_uri() . '/css/page-title.css',
        array( 'rooftopsbarcelona-main' ),
        wp_get_theme()->get('Version')
    );

    // Enqueue custom scripts
    wp_enqueue_script( 'rooftopsbarcelona-scripts',
        get_stylesheet_directory_uri() . '/js/scripts.js',
        array('jquery'),
        wp_get_theme()->get('Version'),
        true
    );

    // Enqueue header and hero scripts
    wp_enqueue_script( 'rooftopsbarcelona-header-hero',
        get_stylesheet_directory_uri() . '/js/header-hero.js',
        array('jquery'),
        wp_get_theme()->get('Version'),
        true
    );

    // Fix script removed as it's no longer needed

    // Enqueue emergency footer fix script
    wp_enqueue_script( 'rooftopsbarcelona-footer-fix',
        get_stylesheet_directory_uri() . '/js/footer-fix.js',
        array(),  // No dependencies to ensure it loads regardless of other scripts
        time(),    // Use current time as version to prevent caching
        true       // Load in footer
    );

    // Enqueue animation CSS
    wp_enqueue_style( 'animate-css',
        'https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css',
        array(),
        '4.1.1'
    );

    // Fix CSS removed as it's no longer needed

    // Enqueue emergency footer fix CSS with highest priority
    wp_enqueue_style( 'rooftopsbarcelona-footer-fix',
        get_stylesheet_directory_uri() . '/css/footer-fix.css',
        array(),  // No dependencies to ensure it loads regardless of other styles
        time()    // Use current time as version to prevent caching
    );

    // Enqueue custom CSS fixes with highest priority
    wp_enqueue_style( 'rooftopsbarcelona-custom',
        get_stylesheet_directory_uri() . '/css/custom.css',
        array(),  // No dependencies to ensure it loads regardless of other styles
        time()    // Use current time as version to prevent caching
    );

    // Enqueue rooftop title card fixes with highest priority
    wp_enqueue_style( 'rooftopsbarcelona-rooftop-title-card-fix',
        get_stylesheet_directory_uri() . '/css/spa-title-card-fix.css',
        array(),  // No dependencies to ensure it loads regardless of other styles
        time()    // Use current time as version to prevent caching
    );

    // Explicitly enqueue neighborhood styles with highest priority
    wp_enqueue_style( 'rooftopsbarcelona-neighborhood',
        get_stylesheet_directory_uri() . '/css/spa-neighborhood.css',
        array(),  // No dependencies to ensure it loads regardless of other styles
        time()    // Use current time as version to prevent caching
    );

    // Localize script for AJAX
    wp_localize_script( 'rooftopsbarcelona-scripts', 'rooftopsbarcelona_ajax', array(
        'ajax_url' => admin_url( 'admin-ajax.php' ),
        'nonce' => wp_create_nonce( 'rooftopsbarcelona_nonce' )
    ));
}

// Add minimal footer styles
add_action( 'wp_footer', function() {
    echo '<style>
    /* Basic footer styles */
    .site-footer {
        background-color: #3a4a4a !important;
        color: #ffffff !important;
        padding: 30px 0 !important;
    }
    </style>';
} );

// Add spa card image fix styles
add_action( 'wp_head', function() {
    echo '<style>
    /* Fix for spa card images to remove padding/spacing */
    .spa-card {
        overflow: hidden !important;
        padding: 0 !important;
        border-radius: 12px !important;
    }
    .spa-card-inner {
        width: 100% !important;
        padding: 0 !important;
        margin: 0 !important;
    }
    .spa-card-image {
        padding: 0 !important;
        margin: 0 !important;
        border-radius: 12px 12px 0 0 !important;
        overflow: hidden !important;
        width: 100% !important;
        max-width: 100% !important;
        display: block !important;
        background-color: #000 !important;
        position: relative !important;
    }
    .spa-card-image a {
        display: block !important;
        padding: 0 !important;
        margin: 0 !important;
        height: 100% !important;
        width: 100% !important;
        position: relative !important;
        border-radius: 12px 12px 0 0 !important;
        overflow: hidden !important;
        line-height: 0 !important;
        font-size: 0 !important;
    }
    .spa-card-image img {
        display: block !important;
        width: 100% !important;
        height: 100% !important;
        padding: 0 !important;
        margin: 0 !important;
        object-fit: cover !important;
        object-position: center !important;
        border-radius: 12px 12px 0 0 !important;
        max-width: none !important;
        line-height: 0 !important;
        font-size: 0 !important;
    }
    /* Beautiful rating badge styling */
    .spa-rating-badge {
        position: absolute !important;
        bottom: 15px !important;
        left: 50% !important;
        transform: translateX(-50%) !important;
        z-index: 10 !important;
        background: rgba(0, 0, 0, 0.7) !important;
        color: #ffffff !important;
        padding: 14px 22px !important; /* Further increased padding for taller badge */
        border-radius: 30px !important;
        font-weight: 700 !important;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5) !important;
        text-align: center !important;
        min-width: auto !important;
        backdrop-filter: blur(10px) !important;
        border: 2px solid rgba(255, 255, 255, 0.8) !important;
    }
    .spa-rating-badge-stars {
        font-size: 1.4rem !important;
        font-weight: 700 !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        color: #ffffff !important; /* Ensure text is white for better readability */
        text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3) !important; /* Added text shadow for better contrast */
    }
    .star-icon {
        color: #FFD700 !important;
        font-size: 1.5rem !important;
        margin-left: 6px !important;
        text-shadow: 0 0 8px rgba(255, 215, 0, 0.7) !important;
    }
    </style>';
} );

// Register navigation menus
function rooftopsbarcelona_register_menus() {
    register_nav_menus(
        array(
            'footer-menu' => __( 'Footer Menu', 'rooftopsbarcelona' )
        )
    );
}
add_action( 'after_setup_theme', 'rooftopsbarcelona_register_menus' );

// Include additional files
require_once get_stylesheet_directory() . '/inc/template-functions.php';
require_once get_stylesheet_directory() . '/inc/template-hooks.php';
require_once get_stylesheet_directory() . '/inc/customizer.php';
require_once get_stylesheet_directory() . '/flush-rewrites-admin.php';
require_once get_stylesheet_directory() . '/add-neighborhood-menu.php';
require_once get_stylesheet_directory() . '/admin-test-schema.php';
require_once get_stylesheet_directory() . '/admin-validate-schema.php';
require_once get_stylesheet_directory() . '/admin-schema-validator.php';

/**
 * Create necessary pages if they don't exist
 * This function runs on theme activation
 */
function spasinbarcelona_create_required_pages() {
    // Check if the Popular Features page already exists
    $popular_page = get_page_by_path('spa-popular');

    // If the page doesn't exist, create it
    if (!$popular_page) {
        // Create the page
        $page_id = wp_insert_post(array(
            'post_title'    => 'Popular Spa Features',
            'post_content'  => 'Discover spas in Barcelona by popular features. Browse our comprehensive collection of spas categorized by popular features.',
            'post_status'   => 'publish',
            'post_type'     => 'page',
            'post_name'     => 'spa-popular',
            'page_template' => 'page-templates/spa-popular-template.php'
        ));

        // Set the page template
        if ($page_id && !is_wp_error($page_id)) {
            update_post_meta($page_id, '_wp_page_template', 'page-templates/spa-popular-template.php');
        }
    }

    // Check if the Spa Services page already exists
    $services_page = get_page_by_path('spa-services');

    // If the page doesn't exist, create it
    if (!$services_page) {
        // Create the page
        $page_id = wp_insert_post(array(
            'post_title'    => 'Spa Services',
            'post_content'  => 'Discover spas in Barcelona by services offered. Browse our comprehensive collection of spas categorized by services.',
            'post_status'   => 'publish',
            'post_type'     => 'page',
            'post_name'     => 'spa-services',
            'page_template' => 'page-templates/spa-services-template.php'
        ));

        // Set the page template
        if ($page_id && !is_wp_error($page_id)) {
            update_post_meta($page_id, '_wp_page_template', 'page-templates/spa-services-template.php');
        }
    }

    // Neighborhood page creation has been disabled as per client request
    // The code below is kept for reference but will not execute
    /*
    // Check if the Spa Neighborhoods page already exists
    $neighborhoods_page = get_page_by_path('spa-neighborhood');

    // If the page doesn't exist, create it
    if (!$neighborhoods_page) {
        // Create the page
        $page_id = wp_insert_post(array(
            'post_title'    => 'Spa Neighborhoods',
            'post_content'  => 'Discover spas in Barcelona by neighborhood. Browse our comprehensive collection of spas categorized by location throughout Barcelona.',
            'post_status'   => 'publish',
            'post_type'     => 'page',
            'post_name'     => 'spa-neighborhood',
            'page_template' => 'page-templates/spa-neighborhood-template.php'
        ));

        // Set the page template
        if ($page_id && !is_wp_error($page_id)) {
            update_post_meta($page_id, '_wp_page_template', 'page-templates/spa-neighborhood-template.php');
        }
    }
    */

    // Force a flush of rewrite rules
    flush_rewrite_rules();
}

// Run the function on theme activation
add_action('after_switch_theme', 'spasinbarcelona_create_required_pages');

// Also run it now to create the pages immediately
spasinbarcelona_create_required_pages();

/**
 * Force flush rewrite rules when needed
 * This helps ensure our custom URL structures work correctly
 */
function spasinbarcelona_maybe_flush_rules() {
    // Only run for administrators to avoid performance issues
    if (current_user_can('administrator')) {
        // Check if we need to flush based on a query parameter
        if (isset($_GET['flush_rules'])) {
            // Force update taxonomy slugs before flushing
            spasinbarcelona_override_taxonomy_slugs();

            // Add custom rewrite rules
            spasinbarcelona_add_rewrite_rules();

            // Flush rewrite rules
            flush_rewrite_rules();

            // Add a notice for the admin
            add_action('admin_notices', function() {
                echo '<div class="notice notice-success is-dismissible"><p>Rewrite rules have been flushed successfully.</p></div>';
            });

            // Log the action
            error_log('Rewrite rules flushed manually');
        }
    }
}

/**
 * Add a button to the admin bar to flush rewrite rules
 */
function spasinbarcelona_admin_bar_flush_rules_button($admin_bar) {
    if (current_user_can('administrator')) {
        $admin_bar->add_menu(array(
            'id'    => 'flush-rules',
            'title' => 'Flush Rewrite Rules',
            'href'  => add_query_arg('flush_rules', '1'),
            'meta'  => array(
                'title' => 'Flush Rewrite Rules',
            ),
        ));
    }
}
add_action('admin_bar_menu', 'spasinbarcelona_admin_bar_flush_rules_button', 100);
add_action('init', 'spasinbarcelona_maybe_flush_rules', 999);

/**
 * Modify the main query to include spa post type for tag archives and spa_service taxonomy
 */
function spasinbarcelona_modify_tag_query($query) {
    // Only modify the main query on the frontend
    if (!is_admin() && $query->is_main_query()) {
        // For 'popular' taxonomy archives (formerly tags)
        if ($query->is_tax('popular')) {
            $query->set('post_type', array('spa'));
        }

        // For 'services' taxonomy (formerly spa_service)
        if ($query->is_tax('services')) {
            $query->set('posts_per_page', -1); // Consider if this is still needed
        }
        // Add similar blocks if needed for 'specialties', 'amenities'
        // Example for specialties:
        // if ($query->is_tax('specialties')) {
        //     $query->set('post_type', array('spa')); // Ensure it queries spas
        // }
        // Example for amenities:
        // if ($query->is_tax('amenities')) {
        //     $query->set('post_type', array('spa')); // Ensure it queries spas
        // }
    }
}

// Hook into the pre_get_posts action
add_action('pre_get_posts', 'spasinbarcelona_modify_tag_query');

/**
 * Add custom description for spa post type archive and taxonomy archives
 */
function spasinbarcelona_spa_archive_description($description) {
    if (is_post_type_archive('spa')) {
        $description = "<p>Discover the finest spas in Barcelona with our comprehensive directory. Browse through our curated selection of luxury spas, wellness centers, and beauty treatment facilities. Use our convenient filters to find the perfect spa experience based on services, features, location, and price range. Whether you're looking for a relaxing massage, rejuvenating facial, or complete wellness retreat, our Barcelona spa guide has everything you need.</p>";
    } elseif (is_tax('neighborhoods')) { // Updated
        $term = get_queried_object();
        if ($term && !is_wp_error($term)) {
            // Check if there's a custom description in term meta
            $seo_description = get_term_meta($term->term_id, 'seo_description', true);
            if (!empty($seo_description)) {
                $description = "<p>" . esc_html($seo_description) . "</p>";
            } else {
                // Get parent term (district) if it exists
                if ($term->parent) {
                    $parent_term = get_term($term->parent, 'neighborhoods'); // Updated
                    if (!is_wp_error($parent_term)) {
                        $description = "<p>Discover the best spas in the " . esc_html($term->name) . " neighborhood of " . esc_html($parent_term->name) . "in Barcelona. Browse our selection of luxury spas and wellness centers in this area.</p>";
                    } else {
                        $description = "<p>Discover the best spas in " . esc_html($term->name) . "in Barcelona. Browse our selection of luxury spas and wellness centers in this neighborhood.</p>";
                    }
                } else {
                    $description = "<p>Discover the best spas in the " . esc_html($term->name) . " area of Barcelona. Browse our selection of luxury spas and wellness centers in this district.</p>";
                }
            }
        }
    } elseif (is_tax('specialties')) {
        $term = get_queried_object();
        $description = "<p>Explore spas in Barcelona offering " . esc_html(strtolower($term->name)) . ". Find the perfect spa experience tailored to your preferred specialty.</p>";
    } elseif (is_tax('services')) {
        $term = get_queried_object();
        $description = "<p>Discover spas providing " . esc_html(strtolower($term->name)) . " in Barcelona. Your ideal spa service awaits.</p>";
    } elseif (is_tax('amenities')) {
        $term = get_queried_object();
        $description = "<p>Find spas equipped with " . esc_html(strtolower($term->name)) . " in Barcelona. Enjoy these features for an enhanced spa visit.</p>";
    } elseif (is_tax('popular')) {
        $term = get_queried_object();
        $description = "<p>Explore spas known for " . esc_html(strtolower($term->name)) . " in Barcelona. These are highly sought-after spa characteristics.</p>";
    }
    return $description;
}
add_filter('get_the_archive_description', 'spasinbarcelona_spa_archive_description');

/**
 * Override taxonomy slugs to use custom URL structures
 * This ensures the URLs will always use our preferred slugs even if the plugin is updated
 */
function spasinbarcelona_override_taxonomy_slugs() {
    global $wp_taxonomies;

    // This function attempts to force slugs. Given the plugin now defines these,
    // this function might be redundant or cause conflicts.
    // It's updated to reflect the new slugs, but should be tested carefully.

    // Ensure 'amenities' taxonomy (formerly spa_feature) uses 'amenities' slug
    if (isset($wp_taxonomies['amenities'])) { // Check for the new slug
        $wp_taxonomies['amenities']->rewrite['slug'] = 'amenities';
    } elseif (isset($wp_taxonomies['spa_feature'])) { // Fallback for old slug during transition
        $wp_taxonomies['spa_feature']->rewrite['slug'] = 'amenities';
    }

    // Ensure 'specialties' taxonomy (formerly spa_category) uses 'specialties' slug
    if (isset($wp_taxonomies['specialties'])) { // Check for the new slug
        $wp_taxonomies['specialties']->rewrite['slug'] = 'specialties';
    } elseif (isset($wp_taxonomies['spa_category'])) { // Fallback
        $wp_taxonomies['spa_category']->rewrite['slug'] = 'specialties';
    }

    // Ensure 'services' taxonomy (formerly spa_service) uses 'services' slug
    if (isset($wp_taxonomies['services'])) { // Check for the new slug
        $wp_taxonomies['services']->rewrite['slug'] = 'services';
    } elseif (isset($wp_taxonomies['spa_service'])) { // Fallback
        $wp_taxonomies['spa_service']->rewrite['slug'] = 'services';
    }

    // Ensure 'popular' taxonomy (formerly post_tag for spas) uses 'popular' slug
    if (isset($wp_taxonomies['popular'])) { // Check for the new slug
        $wp_taxonomies['popular']->rewrite['slug'] = 'popular';
    }
    // Note: The 'post_tag' part is removed as 'popular' is now its own taxonomy.
    // If 'post_tag' is still intended to be 'popular' for other post types, that's a different concern.

    // Ensure 'neighborhoods' taxonomy (formerly spa_neighborhood) uses 'neighborhoods' slug
    if (isset($wp_taxonomies['neighborhoods'])) { // Check for the new slug
        $wp_taxonomies['neighborhoods']->rewrite['slug'] = 'neighborhoods';
        $wp_taxonomies['neighborhoods']->rewrite['with_front'] = false;
        $wp_taxonomies['neighborhoods']->rewrite['hierarchical'] = true;
    } elseif (isset($wp_taxonomies['spa_neighborhood'])) { // Fallback
        $wp_taxonomies['spa_neighborhood']->rewrite['slug'] = 'neighborhoods';
        $wp_taxonomies['spa_neighborhood']->rewrite['with_front'] = false;
        $wp_taxonomies['spa_neighborhood']->rewrite['hierarchical'] = true;
    }
}

// Run this after the taxonomies are registered
add_action('init', 'spasinbarcelona_override_taxonomy_slugs', 11);

/**
 * Filter term links to ensure they use our custom slugs
 */
function spasinbarcelona_term_link_filter($termlink, $term, $taxonomy) {
    // This function manually adjusts term links.
    // With correct taxonomy registration, this might become less necessary or simpler.

    $base_url = home_url('/');

    switch ($taxonomy) {
        case 'specialties': // formerly spa_category
        case 'spa_category':
            $termlink = $base_url . 'specialties/' . $term->slug . '/';
            break;
        case 'amenities': // formerly spa_feature
        case 'spa_feature':
            $termlink = $base_url . 'amenities/' . $term->slug . '/';
            break;
        case 'services': // formerly spa_service
        case 'spa_service':
            $termlink = $base_url . 'services/' . $term->slug . '/';
            break;
        case 'popular': // formerly post_tag for spas
        case 'post_tag': // Only if post_tag is still relevant for spas and should point to /popular/
            // If 'popular' is the new dedicated taxonomy, this 'post_tag' case might be removed
            // or adjusted if 'post_tag' is used for other things and needs different handling.
            if ($term->taxonomy === 'popular' || $taxonomy === 'popular' || ($taxonomy === 'post_tag' /* && is_singular('spa') or on spa archive */) ) {
                 $termlink = $base_url . 'popular/' . $term->slug . '/';
            }
            break;
        case 'neighborhoods': // formerly spa_neighborhood
        case 'spa_neighborhood':
            $termlink = $base_url . 'neighborhoods/' . $term->slug . '/';
            break;
    }

    return $termlink;
}

// Add filter for term links
add_filter('term_link', 'spasinbarcelona_term_link_filter', 10, 3);

/**
 * Add custom rewrite rules for spa specialties and popular features
 */
function spasinbarcelona_add_rewrite_rules() {
    // These rules map URL patterns to WordPress query variables.
    // The query variable should match the taxonomy slug.

    add_rewrite_rule(
        'specialties/([^/]+)/?$',
        'index.php?specialties=$matches[1]', // Query var 'specialties'
        'top'
    );
    add_rewrite_rule(
        'services/([^/]+)/?$',
        'index.php?services=$matches[1]', // Query var 'services'
        'top'
    );
    add_rewrite_rule(
        'amenities/([^/]+)/?$',
        'index.php?amenities=$matches[1]', // Query var 'amenities'
        'top'
    );
    add_rewrite_rule(
        'popular/([^/]+)/?$',
        'index.php?popular=$matches[1]', // Query var 'popular'
        'top'
    );
    add_rewrite_rule(
        'neighborhoods/([^/]+)/?$',
        'index.php?neighborhoods=$matches[1]', // Query var 'neighborhoods'
        'top'
    );

    // If you still have pages like /spa-services/ that are actual WordPress pages
    // and not taxonomy archives, their rules might need to be preserved or adjusted.
    // For example, if 'spa-services' is a page:
    // add_rewrite_rule('spa-services/?$', 'index.php?pagename=spa-services', 'top');
    // However, the goal is usually for /services/ to be the taxonomy archive.

    if (current_user_can('administrator')) {
        error_log('Updated SIB rewrite rules for new taxonomies');
    }
}
add_action('init', 'spasinbarcelona_add_rewrite_rules', 12);

/**
 * Register custom query variables to match filter names from archive-spa.php
 * And ensure WordPress recognizes the taxonomy slugs as query_vars if not automatically handled.
 */
function spasinbarcelona_register_query_vars($vars) {
    // These are for the filter dropdowns in archive-spa.php
    $vars[] = 'popular_filter';
    $vars[] = 'service_filter';
    $vars[] = 'amenities_filter';
    $vars[] = 'neighborhood_filter';

    // WordPress should automatically add query vars for taxonomies if 'query_var' is true
    // in their registration. Adding them here is a fallback or for clarity.
    // $vars[] = 'specialties';
    // $vars[] = 'services';
    // $vars[] = 'amenities';
    // $vars[] = 'popular';
    // $vars[] = 'neighborhoods';
    return $vars;
}
add_filter('query_vars', 'spasinbarcelona_register_query_vars');

/**
 * Debug function to display information about the current page
 */
function spasinbarcelona_debug_info() {
    if (current_user_can('administrator') && isset($_GET['debug'])) {
        global $wp_query, $wp_rewrite;

        echo '<div style="background: #fff; padding: 20px; margin: 20px; border: 1px solid #ddd;">';
        echo '<h2>Debug Information</h2>';

        echo '<h3>Query Variables</h3>';
        echo '<pre>' . print_r($wp_query->query_vars, true) . '</pre>';

        echo '<h3>Queried Object</h3>';
        echo '<pre>' . print_r($wp_query->queried_object, true) . '</pre>';

        echo '<h3>Rewrite Rules</h3>';
        echo '<pre>' . print_r($wp_rewrite->rewrite_rules(), true) . '</pre>';

        echo '</div>';
    }
}
add_action('wp_footer', 'spasinbarcelona_debug_info');

/**
 * Custom template loader for spa-services and spa-popular URLs
 */
function spasinbarcelona_template_include($template) {
    global $wp_query;
    $current_url = $_SERVER['REQUEST_URI'];

    if (current_user_can('administrator')) {
        error_log('Template Include Debug - URL: ' . $current_url);
        error_log('Template Include Debug - Original Template: ' . $template);
    }

    // Helper function to extract term slug
    $get_term_slug_from_url = function($url_segment_base) use ($current_url) {
        if (strpos($current_url, '/' . $url_segment_base . '/') !== false) {
            $url_parts = explode('/', trim($current_url, '/'));
            $term_slug = end($url_parts);
            // If the term slug is empty (due to trailing slash), get the previous part
            if (empty($term_slug) && count($url_parts) > 1 && $url_parts[count($url_parts)-2] === $url_segment_base) {
                 // This means it's likely the base URL like /services/ , not /services/term/
                 return ''; // Return empty if it's the base archive URL
            }
            if ($term_slug === $url_segment_base) return ''; // Base archive URL
            return $term_slug;
        }
        return null;
    };

    $tax_slug_map = [
        'specialties' => 'specialties', // New slug, template might be taxonomy-specialties.php
        'services' => 'services',       // New slug, template might be taxonomy-services.php
        'amenities' => 'amenities',     // New slug, template might be taxonomy-amenities.php
        'popular' => 'popular',         // New slug, template might be taxonomy-popular.php or tag-popular.php
        'neighborhoods' => 'neighborhoods' // New slug, template might be taxonomy-neighborhoods.php
    ];

    foreach ($tax_slug_map as $url_segment => $taxonomy_name) {
        $term_slug = $get_term_slug_from_url($url_segment);

        if ($term_slug !== null) { // If the URL segment is found
            if (current_user_can('administrator')) {
                error_log(ucfirst($taxonomy_name) . ' Debug - Matched segment: /' . $url_segment . '/, Term Slug: ' . $term_slug);
            }

            // If term_slug is empty, it means it's the archive page for the taxonomy itself,
            // WordPress should handle this with e.g., taxonomy-services.php.
            // This custom loader is more for when a specific term is in the URL.
            if (!empty($term_slug)) {
                // Attempt to locate a specific template first, then a generic one
                $template_files = array(
                    'taxonomy-' . $taxonomy_name . '-' . $term_slug . '.php', // e.g., taxonomy-neighborhoods-vila-olimpica.php
                    'taxonomy-' . $taxonomy_name . '.php',                   // e.g., taxonomy-neighborhoods.php
                    // 'tag-' . $taxonomy_name . '.php' // For non-hierarchical like 'popular' if it behaves like tags
                );
                if ($taxonomy_name === 'popular') { // popular might use tag template conventions
                    $template_files[] = 'tag-' . $term_slug . '.php';
                    $template_files[] = 'tag.php';
                }


                $new_template = locate_template($template_files);

                if (!empty($new_template)) {
                    $term = get_term_by('slug', $term_slug, $taxonomy_name);
                    if ($term && !is_wp_error($term)) {
                        $args = array(
                            'post_type' => 'spa',
                            'posts_per_page' => -1, // Or your desired posts per page
                            'tax_query' => array(
                                array(
                                    'taxonomy' => $taxonomy_name,
                                    'field' => 'slug',
                                    'terms' => $term_slug,
                                ),
                            ),
                        );
                        // Create a new query
                        $custom_query = new WP_Query($args);

                        // Set query vars for the template to use
                        set_query_var($taxonomy_name . '_query', $custom_query); // e.g., services_query
                        set_query_var($taxonomy_name . '_term', $term);       // e.g., services_term

                        // Make WordPress recognize this as a taxonomy archive page
                        $wp_query->is_tax = true;
                        $wp_query->is_archive = true;
                        $wp_query->queried_object = $term;
                        $wp_query->queried_object_id = $term->term_id;
                        $wp_query->is_404 = false; // Ensure it's not a 404

                        if (current_user_can('administrator')) {
                            error_log(ucfirst($taxonomy_name) . ' Debug - Using template: ' . $new_template . ' for term ' . $term_slug);
                            error_log(ucfirst($taxonomy_name) . ' Query has ' . $custom_query->post_count . ' posts.');
                        }
                        return $new_template;
                    } else {
                         if (current_user_can('administrator')) {
                            error_log(ucfirst($taxonomy_name) . ' Debug - Term not found for slug: ' . $term_slug . ' in taxonomy ' . $taxonomy_name);
                        }
                    }
                } else {
                     if (current_user_can('administrator')) {
                        error_log(ucfirst($taxonomy_name) . ' Debug - No specific template found for: ' . $term_slug . ' (tried: ' . implode(', ', $template_files) . ')');
                    }
                }
            } else {
                 if (current_user_can('administrator')) {
                    error_log(ucfirst($taxonomy_name) . ' Debug - Base archive for /' . $url_segment . '/. Letting WordPress handle template.');
                }
                // For base archive pages like /services/, /neighborhoods/, let WordPress's default hierarchy work.
                // This function is primarily for individual term pages if WP isn't picking them up correctly.
            }
        }
    }
    return $template;
}
add_filter('template_include', 'spasinbarcelona_template_include', 99);

/**
 * Standardize URLs with trailing slashes for all spa taxonomy pages
 * This ensures all taxonomy URLs have a consistent format
 */
function spasinbarcelona_standardize_taxonomy_urls() {
    // Only run on frontend requests
    if (is_admin()) {
        return;
    }

    // Get the current URL
    $current_url = $_SERVER['REQUEST_URI'];

    // Check if this is a spa taxonomy URL (neighborhood, service, feature, etc.)
    $spa_taxonomies = array(
        '/spa-neighborhood/',
        '/spa-services/',
        '/spa-amenities/',
        '/spa-popular/',
        '/spa-feature/',
        '/spa-service/',
        '/spa-category/'
    );

    $is_spa_taxonomy = false;
    foreach ($spa_taxonomies as $taxonomy_url) {
        if (strpos($current_url, $taxonomy_url) !== false) {
            $is_spa_taxonomy = true;
            break;
        }
    }

    if ($is_spa_taxonomy) {
        // Check if the URL already has a trailing slash
        $has_trailing_slash = (substr($current_url, -1) === '/');
        $has_query_string = (strpos($current_url, '?') !== false);
        $has_hash = (strpos($current_url, '#') !== false);

        // If no trailing slash and no query string or hash, redirect to the URL with trailing slash
        if (!$has_trailing_slash && !$has_query_string && !$has_hash) {
            // Add trailing slash
            $redirect_url = $current_url . '/';

            // Log for debugging if admin
            if (current_user_can('administrator')) {
                error_log('URL Standardization: Redirecting ' . $current_url . ' to ' . $redirect_url);
            }

            // Perform the redirect
            wp_redirect($redirect_url, 301);
            exit;
        }
    }
}
add_action('template_redirect', 'spasinbarcelona_standardize_taxonomy_urls', 5); // Priority 5 to run early

/**
 * Ensure WordPress uses trailing slashes in permalinks
 * This function runs once during theme activation
 */
function spasinbarcelona_ensure_trailing_slashes() {
    // Get the current permalink structure
    $permalink_structure = get_option('permalink_structure');

    // Check if it already has a trailing slash
    if (substr($permalink_structure, -1) !== '/') {
        // Add trailing slash
        $new_structure = $permalink_structure . '/';

        // Update the permalink structure
        update_option('permalink_structure', $new_structure);

        // Flush rewrite rules
        flush_rewrite_rules();
    }
}

// Run this function when the theme is activated
add_action('after_switch_theme', 'spasinbarcelona_ensure_trailing_slashes');

/**
 * Add a notice to remind about permalink structure if it doesn't have trailing slashes
 */
function spasinbarcelona_permalink_notice() {
    // Only show to administrators
    if (!current_user_can('administrator')) {
        return;
    }

    // Get the current permalink structure
    $permalink_structure = get_option('permalink_structure');

    // Check if it has a trailing slash
    if (substr($permalink_structure, -1) !== '/') {
        ?>
        <div class="notice notice-warning is-dismissible">
            <p><strong>Spas in Barcelona:</strong> Your permalink structure doesn't end with a trailing slash. For consistent URLs, please go to <a href="<?php echo admin_url('options-permalink.php'); ?>">Settings &gt; Permalinks</a> and ensure your permalink structure ends with a slash (/).</p>
        </div>
        <?php
    }
}
add_action('admin_notices', 'spasinbarcelona_permalink_notice');

/**
 * Function to get spa services for a specific spa
 */
function spasinbarcelona_get_spa_services($post_id) {
    $services = get_the_terms($post_id, 'spa_service');
    if (!$services || is_wp_error($services)) {
        return array();
    }

    return $services; // This function is spasinbarcelona_get_spa_services
}

/**
 * Function to display spa service links
 */
function spasinbarcelona_display_spa_service_links($post_id) {
    $services = spasinbarcelona_get_spa_services($post_id); // This correctly calls the updated function above
    if (empty($services)) {
        return;
    }

    echo '<div class="spa-service-links">';
    foreach ($services as $service) {
        // Use get_term_link for robustness, it will respect the registered rewrite rules
        $service_link = get_term_link($service, 'services'); // Taxonomy is 'services'
        if (!is_wp_error($service_link)) {
            echo '<a href="' . esc_url($service_link) . '" class="spa-service-link">';
            echo esc_html($service->name);
            echo '</a>';
        }
    }
    echo '</div>';
}

/**
 * Function to get spa neighborhoods for a specific spa
 */
function spasinbarcelona_get_spa_neighborhoods($post_id) {
    $neighborhoods = get_the_terms($post_id, 'neighborhoods'); // Updated to 'neighborhoods'
    if (!$neighborhoods || is_wp_error($neighborhoods)) {
        return array();
    }

    return $neighborhoods;
}

/**
 * Function to display spa neighborhood links
 */
function spasinbarcelona_display_spa_neighborhood_links($post_id) {
    $neighborhoods = spasinbarcelona_get_spa_neighborhoods($post_id);
    if (empty($neighborhoods)) {
        return;
    }

    echo '<div class="spa-neighborhood-links">';
    foreach ($neighborhoods as $neighborhood) {
        // Use get_term_link for robustness
        $neighborhood_link = get_term_link($neighborhood, 'neighborhoods'); // Taxonomy is 'neighborhoods'
        if (!is_wp_error($neighborhood_link)) {
            echo '<a href="' . esc_url($neighborhood_link) . '" class="spa-neighborhood-link">';
            echo esc_html($neighborhood->name);
            echo '</a>';
        }
    }
    echo '</div>';
}

// Remove sidebar from specific pages (Contact Us, About Us)
add_filter( 'generate_sidebar_layout', 'spasinbarcelona_no_sidebar_for_specific_pages' );
function spasinbarcelona_no_sidebar_for_specific_pages( $layout ) {
    // Ensure is_page() can accept an array of slugs/IDs
    if ( is_page( array( 'contact-us', 'about-us', 'contact', 'about', 'privacy-policy' ) ) ) { // Added common variations
        return 'no-sidebars';
    }
    return $layout;
}

/**
 * Modify the main query on the spa archive page to apply filters.
 */
function spasinbarcelona_apply_spa_archive_filters($query) {
    if (!is_admin() && $query->is_main_query() && $query->is_post_type_archive('spa')) {
        $tax_query = array('relation' => 'AND');

        // Popular filter
        if (!empty($_GET['popular_filter'])) {
            $tax_query[] = array(
                'taxonomy' => 'popular',
                'field'    => 'slug',
                'terms'    => sanitize_text_field($_GET['popular_filter']),
            );
        }

        // Service filter
        if (!empty($_GET['service_filter'])) {
            $tax_query[] = array(
                'taxonomy' => 'services',
                'field'    => 'slug',
                'terms'    => sanitize_text_field($_GET['service_filter']),
            );
        }

        // Amenities filter
        if (!empty($_GET['amenities_filter'])) {
            $tax_query[] = array(
                'taxonomy' => 'amenities',
                'field'    => 'slug',
                'terms'    => sanitize_text_field($_GET['amenities_filter']),
            );
        }

        // Neighborhood filter
        if (!empty($_GET['neighborhood_filter'])) {
            $tax_query[] = array(
                'taxonomy' => 'neighborhoods',
                'field'    => 'slug',
                'terms'    => sanitize_text_field($_GET['neighborhood_filter']),
            );
        }

        if (count($tax_query) > 1) {
            $query->set('tax_query', $tax_query);
        }

        // Search filter
        if (!empty($_GET['search'])) {
            $query->set('s', sanitize_text_field($_GET['search']));
        }

        // Sort by filter
        if (!empty($_GET['sort_by'])) {
            $sort_by = sanitize_text_field($_GET['sort_by']);
            if ($sort_by === 'alphabetical') {
                $query->set('orderby', 'title');
                $query->set('order', 'ASC');
            } elseif ($sort_by === 'rating') {
                // Assuming 'rating' is a meta key storing the average rating.
                // Adjust meta_key if it's different.
                $query->set('meta_key', 'reviews_average_rating'); // You might need to create/update this meta key
                $query->set('orderby', 'meta_value_num');
                $query->set('order', 'DESC');
            }
        }
    }
}
add_action('pre_get_posts', 'spasinbarcelona_apply_spa_archive_filters');

// Role-based control for PHP error display
function spasinbarcelona_control_php_error_visibility() {
    if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
        if ( current_user_can( 'manage_options' ) ) {
            // Administrator: display errors
            @ini_set( 'display_errors', 1 );
            // Ensure full error reporting for admins
            @error_reporting( E_ALL );
        } else {
            // Non-administrator: do not display errors
            @ini_set( 'display_errors', 0 );
        }
    } else {
        // WP_DEBUG is off, ensure errors are not displayed
        @ini_set( 'display_errors', 0 );
    }
}
add_action( 'after_setup_theme', 'spasinbarcelona_control_php_error_visibility', 0 ); // Run very early

// Role-based control for JavaScript console output
function spasinbarcelona_js_debug_control() {
    // Only apply this to non-administrators
    if ( ! current_user_can( 'manage_options' ) ) {
        ?>
        <script type="text/javascript">
            (function() {
                if (typeof window.console === 'object') {
                    // Overwrite console methods to do nothing
                    console.log = function() {};
                    console.error = function() {};
                    console.warn = function() {};
                    console.info = function() {};
                    console.debug = function() {};
                    // Add other console methods if they are used and need to be suppressed
                    // e.g., console.table, console.dir
                }
            })();
        </script>
        <?php
    }
}
// Add this to wp_head for frontend and admin_head for backend to cover all areas.
add_action( 'wp_head', 'spasinbarcelona_js_debug_control', 1 ); // Priority 1 to run early
add_action( 'admin_head', 'spasinbarcelona_js_debug_control', 1 ); // Priority 1 to run early

/**
 * Dynamically sets the SEO title for specific taxonomy archive pages.
 */
function spasinbarcelona_custom_seo_title_parts( $title_parts ) {
    $target_taxonomies = array( 'popular', 'services', 'amenities', 'neighborhoods' ); // Add 'specialties' if needed

    if ( is_tax( $target_taxonomies ) ) {
        $term = get_queried_object();
        $site_name = get_bloginfo( 'name' );
        $new_title = '';

        if ( $term && ! is_wp_error( $term ) ) {
            $term_name = esc_html( $term->name );
            $taxonomy_slug = $term->taxonomy;

            switch ( $taxonomy_slug ) {
                case 'popular':
                    $new_title = sprintf( 'Best Popular Spas for %s in Barcelona | %s', $term_name, $site_name );
                    break;
                case 'services':
                    $new_title = sprintf( 'Best %s Spa Services in Barcelona | %s', $term_name, $site_name );
                    break;
                case 'amenities':
                    $new_title = sprintf( 'Best Barcelona Spas with %s | %s', $term_name, $site_name );
                    break;
                case 'neighborhoods':
                    $new_title = sprintf( 'Best Spas in %s, Barcelona | %s', $term_name, $site_name );
                    break;
                // Add case for 'specialties' if you want to include it
                // case 'specialties':
                //     $new_title = sprintf( 'Spas Specializing in %s in Barcelona | %s', $term_name, $site_name );
                //     break;
            }

            if ( ! empty( $new_title ) ) {
                $title_parts['title'] = $new_title;
                if (isset($title_parts['tagline'])) {
                    unset($title_parts['tagline']);
                }
                if (isset($title_parts['site'])) {
                    unset($title_parts['site']);
                }
            }
        }
    } elseif ( is_front_page() ) {
        $site_name = get_bloginfo( 'name' ); // "SpasInBarcelona"
        // Intended title: "Best Spas in Barcelona: Your Ultimate Guide to Relaxation"
        // We can use the existing site name or a more keyword-rich version.
        // Let's make it more keyword focused and keep the brand at the end if desired.
        $title_parts['title'] = 'Best Spas in Barcelona: Your Ultimate Guide to Relaxation';
        // $title_parts['tagline'] = $site_name; // Or remove tagline if title is long enough
        if (isset($title_parts['tagline'])) {
             unset($title_parts['tagline']); // Remove default tagline
        }
         // Keep site name if you want "Title | Site Name"
        // $title_parts['site'] = $site_name;
        // If the title is comprehensive, you might not need to append site name again.
        // For "Best Spas in Barcelona: Your Ultimate Guide to Relaxation", appending site name might be redundant or too long.
        // Let's remove the default site part as well, assuming the title is self-sufficient.
        if (isset($title_parts['site'])) {
            unset($title_parts['site']);
        }
    } elseif ( is_post_type_archive('spa') ) {
        $title_parts['title'] = 'Directory of All Spas in Barcelona | Filter & Find Your Ideal Spa';
        // Remove default tagline and site name if they exist to avoid "Title - Tagline - Site Name"
        if (isset($title_parts['tagline'])) {
             unset($title_parts['tagline']);
        }
        if (isset($title_parts['site'])) {
            unset($title_parts['site']);
        }
    }
    return $title_parts;
}
add_filter( 'document_title_parts', 'spasinbarcelona_custom_seo_title_parts', 999 );

/**
 * Adds a custom meta description to the homepage.
 */
function spasinbarcelona_homepage_meta_description() {
    if ( is_front_page() ) {
        $description = "Discover the top spas in Barcelona. Explore luxury retreats, wellness centers, and therapeutic treatments for ultimate relaxation. Find your perfect Barcelona spa experience!";
        echo '<meta name="description" content="' . esc_attr( $description ) . '" />' . "\n";
    }
}
add_action( 'wp_head', 'spasinbarcelona_homepage_meta_description', 5 );
