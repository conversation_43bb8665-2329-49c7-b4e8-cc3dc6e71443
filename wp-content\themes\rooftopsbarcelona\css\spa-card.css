/**
 * Spa card styles for Spas in Barcelona theme
 */

/* Spa Card */
.spa-card {
    background-color: #fff;
    border-radius: 12px; /* More rounded corners */
    box-shadow: var(--box-shadow);
    overflow: hidden;
    transition: var(--transition);
    height: 100%;
    display: flex;
    flex-direction: column;
    border: 1px solid rgba(210, 180, 140, 0.2); /* Very subtle border */
}

.spa-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(104, 160, 160, 0.15); /* Shadow with primary color */
    border-color: var(--primary-color);
}

.spa-card-inner {
    display: flex;
    flex-direction: column;
    height: 100%;
}

/* Spa Card Image */
.spa-card-image {
    position: relative;
    height: 200px;
    overflow: hidden;
    margin: 0;
    padding: 0;
    border-radius: 12px 12px 0 0;
    background-color: #000;
}

.spa-card-image a {
    display: block;
    height: 100%;
    width: 100%;
    margin: 0;
    padding: 0;
    line-height: 0;
    font-size: 0;
}

.spa-card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.8s ease; /* Slower transition for a more relaxed feel */
    filter: brightness(1.02); /* Slightly brighter images */
    display: block;
    margin: 0;
    padding: 0;
    border-radius: 12px 12px 0 0;
    object-position: center;
    line-height: 0;
    font-size: 0;
}

.spa-card:hover .spa-card-image img {
    transform: scale(1.03); /* Slightly more subtle zoom effect */
    filter: brightness(1.05); /* Slightly brighter on hover */
}

.spa-category {
    position: absolute;
    bottom: 10px;
    left: 10px;
    background-color: var(--primary-color);
    color: #fff;
    padding: 6px 12px;
    border-radius: 20px; /* Rounded pill shape */
    font-size: 0.8rem;
    font-weight: 500;
    z-index: 1;
    letter-spacing: 0.02em;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); /* Subtle shadow */
}

/* Rating Badge on Image */
.spa-rating-badge {
    position: absolute;
    bottom: 15px;
    left: 15px; /* Positioned on the left side instead of center */
    background: rgba(0, 0, 0, 0.8);
    color: #ffffff;
    padding: 5px 12px; /* Smaller padding for a more compact badge */
    border-radius: 30px;
    font-weight: 700;
    z-index: 10;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
    text-align: center;
    min-width: auto;
    transition: all 0.3s ease;
}

.spa-card:hover .spa-rating-badge {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.6);
}

.spa-rating-badge-stars {
    font-size: 1.1rem;
    letter-spacing: 0.02em;
    display: flex;
    align-items: center;
    justify-content: center;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
    font-weight: 700;
    color: #ffffff;
}

.star-icon {
    color: #FFD700;
    font-size: 1.2rem;
    margin-left: 4px;
    display: inline-block;
    text-shadow: 0 0 4px rgba(255, 215, 0, 0.7);
}

/* Rating badge count removed to save space */

.spa-featured-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: var(--secondary-color);
    color: #fff;
    padding: 6px 12px;
    border-radius: 20px; /* Rounded pill shape */
    font-size: 0.8rem;
    font-weight: 500;
    z-index: 1;
    letter-spacing: 0.02em;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); /* Subtle shadow */
}

.spa-no-image {
    width: 100%;
    height: 100%;
    background-color: var(--light-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgba(104, 160, 160, 0.3); /* Faded primary color */
    font-size: 3rem;
}

/* Spa Card Content */
.spa-card-content {
    padding: 20px; /* Adjusted padding */
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    background: linear-gradient(to bottom, #ffffff, #f9f9f5); /* Subtle gradient background */
    border-radius: 0 0 12px 12px;
}

.spa-card-title {
    font-size: 1.3rem;
    margin-bottom: 12px;
    line-height: 1.4;
    letter-spacing: 0.01em;
}

.spa-card-title a {
    color: var(--dark-color);
    text-decoration: none;
}

.spa-card-title a:hover {
    color: var(--primary-color);
}

.spa-location {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    color: #777;
    font-size: 0.9rem;
    letter-spacing: 0.01em;
}

.spa-location i {
    margin-right: 5px;
    color: var(--primary-color);
}

.spa-rating {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    line-height: 1.2;
}

.star-rating {
    display: inline-block;
    margin-right: 5px;
    letter-spacing: 2px;
    line-height: 1;
}

.spa-review-count {
    font-size: 0.85rem;
    color: #666;
    font-weight: 500;
}

.spa-services {
    margin-bottom: 15px;
    font-size: 0.9rem;
    color: #666;
    display: flex;
    align-items: flex-start;
}

.spa-services i {
    margin-right: 5px;
    color: var(--primary-color);
    margin-top: 3px;
}

.spa-excerpt {
    margin-bottom: 15px;
    font-size: 0.95rem;
    color: #555;
    flex-grow: 1;
    line-height: 1.6;
    letter-spacing: 0.01em;
    height: 9.6em; /* 6 lines * 1.6 line-height */
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 6;
    -webkit-box-orient: vertical;
}

/* Spa Card Services */
.spa-card-services {
    display: none; /* Hide service tags as requested */
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 15px;
}

.spa-card-service-tag {
    display: inline-flex;
    align-items: center;
    background-color: #f0f5f5;
    color: var(--primary-color);
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    transition: var(--transition);
    text-decoration: none;
    border: 1px solid rgba(104, 160, 160, 0.2);
}

.spa-card-service-tag i {
    margin-right: 5px;
    font-size: 0.75rem;
}

.spa-card-service-tag:hover {
    background-color: var(--primary-color);
    color: #fff;
    transform: translateY(-2px);
}

.spa-read-more {
    display: inline-flex;
    align-items: center;
    color: var(--primary-color);
    font-weight: 500;
    font-size: 0.9rem;
    margin-top: auto;
    padding: 8px 0;
    letter-spacing: 0.02em;
    border-top: 1px solid rgba(210, 180, 140, 0.2); /* Subtle separator */
}

.spa-read-more i {
    margin-left: 5px;
    transition: transform 0.3s ease;
}

.spa-read-more:hover i {
    transform: translateX(3px);
}

/* Responsive Styles */
@media (max-width: 768px) {
    .spa-card-image {
        height: 180px;
    }
}

@media (max-width: 576px) {
    .spa-card-image {
        height: 160px;
    }

    .spa-card-content {
        padding: 15px;
        border-radius: 0 0 12px 12px;
    }

    .spa-card-title {
        font-size: 1.2rem;
    }
}
