<?php
/**
 * Standardize Neighborhood Slugs
 * 
 * This script standardizes all neighborhood slugs by:
 * 1. Removing accents and special characters
 * 2. Ensuring consistent URL format
 * 3. Maintaining proper display names with accents
 */

// Load WordPress
require_once( dirname( __FILE__ ) . '/../../wp-load.php' );

// Security check - only administrators can access this page
if (!current_user_can('administrator')) {
    wp_die('You do not have permission to access this page.');
}

echo '<h1>Standardize Neighborhood Slugs</h1>';

// Get all neighborhood terms
$neighborhoods = get_terms(array(
    'taxonomy' => 'spa_neighborhood',
    'hide_empty' => false,
));

echo '<div style="background: #e0f0ff; padding: 15px; margin: 15px 0; border: 1px solid #0073aa;">';
echo '<h2>Standardizing ' . count($neighborhoods) . ' Neighborhood Terms</h2>';

// Process each neighborhood
foreach ($neighborhoods as $neighborhood) {
    // Get the current name and slug
    $current_name = $neighborhood->name;
    $current_slug = $neighborhood->slug;
    
    // Create a standardized slug (remove accents, lowercase, replace spaces with hyphens)
    $standardized_slug = sanitize_title(remove_accents($current_name));
    
    echo '<div style="margin-bottom: 15px; padding: 10px; border: 1px solid #ccc;">';
    echo '<h3>' . esc_html($current_name) . ' (ID: ' . $neighborhood->term_id . ')</h3>';
    echo '<p>Current slug: <code>' . esc_html($current_slug) . '</code></p>';
    echo '<p>Standardized slug: <code>' . esc_html($standardized_slug) . '</code></p>';
    
    // Check if the slug needs to be updated
    if ($current_slug !== $standardized_slug) {
        // Update the term with the standardized slug
        $result = wp_update_term(
            $neighborhood->term_id,
            'spa_neighborhood',
            array(
                'slug' => $standardized_slug
            )
        );
        
        if (is_wp_error($result)) {
            echo '<p style="color: red;">Error updating slug: ' . $result->get_error_message() . '</p>';
        } else {
            echo '<p style="color: green;">Updated slug successfully!</p>';
        }
    } else {
        echo '<p>Slug already standardized. No changes needed.</p>';
    }
    
    // Check if this is Vila Olímpica
    if (strpos(strtolower($current_name), 'vila ol') !== false) {
        echo '<p><strong>Vila Olímpica detected!</strong> Ensuring proper name and assignments...</p>';
        
        // Ensure the name is correct (with accent)
        if ($current_name !== 'Vila Olímpica') {
            $result = wp_update_term(
                $neighborhood->term_id,
                'spa_neighborhood',
                array(
                    'name' => 'Vila Olímpica',
                    'slug' => 'vila-olimpica' // Standardized slug without accent
                )
            );
            
            if (is_wp_error($result)) {
                echo '<p style="color: red;">Error updating name: ' . $result->get_error_message() . '</p>';
            } else {
                echo '<p style="color: green;">Updated name to "Vila Olímpica" successfully!</p>';
            }
        }
        
        // Assign the Mandarin Oriental spa to this neighborhood
        $mandarin = get_page_by_path('the-spa-at-mandarin-oriental-barcelona', OBJECT, 'spa');
        
        if ($mandarin) {
            // Assign the term
            $result = wp_set_object_terms($mandarin->ID, $neighborhood->term_id, 'spa_neighborhood', true);
            
            if (is_wp_error($result)) {
                echo '<p style="color: red;">Error assigning term to Mandarin Oriental: ' . $result->get_error_message() . '</p>';
            } else {
                echo '<p style="color: green;">Successfully assigned Vila Olímpica term to Mandarin Oriental Spa.</p>';
            }
            
            // Update the spa's location meta
            $location = get_post_meta($mandarin->ID, 'location', true);
            
            if (empty($location) || !is_array($location)) {
                $location = array();
            }
            
            $location['neighborhood'] = 'Vila Olímpica';
            $location['district'] = 'Sant Martí';
            
            update_post_meta($mandarin->ID, 'location', $location);
            echo '<p style="color: green;">Updated location metadata for Mandarin Oriental Spa.</p>';
        } else {
            echo '<p style="color: red;">Mandarin Oriental Spa not found!</p>';
        }
    }
    
    echo '</div>';
}

// Check for duplicate slugs
$all_slugs = array();
$duplicates = array();

foreach ($neighborhoods as $neighborhood) {
    $slug = sanitize_title(remove_accents($neighborhood->name));
    
    if (isset($all_slugs[$slug])) {
        $duplicates[$slug][] = $neighborhood->term_id;
        $duplicates[$slug][] = $all_slugs[$slug];
    } else {
        $all_slugs[$slug] = $neighborhood->term_id;
    }
}

if (!empty($duplicates)) {
    echo '<h2 style="color: red;">Duplicate Slugs Detected!</h2>';
    echo '<p>The following neighborhoods would have duplicate slugs after standardization:</p>';
    
    foreach ($duplicates as $slug => $term_ids) {
        echo '<div style="margin-bottom: 15px; padding: 10px; border: 1px solid #f00; background: #fff0f0;">';
        echo '<p><strong>Slug:</strong> ' . esc_html($slug) . '</p>';
        echo '<p><strong>Terms:</strong></p>';
        echo '<ul>';
        
        foreach ($term_ids as $term_id) {
            $term = get_term($term_id, 'spa_neighborhood');
            if ($term && !is_wp_error($term)) {
                echo '<li>ID: ' . $term_id . ' - Name: ' . esc_html($term->name) . '</li>';
            }
        }
        
        echo '</ul>';
        echo '</div>';
    }
}

// Flush rewrite rules
flush_rewrite_rules();
echo '<p style="color: green;">Flushed rewrite rules.</p>';

echo '</div>';

// Check Vila Olímpica specifically
$vila_olimpica = get_term_by('slug', 'vila-olimpica', 'spa_neighborhood');

if ($vila_olimpica) {
    echo '<h2>Vila Olímpica Status</h2>';
    echo '<div style="background: #d1ffd1; padding: 15px; margin: 15px 0; border: 1px solid #00a000;">';
    echo '<p><strong>Term ID:</strong> ' . $vila_olimpica->term_id . '</p>';
    echo '<p><strong>Name:</strong> ' . esc_html($vila_olimpica->name) . '</p>';
    echo '<p><strong>Slug:</strong> ' . esc_html($vila_olimpica->slug) . '</p>';
    echo '<p><strong>Count:</strong> ' . $vila_olimpica->count . '</p>';
    
    // Get the term link
    $term_link = get_term_link($vila_olimpica);
    echo '<p><strong>Term Link:</strong> <a href="' . esc_url($term_link) . '" target="_blank">' . esc_html($term_link) . '</a></p>';
    
    // Get spas in this neighborhood
    $spas = get_posts(array(
        'post_type' => 'spa',
        'posts_per_page' => -1,
        'tax_query' => array(
            array(
                'taxonomy' => 'spa_neighborhood',
                'field' => 'term_id',
                'terms' => $vila_olimpica->term_id,
            ),
        ),
    ));
    
    echo '<h3>Spas in Vila Olímpica (' . count($spas) . ')</h3>';
    
    if (!empty($spas)) {
        echo '<ul>';
        foreach ($spas as $spa) {
            echo '<li><a href="' . get_permalink($spa->ID) . '" target="_blank">' . esc_html($spa->post_title) . '</a> (ID: ' . $spa->ID . ')</li>';
        }
        echo '</ul>';
    } else {
        echo '<p>No spas found in this neighborhood.</p>';
        
        // Force assign the Mandarin Oriental spa again
        $mandarin = get_page_by_path('the-spa-at-mandarin-oriental-barcelona', OBJECT, 'spa');
        
        if ($mandarin) {
            echo '<h4>Force Assigning Mandarin Oriental Spa</h4>';
            
            // Assign the term directly using the database
            global $wpdb;
            
            // First check if the relationship already exists
            $existing = $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(*) FROM {$wpdb->term_relationships} 
                WHERE object_id = %d AND term_taxonomy_id = %d",
                $mandarin->ID,
                $vila_olimpica->term_taxonomy_id
            ));
            
            if ($existing) {
                echo '<p>Relationship already exists in the database but not showing up. Refreshing...</p>';
                
                // Delete and recreate
                $wpdb->delete(
                    $wpdb->term_relationships,
                    array(
                        'object_id' => $mandarin->ID,
                        'term_taxonomy_id' => $vila_olimpica->term_taxonomy_id
                    )
                );
            }
            
            // Insert the relationship
            $wpdb->insert(
                $wpdb->term_relationships,
                array(
                    'object_id' => $mandarin->ID,
                    'term_taxonomy_id' => $vila_olimpica->term_taxonomy_id,
                    'term_order' => 0
                )
            );
            
            // Update the term count
            wp_update_term_count_now(array($vila_olimpica->term_taxonomy_id), 'spa_neighborhood');
            
            echo '<p style="color: green;">Forced term assignment through database. Term count updated.</p>';
            
            // Clear object cache
            clean_object_term_cache($mandarin->ID, 'spa');
            clean_term_cache($vila_olimpica->term_id, 'spa_neighborhood');
            
            echo '<p style="color: green;">Cleared cache.</p>';
        }
    }
    
    echo '</div>';
} else {
    echo '<div style="background: #ffd1d1; padding: 15px; margin: 15px 0; border: 1px solid #a00000;">';
    echo '<p>Vila Olímpica term not found with slug "vila-olimpica"!</p>';
    echo '</div>';
}

// Add links for testing
echo '<h2>Test Links</h2>';
echo '<p><a href="' . esc_url(home_url('/spa-neighborhood/vila-olimpica/')) . '" target="_blank">Test Vila Olímpica Neighborhood Page</a></p>';
echo '<p><a href="' . esc_url(home_url('/wp-content/themes/spasinbarcelona/debug-neighborhoods.php')) . '">Go to Neighborhood Debug</a></p>';
echo '<p><a href="' . esc_url(home_url('/wp-content/themes/spasinbarcelona/update-permalinks.php')) . '">Update Permalinks</a></p>';
