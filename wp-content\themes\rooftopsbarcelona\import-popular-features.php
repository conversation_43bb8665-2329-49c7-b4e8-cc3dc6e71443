<?php
/**
 * <PERSON><PERSON>t to import the hardcoded popular features from the homepage into the database
 */

// Load WordPress
require_once( dirname( __FILE__ ) . '/../../wp-load.php' );

// Define the popular features to import
$popular_features = array(
    array('name' => 'Luxury Experience', 'icon' => 'fa-gem', 'count' => 8),
    array('name' => 'Couples Retreat', 'icon' => 'fa-heart', 'count' => 10),
    array('name' => 'Panoramic Views', 'icon' => 'fa-mountain', 'count' => 6),
    array('name' => 'Organic Products', 'icon' => 'fa-leaf', 'count' => 7),
    array('name' => 'Private Sessions', 'icon' => 'fa-lock', 'count' => 12),
    array('name' => 'Family Friendly', 'icon' => 'fa-users', 'count' => 5),
    array('name' => 'Special Offers', 'icon' => 'fa-percent', 'count' => 9),
    array('name' => 'Relaxation Focus', 'icon' => 'fa-couch', 'count' => 15),
    array('name' => 'Holistic Treatments', 'icon' => 'fa-hands', 'count' => 11),
    array('name' => 'Eco-Friendly', 'icon' => 'fa-seedling', 'count' => 7)
);

// Get all spa posts
$spa_posts = get_posts(array(
    'post_type' => 'spa',
    'posts_per_page' => -1,
    'post_status' => 'publish',
));

if (empty($spa_posts)) {
    echo "<p>No spa posts found. Please import spa data first.</p>";
    exit;
}

echo "<h1>Importing Popular Features</h1>";

// Create the tags and assign them to random spas
foreach ($popular_features as $feature) {
    $name = $feature['name'];
    $count = $feature['count'];
    $icon = $feature['icon'];
    
    // Create the tag if it doesn't exist
    $tag = get_term_by('name', $name, 'post_tag');
    
    if (!$tag) {
        $result = wp_insert_term($name, 'post_tag');
        
        if (is_wp_error($result)) {
            echo "<p>Error creating tag '{$name}': " . $result->get_error_message() . "</p>";
            continue;
        }
        
        $tag_id = $result['term_id'];
        echo "<p>Created tag: {$name} (ID: {$tag_id})</p>";
        
        // Save the icon as term meta
        update_term_meta($tag_id, 'icon', $icon);
    } else {
        $tag_id = $tag->term_id;
        echo "<p>Tag already exists: {$name} (ID: {$tag_id})</p>";
        
        // Update the icon
        update_term_meta($tag_id, 'icon', $icon);
    }
    
    // Assign this tag to random spas
    $random_spas = array_rand($spa_posts, min($count, count($spa_posts)));
    
    if (!is_array($random_spas)) {
        $random_spas = array($random_spas);
    }
    
    foreach ($random_spas as $spa_index) {
        $spa = $spa_posts[$spa_index];
        
        // Get current tags
        $current_tags = wp_get_post_terms($spa->ID, 'post_tag', array('fields' => 'ids'));
        
        // Add the new tag if it's not already assigned
        if (!in_array($tag_id, $current_tags)) {
            $current_tags[] = $tag_id;
            wp_set_post_terms($spa->ID, $current_tags, 'post_tag');
            echo "<p>Assigned tag '{$name}' to spa: {$spa->post_title}</p>";
        }
    }
}

echo "<h2>Import Complete!</h2>";
echo "<p>All popular features have been imported and assigned to spa posts.</p>";
echo "<p><a href='" . home_url('/spa-popular/') . "'>View Popular Features Page</a></p>";
