/**
 * Spa Neighborhood styles for Spas in Barcelona theme
 */

/* Homepage Neighborhood Cards - matching amenities cards */
.spa-neighborhoods {
    padding: 80px 0;
    background-color: #f5f5f0;
    position: relative;
    overflow: hidden;
    box-shadow: none;
}

/* Add a subtle pattern overlay to the neighborhoods section */
.spa-neighborhoods:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('data:image/svg+xml;utf8,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><circle cx="30" cy="30" r="20" fill="none" stroke="rgba(210, 180, 140, 0.05)" stroke-width="1"/></svg>');
    opacity: 0.5;
    pointer-events: none;
}

.spa-neighborhoods-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 30px;
    margin-top: 40px;
    margin-bottom: 30px;
}

.spa-neighborhood-card-wrapper {
    height: 100%;
    width: 100%;
}

.spa-neighborhood-card {
    background-color: #fff;
    border-radius: 12px;
    box-shadow: var(--box-shadow);
    padding: 35px 25px;
    text-align: center;
    transition: var(--transition);
    display: flex;
    flex-direction: column;
    align-items: center;
    color: var(--dark-color);
    border: 1px solid rgba(210, 180, 140, 0.2);
    text-decoration: none;
    height: 100%;
    width: 100%;
}

.spa-neighborhood-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(104, 160, 160, 0.15);
    color: var(--dark-color);
    border-color: var(--primary-color);
}

.spa-neighborhood-icon {
    width: 70px;
    height: 70px;
    background-color: var(--primary-color);
    color: #fff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    margin-bottom: 20px;
    transition: var(--transition);
}

.spa-neighborhood-card:hover .spa-neighborhood-icon {
    background-color: var(--secondary-color);
    transform: scale(1.1);
}

.spa-neighborhood-icon i {
    color: #fff;
    font-size: 1.8rem;
}

.spa-neighborhood-title {
    font-size: 1.2rem;
    margin-bottom: 10px;
    font-family: var(--font-secondary);
    font-weight: 600;
    color: var(--dark-color);
}

.spa-neighborhood-count {
    color: #666;
    font-size: 0.9rem;
}

@media (max-width: 992px) {
    .spa-neighborhoods-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 768px) {
    .spa-neighborhoods-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .spa-neighborhoods-grid {
        grid-template-columns: 1fr;
    }
}

/* Neighborhood page styles */
.neighborhoods-container {
    margin-top: 30px;
}

.district-section {
    margin-bottom: 40px;
    padding: 30px;
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
}

.district-section h2 {
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
    color: #3a4a4a;
    font-size: 1.8rem;
}

.neighborhoods-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
}

.neighborhood-card {
    background: #fff;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.neighborhood-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
}

.neighborhood-card h3 {
    padding: 15px 15px 5px;
    margin: 0;
    font-size: 1.2rem;
}

.neighborhood-card .spa-count {
    padding: 0 15px 15px;
    margin: 0;
    color: #666;
    font-size: 0.9rem;
}

.neighborhood-image {
    height: 150px;
    overflow: hidden;
}

.neighborhood-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.neighborhood-card:hover .neighborhood-image img {
    transform: scale(1.05);
}

.view-spas-button {
    display: block;
    text-align: center;
    padding: 10px 15px;
    background: #f5f5f0;
    color: #333;
    text-decoration: none;
    font-weight: 500;
    transition: background 0.3s ease;
    margin-top: auto;
}

.view-spas-button:hover {
    background: #e0e0d8;
}

/* Neighborhood children list */
.neighborhood-children {
    margin-top: 30px;
    padding: 20px;
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
}

.neighborhood-children h2 {
    margin-bottom: 15px;
    color: #3a4a4a;
}

.neighborhood-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 10px;
    list-style-type: none;
    padding: 0;
    margin: 0;
}

.neighborhood-list li {
    padding: 10px;
    background-color: #f5f5f0;
    border-radius: 8px;
    transition: background-color 0.3s ease;
}

.neighborhood-list li:hover {
    background-color: #e0e0d8;
}

.neighborhood-list a {
    text-decoration: none;
    color: #3a4a4a;
    font-weight: 500;
}

/* Neighborhood info section */
.neighborhood-info {
    margin-top: 30px;
    padding: 20px;
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
}

.neighborhood-info h2 {
    margin-bottom: 15px;
    color: #3a4a4a;
}

.neighborhood-description {
    line-height: 1.6;
}

/* Spa neighborhood links on single spa page */
.spa-neighborhood-links {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
}

.spa-neighborhood-link,
.spa-district-link {
    display: inline-block;
    padding: 3px 8px;
    background-color: #f5f5f0;
    border-radius: 4px;
    text-decoration: none;
    color: #3a4a4a;
    font-size: 0.9em;
    transition: background-color 0.3s ease, color 0.3s ease;
}

.spa-neighborhood-link:hover,
.spa-district-link:hover {
    background-color: #3a4a4a;
    color: #fff;
}

.spa-district-link {
    background-color: #e0e0d8;
}

/* Neighborhood info in spa description */
.spa-neighborhood-info {
    margin: 25px 0;
    padding: 20px;
    background-color: #f9f9f7;
    border-radius: 12px;
    border-left: 4px solid #68a0a0;
}

/* Neighborhood taxonomy page styles */
.neighborhood-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    margin: 30px 0;
}

.neighborhood-card {
    background-color: #fff;
    border-radius: 12px;
    box-shadow: var(--box-shadow);
    padding: 25px;
    text-align: center;
    transition: var(--transition);
    display: flex;
    flex-direction: column;
    align-items: center;
    color: var(--dark-color);
    border: 1px solid rgba(210, 180, 140, 0.2);
    text-decoration: none;
}

.neighborhood-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(104, 160, 160, 0.15);
    color: var(--dark-color);
    border-color: var(--primary-color);
}

.neighborhood-card-title {
    font-size: 1.2rem;
    margin-bottom: 10px;
    font-family: var(--font-secondary);
    font-weight: 600;
    color: var(--dark-color);
}

.neighborhood-card-count {
    color: #666;
    font-size: 0.9rem;
}

@media (max-width: 768px) {
    .neighborhood-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .neighborhood-grid {
        grid-template-columns: 1fr;
    }
}

.spa-neighborhood-title {
    margin-top: 0;
    margin-bottom: 15px;
    color: #3a4a4a;
    font-size: 1.1rem;
}

.spa-neighborhood-title i {
    color: #68a0a0;
    margin-right: 8px;
}

.spa-neighborhood-content {
    margin-bottom: 15px;
}

.spa-neighborhood-badge,
.spa-district-badge {
    display: inline-block;
    padding: 6px 12px;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    margin-right: 5px;
}

.spa-neighborhood-badge {
    background-color: #68a0a0;
    color: #ffffff;
}

.spa-district-badge {
    background-color: #d2b48c;
    color: #3a4a4a;
}

.spa-neighborhood-badge:hover {
    background-color: #5a8e8e;
    color: #ffffff;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.spa-district-badge:hover {
    background-color: #c4a77e;
    color: #3a4a4a;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.spa-neighborhood-description {
    margin-top: 15px;
    font-style: italic;
    color: #666;
    font-size: 0.95em;
    line-height: 1.6;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .neighborhoods-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }

    .neighborhood-list {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }
}

@media (max-width: 480px) {
    .neighborhoods-grid {
        grid-template-columns: 1fr;
    }

    .neighborhood-list {
        grid-template-columns: 1fr;
    }
}
