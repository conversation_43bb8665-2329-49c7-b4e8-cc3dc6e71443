/**
 * Custom CSS fixes for Spas in Barcelona theme
 */

/* Fix for spa card images to remove padding/spacing */
.spa-card {
    overflow: hidden !important;
    padding: 0 !important;
    border-radius: 12px !important;
}

.spa-card-inner {
    width: 100% !important;
    padding: 0 !important;
    margin: 0 !important;
}

.spa-card-image {
    padding: 0 !important;
    margin: 0 !important;
    border-radius: 12px 12px 0 0 !important;
    overflow: hidden !important;
    width: 100% !important;
    max-width: 100% !important;
    display: block !important;
    font-size: 0 !important;
    line-height: 0 !important;
}

.spa-card-image a {
    display: block !important;
    padding: 0 !important;
    margin: 0 !important;
    height: 100% !important;
    width: 100% !important;
    position: relative !important;
    border-radius: 12px 12px 0 0 !important;
    overflow: hidden !important;
    font-size: 0 !important;
    line-height: 0 !important;
}

.spa-card-image img {
    display: block !important;
    width: 100% !important;
    height: 100% !important;
    padding: 0 !important;
    margin: 0 !important;
    object-fit: cover !important;
    object-position: center !important;
    border-radius: 12px 12px 0 0 !important;
    max-width: none !important;
    font-size: 0 !important;
    line-height: 0 !important;
}

/* Target any potential wrapper elements */
.spa-card * {
    box-sizing: border-box !important;
}

.spa-card-image * {
    padding: 0 !important;
    margin: 0 !important;
}

/* Ensure the rating badge is positioned correctly */
.spa-rating-badge {
    position: absolute !important;
    bottom: 15px !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    z-index: 10 !important;
    background: rgba(0, 0, 0, 0.7) !important;
    color: #ffffff !important;
    padding: 14px 22px !important; /* Further increased padding for taller badge */
    border-radius: 30px !important;
    font-weight: 700 !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5) !important;
    text-align: center !important;
    min-width: auto !important;
    backdrop-filter: blur(10px) !important;
    border: 2px solid rgba(255, 255, 255, 0.8) !important;
    transition: all 0.3s ease !important;
}

/* Additional fixes for any potential parent containers */
article.spa-card {
    padding: 0 !important;
    overflow: hidden !important;
}

/* Fix for white background on spa pages */
body.single-spa-page,
body.single-spa-page #page,
body.single-spa-page .site,
body.single-spa-page .site-content,
body.single-spa-page #primary,
body.single-spa-page .content-area,
body.single-spa-page .site-main,
body.single-spa-page article,
body.single-spa-page .inside-article {
    background-color: #f5f5f0 !important;
}

/* Target GeneratePress specific elements that might be causing issues */
.inside-article {
    padding: 0 !important;
}

.post-image-above-header .inside-article .post-image {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
}

/* Target WordPress core image styles */
.wp-post-image {
    margin: 0 !important;
    padding: 0 !important;
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
}

/* Target any figure elements that might be wrapping images */
figure {
    margin: 0 !important;
    padding: 0 !important;
}

/* Target any potential image containers */
.spa-card .image-container,
.spa-card .featured-image,
.spa-card .post-image {
    margin: 0 !important;
    padding: 0 !important;
    width: 100% !important;
    height: auto !important;
    overflow: hidden !important;
    border-radius: 12px 12px 0 0 !important;
}

/* GeneratePress specific fixes */
.generate-columns-container article .inside-article {
    padding: 0 !important;
}

.generate-columns-container article .inside-article img {
    margin: 0 !important;
    padding: 0 !important;
}

/* Fix for any potential whitespace issues */
.spa-card-image::before,
.spa-card-image::after {
    display: none !important;
    content: none !important;
}

.spa-card-image a::before,
.spa-card-image a::after {
    display: none !important;
    content: none !important;
}

/* Fix for any potential inline elements causing whitespace */
.spa-card-image,
.spa-card-image a,
.spa-card-image img {
    font-size: 0 !important;
    line-height: 0 !important;
    letter-spacing: 0 !important;
    word-spacing: 0 !important;
}

/* Fix for neighborhood cards on homepage - matching amenities cards */
.spa-neighborhoods {
    padding: 80px 0 !important;
    background-color: #f5f5f0 !important;
    position: relative !important;
    overflow: hidden !important;
    box-shadow: none !important;
}

/* Add a subtle pattern overlay to the neighborhoods section */
.spa-neighborhoods:before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    background-image: url('data:image/svg+xml;utf8,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><circle cx="30" cy="30" r="20" fill="none" stroke="rgba(210, 180, 140, 0.05)" stroke-width="1"/></svg>') !important;
    opacity: 0.5 !important;
    pointer-events: none !important;
}

.spa-neighborhoods-grid {
    display: grid !important;
    grid-template-columns: repeat(4, 1fr) !important;
    gap: 30px !important;
    margin-top: 40px !important;
    margin-bottom: 30px !important;
}

.spa-neighborhood-card-wrapper {
    height: 100% !important;
    width: 100% !important;
}

.spa-neighborhood-card {
    background-color: #fff !important;
    border-radius: 12px !important;
    box-shadow: var(--box-shadow) !important;
    padding: 35px 25px !important;
    text-align: center !important;
    transition: var(--transition) !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    color: var(--dark-color) !important;
    border: 1px solid rgba(210, 180, 140, 0.2) !important;
    text-decoration: none !important;
    height: 100% !important;
    width: 100% !important;
}

.spa-neighborhood-card:hover {
    transform: translateY(-5px) !important;
    box-shadow: 0 15px 30px rgba(104, 160, 160, 0.15) !important;
    color: var(--dark-color) !important;
    border-color: var(--primary-color) !important;
}

.spa-neighborhood-icon {
    width: 70px !important;
    height: 70px !important;
    background-color: var(--primary-color) !important;
    color: #fff !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 1.8rem !important;
    margin-bottom: 20px !important;
    transition: var(--transition) !important;
}

.spa-neighborhood-card:hover .spa-neighborhood-icon {
    background-color: var(--secondary-color) !important;
    transform: scale(1.1) !important;
}

.spa-neighborhood-icon i {
    color: #fff !important;
    font-size: 1.8rem !important;
}

.spa-neighborhood-title {
    font-size: 1.2rem !important;
    margin-bottom: 10px !important;
    font-family: var(--font-secondary) !important;
    font-weight: 600 !important;
    color: var(--dark-color) !important;
}

.spa-neighborhood-count {
    color: #666 !important;
    font-size: 0.9rem !important;
}

@media (max-width: 992px) {
    .spa-neighborhoods-grid {
        grid-template-columns: repeat(3, 1fr) !important;
    }
}

@media (max-width: 768px) {
    .spa-neighborhoods-grid {
        grid-template-columns: repeat(2, 1fr) !important;
    }
}

@media (max-width: 480px) {
    .spa-neighborhoods-grid {
        grid-template-columns: 1fr !important;
    }
}

/* CTA-style button for section footers */
.spa-cta-style-button {
    display: inline-flex !important;
    align-items: center !important;
    padding: 18px 36px !important;
    color: #fff !important;
    border-radius: 50px !important;
    font-weight: 500 !important;
    transition: all 0.4s ease !important;
    letter-spacing: 0.03em !important;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    text-decoration: none !important;
    background-color: var(--primary-color) !important;
}

.spa-cta-style-button:hover {
    color: #fff !important;
    transform: translateY(-3px) !important;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3) !important;
    background-color: var(--secondary-color) !important;
    border-color: var(--secondary-color) !important;
}

.spa-cta-style-button i {
    margin-left: 8px !important;
}
