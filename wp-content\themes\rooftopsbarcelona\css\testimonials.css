/* Testimonials Section Styling */
.spa-testimonials-section {
    padding: 40px 0;
    background-color: var(--light-color);
    position: relative;
    overflow: hidden;
}

.spa-testimonials-section::before {
    content: '\201C'; /* Opening quote mark */
    font-family: 'Playfair Display', serif;
    font-size: 200px;
    position: absolute;
    top: -40px;
    left: 20px;
    color: rgba(104, 160, 160, 0.1);
    z-index: 0;
}

.spa-testimonials-section::after {
    content: '\201D'; /* Closing quote mark */
    font-family: 'Playfair Display', serif;
    font-size: 200px;
    position: absolute;
    bottom: -120px;
    right: 20px;
    color: rgba(104, 160, 160, 0.1);
    z-index: 0;
}

.spa-testimonials-title {
    text-align: center;
    margin-bottom: 40px;
    font-family: 'Playfair Display', serif;
    font-size: 2.2rem;
    color: var(--dark-color);
    position: relative;
    z-index: 1;
}

.spa-testimonials-title::after {
    content: '';
    display: block;
    width: 80px;
    height: 3px;
    background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
    margin: 15px auto 0;
    border-radius: 2px;
}

.spa-testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 30px;
    position: relative;
    z-index: 1;
}

.spa-testimonial-card {
    background-color: #ffffff;
    border-radius: 12px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.spa-testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(104, 160, 160, 0.1);
}

.spa-testimonial-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 5px;
    height: 100%;
    background: linear-gradient(to bottom, var(--primary-color), var(--secondary-color));
}

.spa-testimonial-content {
    font-size: 1.1rem;
    line-height: 1.7;
    color: var(--text-color);
    margin-bottom: 25px;
    flex-grow: 1;
    position: relative;
    font-style: italic;
}

.spa-testimonial-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-top: 1px solid rgba(104, 160, 160, 0.1);
    padding-top: 20px;
}

.spa-testimonial-author {
    display: flex;
    align-items: center;
}

.spa-testimonial-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: var(--light-color);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    color: var(--primary-color);
    font-size: 1.5rem;
}

.spa-testimonial-author-info {
    display: flex;
    flex-direction: column;
}

.spa-testimonial-author-name {
    font-weight: 600;
    color: var(--dark-color);
    font-size: 1.1rem;
}

.spa-testimonial-date {
    font-size: 0.9rem;
    color: var(--contrast-2);
}

.spa-testimonial-rating {
    display: flex;
    align-items: center;
}

.spa-testimonial-stars {
    color: #FFD700;
    font-size: 1.2rem;
    letter-spacing: 2px;
    margin-right: 5px;
}

.spa-testimonial-source {
    font-size: 0.9rem;
    color: var(--contrast-2);
    font-style: italic;
}

@media (max-width: 768px) {
    .spa-testimonials-grid {
        grid-template-columns: 1fr;
    }
    
    .spa-testimonial-card {
        padding: 25px;
    }
    
    .spa-testimonial-content {
        font-size: 1rem;
    }
}
