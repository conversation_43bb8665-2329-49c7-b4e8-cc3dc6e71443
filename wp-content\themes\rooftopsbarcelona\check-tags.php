<?php
/**
 * A simple script to check if there are any tags assigned to spa posts
 */

// Load WordPress
require_once( dirname( __FILE__ ) . '/../../wp-load.php' );

// Get all tags used by spas
global $wpdb;

// Get tags that are actually used by spa posts
$spa_tags = $wpdb->get_results(
    "SELECT DISTINCT t.term_id, t.name, t.slug, COUNT(tr.object_id) as count
    FROM {$wpdb->terms} t
    INNER JOIN {$wpdb->term_taxonomy} tt ON t.term_id = tt.term_id
    INNER JOIN {$wpdb->term_relationships} tr ON tr.term_taxonomy_id = tt.term_taxonomy_id
    INNER JOIN {$wpdb->posts} p ON p.ID = tr.object_id
    WHERE tt.taxonomy = 'post_tag'
    AND p.post_type = 'spa'
    AND p.post_status = 'publish'
    GROUP BY t.term_id
    ORDER BY count DESC, t.name ASC"
);

echo '<h2>Tags assigned to spa posts:</h2>';

if (!empty($spa_tags) && !is_wp_error($spa_tags)) {
    echo '<ul>';
    foreach ($spa_tags as $tag) {
        echo '<li>' . esc_html($tag->name) . ' (' . esc_html($tag->count) . ' spas)</li>';
    }
    echo '</ul>';
} else {
    echo '<p>No tags found for spa posts.</p>';
    
    // Check if there are any spa posts
    $spa_count = wp_count_posts('spa');
    echo '<p>Number of published spa posts: ' . $spa_count->publish . '</p>';
    
    // Check if there are any tags at all
    $all_tags = get_terms(array(
        'taxonomy' => 'post_tag',
        'hide_empty' => false,
    ));
    
    if (!empty($all_tags) && !is_wp_error($all_tags)) {
        echo '<p>Total number of tags (including unused): ' . count($all_tags) . '</p>';
        echo '<ul>';
        foreach ($all_tags as $tag) {
            echo '<li>' . esc_html($tag->name) . ' (' . esc_html($tag->count) . ' posts)</li>';
        }
        echo '</ul>';
    } else {
        echo '<p>No tags found at all.</p>';
    }
}
