<?php
/**
 * The template for displaying 404 pages (not found)
 *
 * @link https://codex.wordpress.org/Creating_an_Error_404_Page
 *
 * @package SpaInBarcelona
 */

get_header();
?>

	<div id="primary" class="content-area">
		<main id="main" class="site-main">

			<section class="error-404 not-found" style="text-align: center; padding: 40px 20px;">
				<header class="page-header">
					<h1 class="page-title" style="font-size: 2.5em; margin-bottom: 20px;"><?php esc_html_e( 'Oops! That page can&rsquo;t be found.', 'spasinbarcelona' ); ?></h1>
				</header><!-- .page-header -->

				<div class="page-content">
					<p style="font-size: 1.2em; margin-bottom: 30px;"><?php esc_html_e( 'It looks like nothing was found. Maybe try one of the links below or a search?', 'spasinbarcelona' ); ?></p>

					<?php // get_search_form(); ?> <!-- Optionally include a search form -->
				</div><!-- .page-content -->
			</section><!-- .error-404 -->

			<section class="suggested-spas" style="padding: 40px 0;">
				<div class="spa-container"> <!-- Using the same container class as front-page -->
					<div class="spa-section-header" style="text-align: center; margin-bottom: 30px;">
						<h2 class="spa-section-title"><?php esc_html_e( 'Maybe one of these spas?', 'spasinbarcelona' ); ?></h2>
					</div>

					<div class="spa-grid"> <!-- This class is used on the front page for the spa cards grid -->
						<?php
						$args = array(
							'post_type'      => 'spa',
							'posts_per_page' => 3,
							'orderby'        => 'date', // Get recent spas
							'order'          => 'DESC',
						);

						$suggested_query = new WP_Query( $args );

						if ( $suggested_query->have_posts() ) :
							while ( $suggested_query->have_posts() ) :
								$suggested_query->the_post();
								get_template_part( 'template-parts/content', 'spa-card' );
							endwhile;
							wp_reset_postdata();
						else :
							echo '<p style="text-align:center;">' . esc_html__( 'No spas found to suggest.', 'spasinbarcelona' ) . '</p>';
						endif;
						?>
					</div> <!-- .spa-grid -->
				</div> <!-- .spa-container -->
			</section> <!-- .suggested-spas -->



			<!-- Navigation Buttons -->
			<div class="spa-navigation-buttons">
				<a href="<?php echo esc_url( get_post_type_archive_link( 'spa' ) ); ?>" class="spa-button">
					View All Spas <i class="fas fa-arrow-right"></i>
				</a>
				<a href="<?php echo esc_url( home_url() ); ?>" class="spa-button">
					<i class="fas fa-home"></i> Back To Homepage
				</a>
			</div>

		</main><!-- #main -->
	</div><!-- #primary -->

<?php
get_footer();
