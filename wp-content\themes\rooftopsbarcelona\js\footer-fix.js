/**
 * Footer fix script for Spas in Barcelona theme
 */
(function() {
    // Function to apply footer styles
    function applyFooterStyles() {
        // Get the footer element
        var footer = document.querySelector('.site-footer');

        if (footer) {
            // Apply styles to footer
            footer.style.backgroundColor = '#3a4a4a';
            footer.style.color = '#ffffff';
            footer.style.padding = '30px 0';

            // Apply styles to footer bottom
            var footerBottom = footer.querySelector('.footer-bottom');
            if (footerBottom) {
                footerBottom.style.display = 'flex';
                footerBottom.style.justifyContent = 'space-between';
                footerBottom.style.alignItems = 'center';
                footerBottom.style.flexWrap = 'wrap';
            }

            // Apply styles to footer copyright
            var footerCopyright = footer.querySelector('.footer-copyright');
            if (footerCopyright) {
                footerCopyright.style.color = '#ffffff';
                footerCopyright.style.marginBottom = '10px';
            }

            // Apply styles to footer menu
            var footerMenu = footer.querySelector('.footer-menu');
            if (footerMenu) {
                footerMenu.style.display = 'flex';
                footerMenu.style.flexWrap = 'wrap';
                footerMenu.style.listStyle = 'none';
                footerMenu.style.padding = '0';
                footerMenu.style.margin = '0';

                var menuItems = footerMenu.querySelectorAll('li');
                menuItems.forEach(function(item) {
                    item.style.margin = '0 15px 10px 0';

                    var link = item.querySelector('a');
                    if (link) {
                        link.style.color = '#ffffff';
                        link.style.textDecoration = 'none';
                        link.style.fontFamily = '"Poppins", sans-serif';
                        link.style.fontSize = '14px';
                        link.style.transition = 'all 0.3s ease';
                        link.style.padding = '5px 0';
                        link.style.position = 'relative';

                        // Add hover effect
                        link.addEventListener('mouseenter', function() {
                            this.style.color = '#68a0a0';
                        });
                        link.addEventListener('mouseleave', function() {
                            this.style.color = '#ffffff';
                        });
                    }
                });

                // Apply responsive styles
                if (window.innerWidth <= 768) {
                    if (footerBottom) {
                        footerBottom.style.flexDirection = 'column';
                        footerBottom.style.alignItems = 'center';
                        footerBottom.style.textAlign = 'center';
                    }

                    footerMenu.style.justifyContent = 'center';
                    footerMenu.style.marginTop = '15px';

                    menuItems.forEach(function(item) {
                        item.style.margin = '0 10px 10px';
                    });
                }
            }
        }
    }

    // Apply styles when the DOM is fully loaded
    document.addEventListener('DOMContentLoaded', applyFooterStyles);

    // Also apply styles now in case the DOM is already loaded
    if (document.readyState === 'complete' || document.readyState === 'interactive') {
        setTimeout(applyFooterStyles, 1);
    }

    // Handle back to top button
    var backToTop = document.querySelector('.back-to-top');
    if (backToTop) {
        // Function to show/hide the back to top button
        function toggleBackToTop() {
            if (window.pageYOffset > 300) {
                backToTop.style.opacity = '1';
                backToTop.style.visibility = 'visible';
            } else {
                backToTop.style.opacity = '0';
                backToTop.style.visibility = 'hidden';
            }
        }

        // Initial check
        toggleBackToTop();

        // Add scroll event listener
        window.addEventListener('scroll', toggleBackToTop);

        // Add click event listener
        backToTop.addEventListener('click', function(e) {
            e.preventDefault();
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });

        // Add hover effect
        backToTop.addEventListener('mouseenter', function() {
            this.style.backgroundColor = '#d2b48c';
            this.style.transform = 'translateY(-5px)';
        });

        backToTop.addEventListener('mouseleave', function() {
            this.style.backgroundColor = '#68a0a0';
            this.style.transform = 'translateY(0)';
        });
    }
})();
