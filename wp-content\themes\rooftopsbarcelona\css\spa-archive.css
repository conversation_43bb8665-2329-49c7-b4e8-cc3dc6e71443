/**
 * Spa archive page styles for Spas in Barcelona theme
 */

/* Archive Header */
.page-header {
    text-align: center;
    margin-bottom: 50px;
    padding: 60px 0;
    background-color: var(--light-color);
    border-bottom: 1px solid var(--border-color);
    position: relative;
    overflow: hidden;
}

/* Add a subtle pattern overlay to the header */
.page-header:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('data:image/svg+xml;utf8,<svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"><path d="M0 20 L20 0 L40 20 L20 40 Z" fill="none" stroke="rgba(104, 160, 160, 0.05)" stroke-width="1"/></svg>');
    opacity: 0.5;
    pointer-events: none;
}

.page-title {
    font-size: 2.8rem;
    margin-bottom: 18px;
    color: var(--dark-color);
    letter-spacing: 0.02em;
    position: relative;
    display: inline-block;
}

/* Add a decorative underline to the title */
.page-title:after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
    border-radius: 3px;
}

.archive-description {
    max-width: 800px;
    margin: 25px auto 0;
    color: #777;
    font-size: 1.1rem;
    line-height: 1.8;
    letter-spacing: 0.01em;
}

/* Spa Results */
.spa-results {
    min-height: 300px;
    position: relative;
    transition: opacity 0.3s ease;
    margin-top: 0; /* Ensure no extra top margin */
}

.spa-results.loading {
    opacity: 0.5;
}

/* Spa Grid */
.spa-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;
    margin: 30px 0;
}

@media (max-width: 768px) {
    .spa-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 20px;
    }
}

@media (max-width: 576px) {
    .spa-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
}

.spa-loading {
    display: none;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 10;
    background-color: rgba(255, 255, 255, 0.8);
    padding: 15px 30px;
    border-radius: 50px;
    box-shadow: var(--box-shadow);
    font-weight: 500;
}

.spa-loading i {
    margin-right: 10px;
    color: var(--primary-color);
}

/* No Results */
.no-results {
    text-align: center;
    padding: 60px 0;
    background-color: var(--light-color);
    border-radius: 12px;
    margin: 30px 0;
    border: 1px solid rgba(210, 180, 140, 0.2);
    box-shadow: var(--box-shadow);
}

.no-results p {
    font-size: 1.1rem;
    color: #777;
    margin-bottom: 20px;
    line-height: 1.7;
    letter-spacing: 0.01em;
}

/* Pagination */
.spa-pagination {
    display: flex;
    justify-content: center;
    margin: 40px 0;
}

.spa-pagination a,
.spa-pagination span {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 40px;
    height: 40px;
    padding: 0 12px;
    margin: 0 5px;
    border-radius: 20px;
    background-color: #fff;
    color: var(--dark-color);
    font-weight: 500;
    transition: var(--transition);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(210, 180, 140, 0.1);
}

.spa-pagination a:hover {
    background-color: var(--secondary-color);
    color: #fff;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
}

.spa-pagination .current {
    background-color: var(--primary-color);
    color: #fff;
    box-shadow: 0 4px 8px rgba(104, 160, 160, 0.2);
}

.spa-pagination .prev,
.spa-pagination .next {
    padding: 0 15px;
}

/* Filter Styles */
.spa-filter-container {
    background-color: #fff;
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 40px;
    box-shadow: var(--box-shadow);
    border: 1px solid rgba(210, 180, 140, 0.2);
}

/* Ensure consistent spacing between filter and cards */
.spa-filter-sidebar .spa-filter-container {
    padding: 25px;
}

.spa-filter-form {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.spa-filter-row {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

/* Adjust filter form for sidebar layout */
.spa-filter-sidebar .spa-filter-form {
    gap: 20px;
}

.spa-filter-sidebar .spa-filter-field {
    width: 100%;
    margin-bottom: 5px;
}

.spa-filter-field {
    flex: 1;
    min-width: 200px;
}

.spa-filter-field label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--dark-color);
    letter-spacing: 0.01em;
}

.spa-filter-select,
.spa-filter-field input[type="text"] {
    width: 100%;
    padding: 12px 18px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background-color: var(--light-color);
    color: var(--dark-color);
    font-family: var(--font-primary);
    transition: var(--transition);
}

.spa-filter-select:focus,
.spa-filter-field input[type="text"]:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(104, 160, 160, 0.1);
    outline: none;
}

.spa-filter-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12px 24px;
    background-color: #6ba5a5; /* Teal color from the image */
    color: #fff;
    border: none;
    border-radius: 30px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    letter-spacing: 0.02em;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
    width: 100%;
}

.spa-clear-filter-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12px 24px;
    background-color: #6ba5a5; /* Same teal color as filter button */
    color: #fff; /* White text for better readability */
    border: none;
    border-radius: 30px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    letter-spacing: 0.02em;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
    width: 100%;
}

.spa-clear-filter-button:hover {
    background-color: #d2b48c; /* Gold color for hover */
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.08);
}

/* Adjust filter button for sidebar layout */
.spa-filter-sidebar .spa-filter-button {
    width: 100%;
    margin-top: 10px;
}

.spa-filter-button:hover {
    background-color: #d2b48c; /* Gold color for hover */
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.08);
}

.spa-filter-button i,
.spa-clear-filter-button i {
    margin-right: 8px;
}

.spa-submit-field {
    display: flex;
    align-items: flex-end;
    flex-direction: column;
    gap: 10px;
}

.spa-search-field {
    flex: 2;
}

.spa-search-field input[type="text"] {
    padding-left: 35px;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="%23666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line></svg>');
    background-repeat: no-repeat;
    background-position: 10px center;
}

/* Two-Column Layout Styles */
.spa-archive-layout {
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
    margin-bottom: 40px;
    align-items: flex-start; /* Align items at the top */
}

.spa-filter-sidebar {
    flex: 0 0 280px;
}

.spa-content-area {
    flex: 1;
    min-width: 0; /* Prevents flex items from overflowing */
}

/* Adjust filter container for sidebar */
.spa-filter-sidebar .spa-filter-container {
    margin-bottom: 0;
    margin-top: 0; /* Ensure no extra top margin */
    position: sticky;
    top: 30px;
}

/* Remove any extra spacing from the content area */
.spa-content-area {
    margin-top: 0;
    padding-top: 0;
}

/* Adjust grid for the new layout */
.spa-content-area .spa-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    margin-top: 0; /* Remove top margin to align with filter */
}

/* Ensure the spa cards align with the filter container */
.spa-content-area .spa-card {
    margin-top: 0;
    margin-bottom: 0; /* Override the margin-bottom from main.css */
}

.spa-content-area .spa-grid {
    margin-bottom: 30px; /* Add margin to the grid instead */
}

/* Responsive Styles */
@media (max-width: 992px) {
    .spa-archive-layout {
        flex-direction: column;
    }

    .spa-filter-sidebar {
        flex: 0 0 100%;
        width: 100%;
    }

    .spa-filter-sidebar .spa-filter-container {
        position: static;
        margin-bottom: 30px;
    }
}

@media (max-width: 768px) {
    .page-header {
        padding: 30px 0;
    }

    .page-title {
        font-size: 2rem;
    }

    .archive-description {
        font-size: 1rem;
    }

    .spa-filter-field {
        min-width: 100%;
    }

    .spa-submit-field {
        width: 100%;
    }

    .spa-filter-button {
        width: 100%;
    }
}

@media (max-width: 576px) {
    .page-header {
        padding: 20px 0;
    }

    .page-title {
        font-size: 1.8rem;
    }

    .spa-pagination a,
    .spa-pagination span {
        min-width: 35px;
        height: 35px;
        margin: 0 3px;
        font-size: 0.9rem;
    }
}
