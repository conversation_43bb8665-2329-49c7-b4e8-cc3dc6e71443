<?php
/**
 * Update Permalinks
 * 
 * This script updates the permalink structure to ensure trailing slashes
 * and flushes rewrite rules for proper URL handling
 */

// Load WordPress
require_once( dirname( __FILE__ ) . '/../../wp-load.php' );

// Security check - only administrators can access this page
if (!current_user_can('administrator')) {
    wp_die('You do not have permission to access this page.');
}

echo '<h1>Update Permalinks</h1>';

// Get the current permalink structure
$current_structure = get_option('permalink_structure');

echo '<div style="background: #e0f0ff; padding: 15px; margin: 15px 0; border: 1px solid #0073aa;">';
echo '<p><strong>Current Permalink Structure:</strong> ' . esc_html($current_structure) . '</p>';

// Check if it already has a trailing slash
if (substr($current_structure, -1) !== '/') {
    // Add trailing slash
    $new_structure = $current_structure . '/';
    
    // Update the permalink structure
    update_option('permalink_structure', $new_structure);
    
    echo '<p><strong>Updated Permalink Structure:</strong> ' . esc_html($new_structure) . '</p>';
    echo '<p>Permalink structure has been updated to include trailing slashes.</p>';
} else {
    echo '<p>Permalink structure already includes trailing slashes. No changes needed.</p>';
}

// Flush rewrite rules
flush_rewrite_rules();
echo '<p>Rewrite rules have been flushed successfully.</p>';
echo '</div>';

// Check taxonomy rewrite rules
global $wp_rewrite;
$rules = $wp_rewrite->rewrite_rules();

echo '<h2>Taxonomy Rewrite Rules</h2>';
echo '<p>Showing rules related to spa taxonomies:</p>';

echo '<table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">';
echo '<tr style="background: #f0f0f0;">';
echo '<th style="border: 1px solid #ccc; padding: 8px; text-align: left;">Pattern</th>';
echo '<th style="border: 1px solid #ccc; padding: 8px; text-align: left;">Replacement</th>';
echo '</tr>';

$found = false;
$taxonomies = array('spa-neighborhood', 'spa-services', 'spa-amenities', 'spa-popular', 'spa-feature', 'spa-service', 'spa-category');

foreach ($rules as $pattern => $replacement) {
    foreach ($taxonomies as $taxonomy) {
        if (strpos($pattern, $taxonomy) !== false || strpos($replacement, str_replace('-', '_', $taxonomy)) !== false) {
            $found = true;
            echo '<tr>';
            echo '<td style="border: 1px solid #ccc; padding: 8px;">' . $pattern . '</td>';
            echo '<td style="border: 1px solid #ccc; padding: 8px;">' . $replacement . '</td>';
            echo '</tr>';
            break;
        }
    }
}

if (!$found) {
    echo '<tr><td colspan="2" style="border: 1px solid #ccc; padding: 8px;">No spa taxonomy rules found!</td></tr>';
}

echo '</table>';

// Add links for testing
echo '<h2>Test Links</h2>';
echo '<p>Use these links to test the URL standardization:</p>';
echo '<ul>';
echo '<li><a href="' . esc_url(home_url('/spa-neighborhood/vila-olimpica/')) . '" target="_blank">Vila Olímpica (with trailing slash)</a></li>';
echo '<li><a href="' . esc_url(home_url('/spa-neighborhood/vila-olimpica')) . '" target="_blank">Vila Olímpica (without trailing slash)</a></li>';
echo '<li><a href="' . esc_url(home_url('/spa-neighborhood/gothic-quarter/')) . '" target="_blank">Gothic Quarter (with trailing slash)</a></li>';
echo '<li><a href="' . esc_url(home_url('/spa-neighborhood/gothic-quarter')) . '" target="_blank">Gothic Quarter (without trailing slash)</a></li>';
echo '</ul>';

// Add a link back to the debug page
echo '<p><a href="' . esc_url(home_url('/wp-content/themes/spasinbarcelona/debug-neighborhoods.php')) . '">Back to Neighborhood Debug</a></p>';
