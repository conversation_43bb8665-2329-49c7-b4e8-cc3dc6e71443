<?php
/**
 * <PERSON><PERSON><PERSON> to import specialties from JSON files and assign them to spas
 */

// Load WordPress
require_once( dirname( __FILE__ ) . '/../../../wp-load.php' );

// Get all JSON files in the uploads directory
$directory = WP_CONTENT_DIR . '/uploads/2025/05';
$files = glob($directory . '/*.json');

if (empty($files)) {
    echo "<p>No JSON files found in the specified directory.</p>";
    exit;
}

echo "<h1>Importing Specialties and Services from JSON Files</h1>";

// Debug: Show the first JSON file structure
$first_file = reset($files);
if ($first_file) {
    $json_content = file_get_contents($first_file);
    $spa_data = json_decode($json_content, true);

    if (json_last_error() === JSON_ERROR_NONE) {
        echo "<h2>Sample JSON Structure from " . basename($first_file) . "</h2>";
        echo "<pre>";

        // Show specialties if they exist
        if (!empty($spa_data['specialties'])) {
            echo "\nSpecialties:\n";
            print_r($spa_data['specialties']);
        } else {
            echo "\nNo 'specialties' key found in this JSON file.\n";
        }

        // Show services if they exist
        if (!empty($spa_data['services'])) {
            echo "\nServices:\n";
            print_r($spa_data['services']);
        } else {
            echo "\nNo 'services' key found in this JSON file.\n";
        }

        // Show popular if they exist
        if (!empty($spa_data['popular'])) {
            echo "\nPopular:\n";
            print_r($spa_data['popular']);
        } else {
            echo "\nNo 'popular' key found in this JSON file.\n";
        }

        echo "</pre>";
    }
}

$all_specialties = array();
$spa_specialties = array();
$unique_json_popular_items = array(); // Initialize array for popular items

// First pass: collect all specialties from all JSON files
foreach ($files as $file) {
    $json_content = file_get_contents($file);
    $spa_data = json_decode($json_content, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
        echo "<p>Error decoding JSON file: " . basename($file) . "</p>";
        continue;
    }

    if (empty($spa_data['name'])) {
        echo "<p>Missing name in JSON file: " . basename($file) . "</p>";
        continue;
    }

    $spa_name = $spa_data['name'];
    $spa_slug = sanitize_title($spa_name);

    // Get specialties, services, and popular features from the JSON file
    $combined_features = array(); // Use a new array to combine all features

    // Get specialties
    if (!empty($spa_data['specialties']) && is_array($spa_data['specialties'])) {
        $combined_features = array_merge($combined_features, $spa_data['specialties']);
    }

    // Get services and merge
    if (!empty($spa_data['services']) && is_array($spa_data['services'])) {
        if (isset($spa_data['services'][0]) && is_string($spa_data['services'][0])) {
            $combined_features = array_merge($combined_features, $spa_data['services']);
        } else {
            foreach ($spa_data['services'] as $service_item) {
                if (is_array($service_item) && !empty($service_item['name'])) {
                    $combined_features[] = $service_item['name'];
                } elseif (is_string($service_item)) { // Handle if services can also be simple strings in a mixed array
                    $combined_features[] = $service_item;
                }
            }
        }
    }

    // Get popular features and merge, AND collect them separately
    if (!empty($spa_data['popular']) && is_array($spa_data['popular'])) {
        foreach ($spa_data['popular'] as $pop_item) {
            if (is_string($pop_item)) {
                $combined_features[] = $pop_item; // Add to combined list for general processing
                if (!in_array($pop_item, $unique_json_popular_items)) {
                    $unique_json_popular_items[] = $pop_item; // Add to specific popular list
                }
            }
        }
    }

    // Remove duplicates
    $combined_features = array_unique($combined_features);

    // Store combined features for this spa
    if (!empty($combined_features)) {
        $spa_specialties[$spa_slug] = array(
            'name' => $spa_name,
            'specialties' => $combined_features // This now holds popular, specialties, services
        );

        // Add to the list of all unique features
        foreach ($combined_features as $feature_name) {
            if (!in_array($feature_name, $all_specialties)) {
                $all_specialties[] = $feature_name;
            }
        }
    }
}

// Sort specialties alphabetically
sort($all_specialties);

echo "<h2>Found " . count($all_specialties) . " Unique Specialties</h2>";
echo "<ul>";
foreach ($all_specialties as $specialty) {
    echo "<li>" . esc_html($specialty) . "</li>";
}
echo "</ul>";

// Second pass: create taxonomy terms and assign them to spas
foreach ($all_specialties as $specialty) {
    // Create or get category term
    $category_term = term_exists($specialty, 'spa_category');
    if (!$category_term) {
        $category_term = wp_insert_term($specialty, 'spa_category', array(
            'description' => sprintf('Spas specializing in %s', $specialty),
        ));
    }

    if (is_wp_error($category_term)) {
        echo "<p>Error creating category term for '{$specialty}': " . $category_term->get_error_message() . "</p>";
        continue;
    }

    $category_id = $category_term['term_id'];

    // Create or get service term
    $service_term = term_exists($specialty, 'spa_service');
    if (!$service_term) {
        $service_term = wp_insert_term($specialty, 'spa_service', array(
            'description' => sprintf('Spas offering %s service', $specialty),
        ));
    }

    if (is_wp_error($service_term)) {
        echo "<p>Error creating service term for '{$specialty}': " . $service_term->get_error_message() . "</p>";
        continue;
    }

    $service_id = $service_term['term_id'];

    echo "<h3>Created/Updated Terms for: " . esc_html($specialty) . "</h3>";
    echo "<p>Category ID: " . $category_id . "</p>";
    echo "<p>Service ID: " . $service_id . "</p>";

    // Find spas with this specialty and assign the terms
    foreach ($spa_specialties as $spa_slug => $spa_data) {
        if (in_array($specialty, $spa_data['specialties'])) {
            // Get the spa post
            $spa_post = get_page_by_path($spa_slug, OBJECT, 'spa');

            if ($spa_post) {
                // Assign category term
                $result = wp_set_object_terms($spa_post->ID, $category_id, 'spa_category', true);
                if (is_wp_error($result)) {
                    echo "<p>Error assigning category '{$specialty}' to spa '{$spa_data['name']}': " . $result->get_error_message() . "</p>";
                }

                // Assign service term
                $result = wp_set_object_terms($spa_post->ID, $service_id, 'spa_service', true);
                if (is_wp_error($result)) {
                    echo "<p>Error assigning service '{$specialty}' to spa '{$spa_data['name']}': " . $result->get_error_message() . "</p>";
                }

                echo "<p>Assigned '{$specialty}' to spa: " . esc_html($spa_data['name']) . "</p>";
            } else {
                echo "<p>Spa not found: " . esc_html($spa_data['name']) . " (slug: {$spa_slug})</p>";
            }
        }
    }
}

// Save the unique popular items list as a WordPress option
update_option('actual_popular_features_from_json', $unique_json_popular_items);
echo "<h3>Updated 'actual_popular_features_from_json' option with " . count($unique_json_popular_items) . " items.</h3>";

echo "<h2>Import Complete!</h2>";
echo "<p>All specialties have been imported and assigned to spa posts.</p>";
echo "<p><a href='" . home_url('/spa-services/') . "'>View Spa Services Page</a></p>";
echo "<p><a href='" . home_url() . "'>Return to Homepage</a></p>";
