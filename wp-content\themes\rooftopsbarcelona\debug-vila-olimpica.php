<?php
/**
 * Debug Vila Olímpica
 * 
 * This file helps diagnose issues with the Vila Olímpica neighborhood
 */

// Load WordPress
require_once( dirname( __FILE__ ) . '/../../wp-load.php' );

// Security check - only administrators can access this page
if (!current_user_can('administrator')) {
    wp_die('You do not have permission to access this page.');
}

echo '<h1>Vila Olímpica Debug</h1>';

// Check for the term with different variations
$variations = array(
    'Vila Olímpica' => 'name',
    'Vila Olimpica' => 'name',
    'vila-olimpica' => 'slug',
    'vila-olímpica' => 'slug',
);

echo '<h2>Term Variations</h2>';
echo '<table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">';
echo '<tr style="background: #f0f0f0;">';
echo '<th style="border: 1px solid #ccc; padding: 8px; text-align: left;">Variation</th>';
echo '<th style="border: 1px solid #ccc; padding: 8px; text-align: left;">Field</th>';
echo '<th style="border: 1px solid #ccc; padding: 8px; text-align: left;">Found</th>';
echo '<th style="border: 1px solid #ccc; padding: 8px; text-align: left;">Term ID</th>';
echo '<th style="border: 1px solid #ccc; padding: 8px; text-align: left;">Name</th>';
echo '<th style="border: 1px solid #ccc; padding: 8px; text-align: left;">Slug</th>';
echo '<th style="border: 1px solid #ccc; padding: 8px; text-align: left;">Count</th>';
echo '</tr>';

foreach ($variations as $variation => $field) {
    $term = get_term_by($field, $variation, 'spa_neighborhood');
    
    echo '<tr>';
    echo '<td style="border: 1px solid #ccc; padding: 8px;">' . esc_html($variation) . '</td>';
    echo '<td style="border: 1px solid #ccc; padding: 8px;">' . esc_html($field) . '</td>';
    
    if ($term && !is_wp_error($term)) {
        echo '<td style="border: 1px solid #ccc; padding: 8px; background: #d1ffd1;">Yes</td>';
        echo '<td style="border: 1px solid #ccc; padding: 8px;">' . $term->term_id . '</td>';
        echo '<td style="border: 1px solid #ccc; padding: 8px;">' . esc_html($term->name) . '</td>';
        echo '<td style="border: 1px solid #ccc; padding: 8px;">' . esc_html($term->slug) . '</td>';
        echo '<td style="border: 1px solid #ccc; padding: 8px;">' . $term->count . '</td>';
    } else {
        echo '<td style="border: 1px solid #ccc; padding: 8px; background: #ffd1d1;">No</td>';
        echo '<td style="border: 1px solid #ccc; padding: 8px;" colspan="4">Not found</td>';
    }
    
    echo '</tr>';
}

echo '</table>';

// Get the actual term we'll use
$vila_olimpica = get_term_by('slug', 'vila-olimpica', 'spa_neighborhood');
if (!$vila_olimpica) {
    $vila_olimpica = get_term_by('name', 'Vila Olímpica', 'spa_neighborhood');
}

if ($vila_olimpica && !is_wp_error($vila_olimpica)) {
    echo '<h2>Vila Olímpica Term Details</h2>';
    echo '<div style="background: #e0f0ff; padding: 15px; margin: 15px 0; border: 1px solid #0073aa;">';
    echo '<p><strong>Term ID:</strong> ' . $vila_olimpica->term_id . '</p>';
    echo '<p><strong>Name:</strong> ' . esc_html($vila_olimpica->name) . '</p>';
    echo '<p><strong>Slug:</strong> ' . esc_html($vila_olimpica->slug) . '</p>';
    echo '<p><strong>Description:</strong> ' . esc_html($vila_olimpica->description) . '</p>';
    echo '<p><strong>Count:</strong> ' . $vila_olimpica->count . '</p>';
    echo '<p><strong>Taxonomy:</strong> ' . $vila_olimpica->taxonomy . '</p>';
    echo '<p><strong>Parent:</strong> ' . $vila_olimpica->parent . '</p>';
    
    // Get the term link
    $term_link = get_term_link($vila_olimpica);
    echo '<p><strong>Term Link:</strong> <a href="' . esc_url($term_link) . '" target="_blank">' . esc_html($term_link) . '</a></p>';
    
    // Get spas in this neighborhood
    $spas = get_posts(array(
        'post_type' => 'spa',
        'posts_per_page' => -1,
        'tax_query' => array(
            array(
                'taxonomy' => 'spa_neighborhood',
                'field' => 'term_id',
                'terms' => $vila_olimpica->term_id,
            ),
        ),
    ));
    
    echo '<h3>Spas in Vila Olímpica (' . count($spas) . ')</h3>';
    
    if (!empty($spas)) {
        echo '<ul>';
        foreach ($spas as $spa) {
            echo '<li><a href="' . get_permalink($spa->ID) . '" target="_blank">' . esc_html($spa->post_title) . '</a> (ID: ' . $spa->ID . ')</li>';
        }
        echo '</ul>';
    } else {
        echo '<p>No spas found in this neighborhood.</p>';
    }
    
    echo '</div>';
    
    // Check for the Mandarin Oriental spa specifically
    $mandarin = get_page_by_path('the-spa-at-mandarin-oriental-barcelona', OBJECT, 'spa');
    
    if ($mandarin) {
        echo '<h2>Mandarin Oriental Spa</h2>';
        echo '<div style="background: #e0f0ff; padding: 15px; margin: 15px 0; border: 1px solid #0073aa;">';
        echo '<p><strong>ID:</strong> ' . $mandarin->ID . '</p>';
        echo '<p><strong>Title:</strong> ' . esc_html($mandarin->post_title) . '</p>';
        
        // Get the spa's neighborhoods
        $spa_neighborhoods = wp_get_object_terms($mandarin->ID, 'spa_neighborhood');
        
        echo '<h3>Assigned Neighborhoods</h3>';
        
        if (!empty($spa_neighborhoods) && !is_wp_error($spa_neighborhoods)) {
            echo '<ul>';
            foreach ($spa_neighborhoods as $n) {
                echo '<li>' . esc_html($n->name) . ' (ID: ' . $n->term_id . ', Slug: ' . $n->slug . ')</li>';
            }
            echo '</ul>';
        } else {
            echo '<p>No neighborhoods assigned to this spa.</p>';
        }
        
        // Get the spa's location meta
        $location = get_post_meta($mandarin->ID, 'location', true);
        
        echo '<h3>Location Meta</h3>';
        
        if (!empty($location)) {
            echo '<pre>' . print_r($location, true) . '</pre>';
        } else {
            echo '<p>No location meta found.</p>';
        }
        
        echo '</div>';
    } else {
        echo '<div style="background: #ffd1d1; padding: 10px; margin: 10px 0; border: 1px solid #a00000;">';
        echo '<p>Mandarin Oriental Spa not found!</p>';
        echo '</div>';
    }
} else {
    echo '<div style="background: #ffd1d1; padding: 10px; margin: 10px 0; border: 1px solid #a00000;">';
    echo '<p>Vila Olímpica term not found!</p>';
    echo '</div>';
}

// Add a button to fix the Vila Olímpica term
echo '<div style="margin: 20px 0;">';
echo '<a href="' . esc_url(home_url('/wp-content/themes/spasinbarcelona/fix-vila-olimpica.php')) . '" style="display: inline-block; padding: 10px 15px; background: #0073aa; color: #fff; text-decoration: none; border-radius: 3px;">Fix Vila Olímpica</a>';
echo '</div>';

// Add a button to flush rewrite rules
echo '<div style="margin: 20px 0;">';
echo '<a href="' . esc_url(home_url('/wp-content/themes/spasinbarcelona/debug-neighborhoods.php?flush=1')) . '" style="display: inline-block; padding: 10px 15px; background: #0073aa; color: #fff; text-decoration: none; border-radius: 3px;">Flush Rewrite Rules</a>';
echo '</div>';

// Add links for testing
echo '<p><a href="' . esc_url(home_url('/spa-neighborhood/vila-olimpica/')) . '" target="_blank">Test Vila Olímpica Neighborhood Page (with trailing slash)</a></p>';
echo '<p><a href="' . esc_url(home_url('/spa-neighborhood/vila-olimpica')) . '" target="_blank">Test Vila Olímpica Neighborhood Page (without trailing slash)</a></p>';
