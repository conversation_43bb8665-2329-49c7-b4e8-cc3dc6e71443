<?php
/**
 * Template Name: Spa Neighborhoods Template
 *
 * This template is used to display spas by neighborhood
 */

get_header();

// Enhanced debugging for administrators
if (current_user_can('administrator')) {
    echo '<!-- ===== SPA NEIGHBORHOOD PAGE TEMPLATE DEBUG START ===== -->';
    echo '<!-- Current URL: ' . esc_url($_SERVER['REQUEST_URI']) . ' -->';

    // Check query vars
    $neighborhood_slug = get_query_var('neighborhood_slug');
    echo '<!-- Query Var neighborhood_slug: ' . (!empty($neighborhood_slug) ? esc_html($neighborhood_slug) : 'Not set') . ' -->';

    // Check URL parts
    $current_url = $_SERVER['REQUEST_URI'];
    $url_parts = explode('/', trim($current_url, '/'));
    $url_slug = end($url_parts);
    echo '<!-- URL Slug from path: ' . esc_html($url_slug) . ' -->';

    echo '<!-- ===== SPA NEIGHBORHOOD PAGE TEMPLATE DEBUG END ===== -->';
}

// Get the neighborhood slug from query var
$neighborhood_slug = get_query_var('neighborhood_slug');

// If no query var, try to get from URL
if (empty($neighborhood_slug)) {
    $current_url = $_SERVER['REQUEST_URI'];
    $url_parts = explode('/', trim($current_url, '/'));
    $neighborhood_slug = end($url_parts);

    // If the term slug is empty (due to trailing slash), get the previous part
    if (empty($neighborhood_slug)) {
        // Remove the last element (empty string)
        array_pop($url_parts);
        // Get the new last element
        $neighborhood_slug = end($url_parts);
    }

    // If the last part is the page name, there's no slug
    if ($neighborhood_slug === 'spa-neighborhood') {
        $neighborhood_slug = '';
    }
}

// Get the term object if we have a slug
$neighborhood = !empty($neighborhood_slug) ? get_term_by('slug', $neighborhood_slug, 'spa_neighborhood') : null;

// If we have a neighborhood, redirect to the taxonomy URL
if ($neighborhood && !is_wp_error($neighborhood)) {
    $taxonomy_url = get_term_link($neighborhood);
    if (!is_wp_error($taxonomy_url)) {
        // Only redirect if we're not already on the taxonomy page
        if (strpos($current_url, '/spa-neighborhood/' . $neighborhood_slug) !== false &&
            strpos($current_url, '/spa-neighborhood/' . $neighborhood_slug . '/') === false) {
            wp_redirect($taxonomy_url);
            exit;
        }
    }
}
?>

<div id="primary" class="content-area">
    <main id="main" class="site-main">
        <header class="page-header">
            <div class="page-title-wrapper">
                <?php
                if ($neighborhood) {
                    echo '<h1 class="page-title">Best Spas in ' . esc_html($neighborhood->name) . ' in Barcelona</h1>';
                } else {
                    echo '<h1 class="page-title">Best Spas by Neighborhood in Barcelona</h1>';
                }
                ?>
            </div>
            <div class="archive-description">
                <?php
                if ($neighborhood) {
                    // Check if there's a custom description in term meta
                    $seo_description = get_term_meta($neighborhood->term_id, 'seo_description', true);
                    if (!empty($seo_description)) {
                        echo '<p>' . esc_html($seo_description) . '</p>';
                    } else {
                        // Get parent term (district) if it exists
                        $parent_term = null;
                        if ($neighborhood->parent) {
                            $parent_term = get_term($neighborhood->parent, 'spa_neighborhood');
                        }

                        if ($parent_term && !is_wp_error($parent_term)) {
                            echo '<p>Discover the best spas in the ' . esc_html($neighborhood->name) . ' neighborhood of ' . esc_html($parent_term->name) . 'in Barcelona. Browse our selection of luxury spas and wellness centers in this area.</p>';
                        } else {
                            echo '<p>Discover the best spas in the ' . esc_html($neighborhood->name) . ' area of Barcelona. Browse our selection of luxury spas and wellness centers in this neighborhood.</p>';
                        }
                    }
                } else {
                    echo '<p>Explore Barcelona\'s spas by neighborhood. Find the perfect spa experience in your preferred area of the city.</p>';
                }
                ?>
            </div>
        </header>

        <div class="spa-results" style="width: 100%;">
            <?php
            if ($neighborhood) {
                // Display spas in the selected neighborhood
                $args = get_query_var('spa_neighborhood_query_args');

                if (empty($args)) {
                    // Create a custom query to get spas with this neighborhood
                    $args = array(
                        'post_type' => 'spa',
                        'posts_per_page' => -1,
                        'tax_query' => array(
                            array(
                                'taxonomy' => 'spa_neighborhood',
                                'field' => 'slug',
                                'terms' => $neighborhood_slug,
                            ),
                        ),
                    );
                }

                $spa_query = new WP_Query($args);

                if ($spa_query->have_posts()) :
                ?>
                    <div class="spa-grid">
                        <?php
                        while ($spa_query->have_posts()) :
                            $spa_query->the_post();
                            get_template_part('template-parts/content', 'spa-card');
                        endwhile;
                        wp_reset_postdata();
                        ?>
                    </div>
                <?php else : ?>
                    <div class="no-results">
                        <p><?php esc_html_e('No spas found in this neighborhood.', 'spasinbarcelona'); ?></p>
                    </div>
                <?php endif;
            } else {
                // Display all neighborhoods
                // First get districts (parent terms)
                $districts = get_terms(array(
                    'taxonomy' => 'spa_neighborhood',
                    'parent' => 0,
                    'hide_empty' => true,
                ));

                if (!empty($districts) && !is_wp_error($districts)) {
                    echo '<div class="neighborhoods-container">';

                    foreach ($districts as $district) {
                        echo '<div class="district-section">';
                        echo '<h2>' . esc_html($district->name) . ' District</h2>';

                        // Get neighborhoods in this district
                        $neighborhoods = get_terms(array(
                            'taxonomy' => 'spa_neighborhood',
                            'parent' => $district->term_id,
                            'hide_empty' => true,
                        ));

                        if (!empty($neighborhoods) && !is_wp_error($neighborhoods)) {
                            echo '<div class="neighborhoods-grid">';

                            foreach ($neighborhoods as $neighborhood) {
                                $neighborhood_link = get_term_link($neighborhood);
                                $spa_count = $neighborhood->count;

                                echo '<div class="neighborhood-card">';
                                echo '<h3><a href="' . esc_url($neighborhood_link) . '">' . esc_html($neighborhood->name) . '</a></h3>';
                                echo '<p class="spa-count">' . $spa_count . ' ' . _n('spa', 'spas', $spa_count, 'spasinbarcelona') . '</p>';

                                // Get a sample spa from this neighborhood
                                $sample_spa = get_posts(array(
                                    'post_type' => 'spa',
                                    'posts_per_page' => 1,
                                    'tax_query' => array(
                                        array(
                                            'taxonomy' => 'spa_neighborhood',
                                            'field' => 'term_id',
                                            'terms' => $neighborhood->term_id,
                                        ),
                                    ),
                                ));

                                if (!empty($sample_spa)) {
                                    $sample_spa = $sample_spa[0];
                                    $thumbnail = get_the_post_thumbnail_url($sample_spa->ID, 'medium');

                                    if ($thumbnail) {
                                        echo '<div class="neighborhood-image">';
                                        echo '<a href="' . esc_url($neighborhood_link) . '">';
                                        echo '<img src="' . esc_url($thumbnail) . '" alt="' . esc_attr($neighborhood->name) . '">';
                                        echo '</a>';
                                        echo '</div>';
                                    }
                                }

                                echo '<a href="' . esc_url($neighborhood_link) . '" class="view-spas-button">View Spas</a>';
                                echo '</div>';
                            }

                            echo '</div>';
                        } else {
                            // Display the district itself as a card if it has spas but no child neighborhoods
                            $district_link = get_term_link($district);
                            $spa_count = $district->count;

                            echo '<div class="neighborhoods-grid">';
                            echo '<div class="neighborhood-card">';
                            echo '<h3><a href="' . esc_url($district_link) . '">' . esc_html($district->name) . '</a></h3>';
                            echo '<p class="spa-count">' . $spa_count . ' ' . _n('spa', 'spas', $spa_count, 'spasinbarcelona') . '</p>';

                            // Get a sample spa from this district
                            $sample_spa = get_posts(array(
                                'post_type' => 'spa',
                                'posts_per_page' => 1,
                                'tax_query' => array(
                                    array(
                                        'taxonomy' => 'spa_neighborhood',
                                        'field' => 'term_id',
                                        'terms' => $district->term_id,
                                    ),
                                ),
                            ));

                            if (!empty($sample_spa)) {
                                $sample_spa = $sample_spa[0];
                                $thumbnail = get_the_post_thumbnail_url($sample_spa->ID, 'medium');

                                if ($thumbnail) {
                                    echo '<div class="neighborhood-image">';
                                    echo '<a href="' . esc_url($district_link) . '">';
                                    echo '<img src="' . esc_url($thumbnail) . '" alt="' . esc_attr($district->name) . '">';
                                    echo '</a>';
                                    echo '</div>';
                                }
                            }

                            echo '<a href="' . esc_url($district_link) . '" class="view-spas-button">View Spas</a>';
                            echo '</div>';
                            echo '</div>';
                        }

                        echo '</div>';
                    }

                    // Get neighborhoods without a district (parent)
                    $standalone_neighborhoods = get_terms(array(
                        'taxonomy' => 'spa_neighborhood',
                        'parent' => 0,
                        'hide_empty' => true,
                        'exclude' => wp_list_pluck($districts, 'term_id'),
                    ));

                    if (!empty($standalone_neighborhoods) && !is_wp_error($standalone_neighborhoods)) {
                        echo '<div class="district-section">';
                        echo '<h2>Other Areas</h2>';
                        echo '<div class="neighborhoods-grid">';

                        foreach ($standalone_neighborhoods as $neighborhood) {
                            $neighborhood_link = get_term_link($neighborhood);
                            $spa_count = $neighborhood->count;

                            echo '<div class="neighborhood-card">';
                            echo '<h3><a href="' . esc_url($neighborhood_link) . '">' . esc_html($neighborhood->name) . '</a></h3>';
                            echo '<p class="spa-count">' . $spa_count . ' ' . _n('spa', 'spas', $spa_count, 'spasinbarcelona') . '</p>';

                            // Get a sample spa from this neighborhood
                            $sample_spa = get_posts(array(
                                'post_type' => 'spa',
                                'posts_per_page' => 1,
                                'tax_query' => array(
                                    array(
                                        'taxonomy' => 'spa_neighborhood',
                                        'field' => 'term_id',
                                        'terms' => $neighborhood->term_id,
                                    ),
                                ),
                            ));

                            if (!empty($sample_spa)) {
                                $sample_spa = $sample_spa[0];
                                $thumbnail = get_the_post_thumbnail_url($sample_spa->ID, 'medium');

                                if ($thumbnail) {
                                    echo '<div class="neighborhood-image">';
                                    echo '<a href="' . esc_url($neighborhood_link) . '">';
                                    echo '<img src="' . esc_url($thumbnail) . '" alt="' . esc_attr($neighborhood->name) . '">';
                                    echo '</a>';
                                    echo '</div>';
                                }
                            }

                            echo '<a href="' . esc_url($neighborhood_link) . '" class="view-spas-button">View Spas</a>';
                            echo '</div>';
                        }

                        echo '</div>';
                        echo '</div>';
                    }

                    echo '</div>';
                } else {
                    echo '<div class="no-results">';
                    echo '<p>' . esc_html__('No neighborhoods found with spas.', 'spasinbarcelona') . '</p>';
                    echo '</div>';
                }
            }
            ?>
        </div>
    </main>
</div>

<style>
    .neighborhoods-container {
        margin-top: 30px;
    }

    .district-section {
        margin-bottom: 40px;
    }

    .district-section h2 {
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 1px solid #eee;
    }

    .neighborhoods-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 20px;
    }

    .neighborhood-card {
        background: #fff;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .neighborhood-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 16px rgba(0,0,0,0.15);
    }

    .neighborhood-card h3 {
        padding: 15px 15px 5px;
        margin: 0;
        font-size: 1.2rem;
    }

    .neighborhood-card .spa-count {
        padding: 0 15px 15px;
        margin: 0;
        color: #666;
        font-size: 0.9rem;
    }

    .neighborhood-image {
        height: 150px;
        overflow: hidden;
    }

    .neighborhood-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.5s ease;
    }

    .neighborhood-card:hover .neighborhood-image img {
        transform: scale(1.05);
    }

    .view-spas-button {
        display: block;
        text-align: center;
        padding: 10px 15px;
        background: #f5f5f0;
        color: #333;
        text-decoration: none;
        font-weight: 500;
        transition: background 0.3s ease;
    }

    .view-spas-button:hover {
        background: #e0e0d8;
    }
</style>

<?php
get_footer();
