/**
 * Enhanced fix script for footer and CTA section visibility
 */
(function($) {
    // Run on document ready
    $(document).ready(function() {
        fixFooterAndCTA();
    });

    // Also run after window load to ensure all resources are loaded
    $(window).on('load', function() {
        fixFooterAndCTA();
    });

    // Run again after a short delay to catch any dynamic content changes
    setTimeout(function() {
        fixFooterAndCTA();
    }, 500);

    function fixFooterAndCTA() {
        // Ensure CTA section is visible
        $('.spa-cta-wrapper, .cta-section').css({
            'display': 'block',
            'visibility': 'visible',
            'opacity': '1',
            'position': 'relative',
            'z-index': '10'
        });

        // Ensure footer is visible
        $('.site-footer, #colophon').css({
            'display': 'block',
            'visibility': 'visible',
            'opacity': '1',
            'position': 'relative',
            'z-index': '10'
        });

        // Fix any potential container issues
        $('#page').css({
            'overflow': 'visible',
            'position': 'relative'
        });

        // Ensure proper structure
        if ($('.spa-cta-wrapper').length && !$('.spa-cta-wrapper .cta-section').length) {
            // If CTA wrapper exists but doesn't contain the CTA section, fix it
            var $cta = $('.cta-section').detach();
            $('.spa-cta-wrapper').append($cta);
        }

        // Log to console for debugging
        console.log('Enhanced fix script executed - ensuring CTA and footer visibility');
    }
})(jQuery);
