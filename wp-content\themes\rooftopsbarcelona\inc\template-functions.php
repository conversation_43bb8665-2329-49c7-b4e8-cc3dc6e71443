<?php
/**
 * Custom template functions for this theme
 */

/**
 * Add custom classes to the body
 */
function rooftopsbarcelona_body_classes( $classes ) {
    // Add a class if we're on the rooftop directory
    if ( is_post_type_archive( 'rooftop' ) || is_tax( 'rooftop_neighborhood' ) || is_tax( 'rooftop_amenities' ) || is_tax( 'rooftop_services' ) || is_tax( 'rooftop_popular' ) ) {
        $classes[] = 'rooftop-directory';
    }

    // Add a class for single rooftop pages
    if ( is_singular( 'rooftop' ) ) {
        $classes[] = 'single-rooftop-page';
    }

    return $classes;
}
add_filter( 'body_class', 'rooftopsbarcelona_body_classes' );

/**
 * Calculate the weighted average rating from review sources
 *
 * @param array $review_sources Array of review sources with rating and count
 * @return array Contains 'rating' (weighted average) and 'count' (total number of reviews)
 */
function spasinbarcelona_calculate_rating($review_sources) {
    // Initialize variables
    $total_rating = 0;
    $total_count = 0;

    // Check if we have valid review sources
    if (!empty($review_sources) && is_array($review_sources)) {
        // Loop through each review source
        foreach ($review_sources as $source) {
            // Make sure the source has both rating and count
            if (!empty($source['rating']) && !empty($source['count'])) {
                // Add weighted rating (rating * count) to total
                $total_rating += $source['rating'] * $source['count'];
                // Add count to total count
                $total_count += $source['count'];
            }
        }
    }

    // Calculate weighted average if we have reviews
    $avg_rating = 0;
    if ($total_count > 0) {
        $avg_rating = round($total_rating / $total_count, 1);
    }

    // Return both the average rating and the total count
    return array(
        'rating' => $avg_rating,
        'count' => $total_count
    );
}

/**
 * Generate star rating HTML using the star symbol (★)
 */
function spasinbarcelona_star_rating( $rating, $max = 5 ) {
    // For review sources on the spa page, use the badge style
    if (is_singular('spa')) {
        // Check if we're in the featured reviews section
        $backtrace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 10);
        $in_featured_review = false;
        $in_review_source = false;
        $source_name = '';

        foreach ($backtrace as $trace) {
            if (isset($trace['file']) && strpos($trace['file'], 'content-spa-single.php') !== false) {
                // If we're in the featured reviews section of the template
                if (isset($trace['line']) && $trace['line'] > 1200) {
                    $in_featured_review = true;
                    break;
                }

                // Check if we're in the review sources section
                if (isset($trace['line']) && $trace['line'] > 800 && $trace['line'] < 1200) {
                    $in_review_source = true;

                    // Try to get the source name from the current context
                    if (isset($source) && isset($source['source'])) {
                        $source_name = $source['source'];
                    }
                    break;
                }
            }
        }

        if ($in_featured_review) {
            // For featured reviews, use a simple rating display with number and star
            $html = '<div class="spa-rating-badge-stars">';
            $html .= '<strong>' . number_format($rating, 1) . '</strong>';
            $html .= ' <span class="star-icon">★</span>';
            $html .= '</div>';
            return $html;
        }

        if ($in_review_source) {
            // For review sources, use the new badge style with rounded rectangle and yellow star
            $badge_color = '';

            // Set badge color based on source name
            switch (strtolower($source_name)) {
                case 'google':
                case 'google reviews':
                    $badge_color = 'google-badge';
                    break;
                case 'tripadvisor':
                    $badge_color = 'tripadvisor-badge';
                    break;
                case 'yelp':
                    $badge_color = 'yelp-badge';
                    break;
                case 'booking':
                case 'booking.com':
                    $badge_color = 'booking-badge';
                    break;
                case 'trustpilot':
                    $badge_color = 'trustpilot-badge';
                    break;
                default:
                    $badge_color = 'default-badge';
                    break;
            }

            $html = '<div class="spa-rating-badge-new ' . $badge_color . '">';
            $html .= '<span class="rating-number-new">' . number_format($rating, 1) . '</span>';
            $html .= ' <span class="star-icon-new">★</span>';
            $html .= '</div>';
            return $html;
        }

        // For header rating in spa page, use the badge style with white text and yellow star
        $html = '<div class="spa-rating-badge-header">';
        $html .= '<span class="rating-number-header">' . number_format($rating, 1) . '</span>';
        $html .= ' <span class="star-icon-header">★</span>';
        $html .= '</div>';
        return $html;
    }

    // For other places, use the FontAwesome stars
    $html = '<div class="star-rating">';

    // Full stars
    $full_stars = floor($rating);

    // Half stars
    $half_stars = ceil($rating - $full_stars);

    // Empty stars
    $empty_stars = $max - $full_stars - $half_stars;

    // Add full stars
    for ($i = 0; $i < $full_stars; $i++) {
        $html .= '<i class="fas fa-star"></i>';
    }

    // Add half star if needed
    if ($half_stars) {
        $html .= '<i class="fas fa-star-half-alt"></i>';
    }

    // Add empty stars
    for ($i = 0; $i < $empty_stars; $i++) {
        $html .= '<i class="far fa-star"></i>';
    }

    $html .= '</div>';

    return $html;
}

/**
 * Format price display
 */
function spasinbarcelona_format_price( $price ) {
    // Remove any non-numeric characters except decimal point
    $price = preg_replace('/[^0-9.]/', '', $price);

    if ( empty( $price ) ) {
        return '';
    }

    return '€' . number_format( (float) $price, 2 );
}

/**
 * Get spa features as a formatted list
 */
function spasinbarcelona_get_spa_features( $post_id, $limit = 5 ) {
    $features = get_the_terms( $post_id, 'spa_feature' );

    if ( ! $features || is_wp_error( $features ) ) {
        return '';
    }

    $html = '<ul class="spa-features-list">';

    $count = 0;
    foreach ( $features as $feature ) {
        if ( $count >= $limit ) {
            break;
        }

        $html .= '<li><i class="fas fa-check-circle"></i> ' . esc_html( $feature->name ) . '</li>';
        $count++;
    }

    $html .= '</ul>';

    return $html;
}

/**
 * Get related spas based on taxonomy terms
 */
function spasinbarcelona_get_related_spas( $post_id, $limit = 3 ) {
    $terms = get_the_terms( $post_id, 'spa_category' );

    if ( ! $terms || is_wp_error( $terms ) ) {
        return array();
    }

    $term_ids = wp_list_pluck( $terms, 'term_id' );

    $args = array(
        'post_type' => 'spa',
        'posts_per_page' => $limit,
        'post__not_in' => array( $post_id ),
        'tax_query' => array(
            array(
                'taxonomy' => 'spa_category',
                'field' => 'term_id',
                'terms' => $term_ids,
            ),
        ),
    );

    return get_posts( $args );
}

/**
 * Generate schema.org structured data for the spa directory page
 */
function spasinbarcelona_get_directory_schema() {
    // Only generate schema for the spa directory page
    if (!is_post_type_archive('spa') && !is_tax(array('spa_category', 'spa_service', 'spa_feature', 'spa_neighborhood'))) {
        return '';
    }

    global $wp_query;

    // Get page title
    $title = wp_get_document_title();

    // Get page description
    $description = '';
    if (is_post_type_archive('spa')) {
        $description = "Explore our complete directory of spas in Barcelona. Filter by services, features, price range, and more to find your perfect spa experience.";
    } elseif (is_tax()) {
        $term = get_queried_object();
        if (is_tax('spa_category')) {
            $description = "Discover the best " . $term->name . " spas in Barcelona. Find detailed information, services, prices, and reviews.";
        } elseif (is_tax('spa_service')) {
            $description = "Find spas in Barcelona offering " . $term->name . ". Compare prices, read reviews, and book your perfect spa experience.";
        } elseif (is_tax('spa_feature')) {
            $description = "Looking for spas in Barcelona with " . $term->name . "? Browse our comprehensive directory of spas featuring " . $term->name . ".";
        } elseif (is_tax('spa_neighborhood')) {
            // Check if there's a custom description in term meta
            $seo_description = get_term_meta($term->term_id, 'seo_description', true);
            if (!empty($seo_description)) {
                $description = $seo_description;
            } else {
                // Simple neighborhood description
                $description = "Discover the best spas in " . $term->name . "in Barcelona. Find luxury spa experiences, wellness centers, and beauty treatments in this neighborhood.";
            }
        }
    }

    // Get current URL
    $url = get_pagenum_link(1, false);

    // Build the schema
    $schema = array(
        '@context' => 'https://schema.org',
        '@type' => 'CollectionPage',
        'name' => $title,
        'description' => $description,
        'url' => $url,
        'isPartOf' => array(
            '@type' => 'WebSite',
            'name' => get_bloginfo('name'),
            'url' => home_url()
        )
    );

    // Add breadcrumb list schema
    $breadcrumb_items = array();

    // Home page
    $breadcrumb_items[] = array(
        '@type' => 'ListItem',
        'position' => 1,
        'name' => 'Home',
        'item' => home_url()
    );

    // All Spas (if we're on a taxonomy page)
    if (is_tax()) {
        $breadcrumb_items[] = array(
            '@type' => 'ListItem',
            'position' => 2,
            'name' => 'All Spas',
            'item' => get_post_type_archive_link('spa')
        );

        // Current taxonomy term
        $breadcrumb_items[] = array(
            '@type' => 'ListItem',
            'position' => 3,
            'name' => single_term_title('', false),
            'item' => get_term_link(get_queried_object())
        );
    } else {
        // We're on the main spa archive
        $breadcrumb_items[] = array(
            '@type' => 'ListItem',
            'position' => 2,
            'name' => 'All Spas',
            'item' => get_post_type_archive_link('spa')
        );
    }

    // Add breadcrumb schema
    $breadcrumb_schema = array(
        '@type' => 'BreadcrumbList',
        'itemListElement' => $breadcrumb_items
    );

    // Add itemList schema for the spas
    $item_list_elements = array();
    $position = 1;

    if ($wp_query->have_posts()) {
        while ($wp_query->have_posts()) {
            $wp_query->the_post();
            $spa_id = get_the_ID();
            $spa_title = get_the_title();
            $spa_url = get_permalink();

            // Get spa image
            $image_url = '';
            if (has_post_thumbnail()) {
                $image_url = get_the_post_thumbnail_url($spa_id, 'medium');
            } else {
                $images = get_post_meta($spa_id, 'images', true);
                if (!empty($images) && is_array($images) && !empty($images[0]['url'])) {
                    $image_url = esc_url($images[0]['url']);
                }
            }

            // Get spa description
            $spa_description = get_the_excerpt();
            if (empty($spa_description)) {
                $spa_description = wp_trim_words(get_the_content(), 30, '...');
            }

            // Add spa to item list
            $item_list_elements[] = array(
                '@type' => 'ListItem',
                'position' => $position,
                'item' => array(
                    '@type' => 'DaySpa',
                    'name' => $spa_title,
                    'url' => $spa_url,
                    'description' => $spa_description,
                    'image' => $image_url
                )
            );

            $position++;
        }
        wp_reset_postdata();
    }

    // Add itemList schema if we have items
    if (!empty($item_list_elements)) {
        $item_list_schema = array(
            '@context' => 'https://schema.org',
            '@type' => 'ItemList',
            'itemListElement' => $item_list_elements,
            'numberOfItems' => count($item_list_elements)
        );

        // Remove any @context entries from nested objects
        $item_list_schema = spa_remove_nested_context($item_list_schema);
    }

    // Add pagination schema if needed
    if ($wp_query->max_num_pages > 1) {
        $current_page = max(1, get_query_var('paged'));

        if ($current_page > 1) {
            $schema['pagination'] = array(
                '@type' => 'SiteNavigationElement',
                'hasPart' => array(
                    array(
                        '@type' => 'SiteNavigationElement',
                        'name' => 'Previous',
                        'url' => get_pagenum_link($current_page - 1)
                    )
                )
            );
        }

        if ($current_page < $wp_query->max_num_pages) {
            if (!isset($schema['pagination'])) {
                $schema['pagination'] = array(
                    '@type' => 'SiteNavigationElement',
                    'hasPart' => array()
                );
            }

            $schema['pagination']['hasPart'][] = array(
                '@type' => 'SiteNavigationElement',
                'name' => 'Next',
                'url' => get_pagenum_link($current_page + 1)
            );
        }
    }

    // Apply context removal to all schemas
    $schema = spa_remove_nested_context($schema);
    $breadcrumb_schema = spa_remove_nested_context($breadcrumb_schema);

    // Final verification to ensure no nested @context entries remain
    $schema = spa_verify_no_nested_context($schema);
    $breadcrumb_schema = spa_verify_no_nested_context($breadcrumb_schema);

    // Prepare the output
    $output = '<script type="application/ld+json">' . wp_json_encode($schema) . '</script>';

    // Add breadcrumb schema
    $output .= '<script type="application/ld+json">' . wp_json_encode($breadcrumb_schema) . '</script>';

    // Add itemList schema if we have items
    if (!empty($item_list_elements)) {
        // Item list schema already had context removal applied earlier
        $output .= '<script type="application/ld+json">' . wp_json_encode($item_list_schema) . '</script>';
    }

    return $output;
}

/**
 * Generate schema.org structured data for spas
 */
function spasinbarcelona_get_spa_schema( $post_id ) {
    $post = get_post( $post_id );

    if ( ! $post || 'spa' !== $post->post_type ) {
        return '';
    }

    $name = get_the_title( $post_id );

    // Check for custom meta description first
    $meta_description = get_post_meta( $post_id, 'meta_description', true );

    // If no custom meta description, fall back to regular description
    $description = empty( $meta_description ) ? get_post_meta( $post_id, 'description', true ) : $meta_description;

    // If still empty, use excerpt
    if ( empty( $description ) ) {
        $description = get_the_excerpt( $post_id );
    }

    // Get neighborhood information for enhancing the description
    $neighborhood_name = '';
    $neighborhood_terms = get_the_terms($post_id, 'spa_neighborhood');

    if (!empty($neighborhood_terms) && !is_wp_error($neighborhood_terms)) {
        foreach ($neighborhood_terms as $term) {
            // Use the first neighborhood term we find
            if (empty($neighborhood_name)) {
                $neighborhood_name = $term->name;
            }
        }
    }

    // Fallback to meta values if taxonomy terms aren't available
    if (empty($neighborhood_name) && !empty($location['neighborhood'])) {
        $neighborhood_name = $location['neighborhood'];
    }

    // Use only the original description from the JSON file without modifications
    // No neighborhood or "near me" context added

    $url = get_permalink( $post_id );

    // Get location data
    $location = get_post_meta( $post_id, 'location', true );
    $address = '';

    if ( ! empty( $location['address'] ) ) {
        $address = $location['address'];
    }

    $city = ! empty( $location['city'] ) ? $location['city'] : 'Barcelona';
    $country = ! empty( $location['country'] ) ? $location['country'] : 'Spain';
    $neighborhood = ! empty( $location['neighborhood'] ) ? $location['neighborhood'] : '';

    // Get rating data with enhanced processing
    $reviews = get_post_meta( $post_id, 'reviews', true );
    $rating = '';
    $review_count = '';
    $review_schema_items = array();
    $review_sources_data = array();

    if ( ! empty( $reviews['review_sources'] ) && is_array( $reviews['review_sources'] ) ) {
        $total_rating = 0;
        $total_count = 0;
        $source_ratings = array();

        foreach ( $reviews['review_sources'] as $source ) {
            if ( ! empty( $source['rating'] ) && ! empty( $source['count'] ) ) {
                // Store source data for individual source ratings
                $source_name = !empty($source['name']) ? $source['name'] : 'Review Site';
                $source_url = !empty($source['url']) ? $source['url'] : '';

                // Calculate weighted contribution to overall rating
                $total_rating += $source['rating'] * $source['count'];
                $total_count += $source['count'];

                // Store source data for schema
                $review_sources_data[] = array(
                    'name' => $source_name,
                    'url' => $source_url,
                    'rating' => floatval($source['rating']),
                    'count' => intval($source['count'])
                );
            }
        }

        if ( $total_count > 0 ) {
            $rating = round( $total_rating / $total_count, 1 );
            $review_count = $total_count;
        }
    }


    // Get featured reviews for schema markup with enhanced details
    if ( ! empty( $reviews['featured_reviews'] ) && is_array( $reviews['featured_reviews'] ) ) {
        $processed_reviews = array(); // Track processed reviews to avoid duplicates

        foreach ( $reviews['featured_reviews'] as $review ) {
            if ( ! empty( $review['text'] ) && ! empty( $review['author'] ) ) {
                // Create a unique key for this review to detect duplicates
                $review_key = md5($review['author'] . '-' . $review['text']);

                // Skip if we've already processed this review
                if (isset($processed_reviews[$review_key])) {
                    continue;
                }

                // Mark this review as processed
                $processed_reviews[$review_key] = true;

                // Create enhanced review schema
                $review_schema = array(
                    '@type' => 'Review',
                    'reviewBody' => $review['text'],
                    'author' => array(
                        '@type' => 'Person',
                        'name' => $review['author']
                    ),
                    'itemReviewed' => array(
                        '@type' => 'HealthAndBeautyBusiness',
                        'name' => $name,
                        'url' => $url
                    )
                );

                // Add rating with enhanced details
                if ( ! empty( $review['rating'] ) ) {
                    $review_schema['reviewRating'] = array(
                        '@type' => 'Rating',
                        'ratingValue' => floatval($review['rating']),
                        'bestRating' => 5,
                        'worstRating' => 1
                    );
                }

                // Add date with enhanced format handling
                if ( ! empty( $review['date'] ) ) {
                    // Try multiple date formats
                    $date_formats = array('d/m/Y', 'Y-m-d', 'm/d/Y', 'd-m-Y', 'Y/m/d');
                    $date_parsed = false;

                    foreach ($date_formats as $format) {
                        $date_obj = DateTime::createFromFormat($format, $review['date']);
                        if ($date_obj) {
                            $review_schema['datePublished'] = $date_obj->format('Y-m-d');
                            $date_parsed = true;
                            break;
                        }
                    }

                    // If no format matched, try strtotime as a fallback
                    if (!$date_parsed) {
                        $timestamp = strtotime($review['date']);
                        if ($timestamp) {
                            $review_schema['datePublished'] = date('Y-m-d', $timestamp);
                        } else {
                            // If all else fails, use as-is but note it might not be valid
                            $review_schema['datePublished'] = $review['date'];
                        }
                    }
                } else {
                    // If no date provided, use current date
                    $review_schema['datePublished'] = date('Y-m-d');
                }

                // Add source information if available
                if (!empty($review['source'])) {
                    $review_schema['publisher'] = array(
                        '@type' => 'Organization',
                        'name' => $review['source']
                    );

                    // Add source URL if available
                    if (!empty($review['sourceUrl'])) {
                        $review_schema['publisher']['url'] = $review['sourceUrl'];
                        $review_schema['url'] = $review['sourceUrl'];
                    }
                }

                // Add language if available
                if (!empty($review['language'])) {
                    $review_schema['inLanguage'] = $review['language'];
                } else {
                    // Default to Spanish for Barcelona
                    $review_schema['inLanguage'] = 'es';
                }

                $review_schema_items[] = $review_schema;
            }
        }
    }

    // Create individual review source ratings
    if (!empty($review_sources_data)) {
        foreach ($review_sources_data as $source) {
            // Only add sources with meaningful data
            if (!empty($source['name']) && !empty($source['rating']) && !empty($source['count'])) {
                // Instead of adding aggregate ratings as reviews, collect them for the main aggregateRating
                if (!isset($aggregate_ratings)) {
                    $aggregate_ratings = array();
                }

                // Store this source's rating data for later use in the main aggregateRating
                $aggregate_ratings[] = array(
                    'source' => $source['name'],
                    'rating' => $source['rating'],
                    'count' => $source['count']
                );

                // We no longer add aggregate ratings as individual reviews
                // This data is now collected in $aggregate_ratings for use in the main aggregateRating
            }
        }
    }

    // Get contact information
    $contact = get_post_meta( $post_id, 'contact', true );
    $phone = ! empty( $contact['phone'] ) ? $contact['phone'] : '';
    $email = ! empty( $contact['email'] ) ? $contact['email'] : '';
    $website = ! empty( $contact['website'] ) ? $contact['website'] : '';

    // Get opening hours with enhanced handling for different formats
    $hoursSchema = get_post_meta( $post_id, 'hoursSchema', true );
    $hours = get_post_meta( $post_id, 'hours', true );
    $hoursDisplay = get_post_meta( $post_id, 'hoursDisplay', true );
    $opening_hours_spec = array();

    // Define the days of the week in schema.org format
    $day_mapping = array(
        'monday' => 'Monday',
        'tuesday' => 'Tuesday',
        'wednesday' => 'Wednesday',
        'thursday' => 'Thursday',
        'friday' => 'Friday',
        'saturday' => 'Saturday',
        'sunday' => 'Sunday'
    );

    // Track which days have been processed to ensure all days are represented
    $processed_days = array();

    // 1. If hoursSchema is available, use it directly (most accurate format)
    if ( ! empty( $hoursSchema ) && is_array( $hoursSchema ) ) {
        foreach ($hoursSchema as $spec) {
            // Ensure the schema is properly formatted
            if (isset($spec['dayOfWeek']) && isset($spec['opens']) && isset($spec['closes'])) {
                // Add to processed days
                if (is_array($spec['dayOfWeek'])) {
                    foreach ($spec['dayOfWeek'] as $day) {
                        $processed_days[$day] = true;
                    }
                } else {
                    $processed_days[$spec['dayOfWeek']] = true;
                }

                // Add to opening hours specification
                $opening_hours_spec[] = $spec;
            }
        }
    }
    // 2. Otherwise, try to generate from hours data (structured format)
    elseif ( ! empty( $hours ) && is_array( $hours ) ) {
        foreach ( $hours as $day => $time ) {
            if ( isset( $day_mapping[$day] ) ) {
                $day_name = $day_mapping[$day];
                $processed_days[$day_name] = true;

                // Handle new format with open/close/isClosed
                if ( is_array( $time ) ) {
                    // Create a special entry for closed days
                    if ( !empty( $time['isClosed'] ) && $time['isClosed'] ) {
                        $opening_hours_spec[] = array(
                            '@type' => 'OpeningHoursSpecification',
                            'dayOfWeek' => $day_name,
                            'validFrom' => date('Y-m-d'), // Current date
                            'validThrough' => date('Y-m-d', strtotime('+1 year')), // Valid for a year
                            'closes' => '00:00', // Required by schema
                            'opens' => '00:00'   // Required by schema
                        );
                        continue;
                    }

                    // Check if we have both open and close times
                    if ( !empty( $time['open'] ) && !empty( $time['close'] ) ) {
                        // Convert to 24-hour format for schema
                        $opening_time_24h = date( 'H:i', strtotime( $time['open'] ) );
                        $closing_time_24h = date( 'H:i', strtotime( $time['close'] ) );

                        $opening_hours_spec[] = array(
                            '@type' => 'OpeningHoursSpecification',
                            'dayOfWeek' => $day_name,
                            'opens' => $opening_time_24h,
                            'closes' => $closing_time_24h
                        );
                    }
                }
                // Handle old format (string)
                else if ( !empty( $time ) ) {
                    // Handle closed days
                    if ( strtolower( $time ) === 'closed' ) {
                        $opening_hours_spec[] = array(
                            '@type' => 'OpeningHoursSpecification',
                            'dayOfWeek' => $day_name,
                            'validFrom' => date('Y-m-d'), // Current date
                            'validThrough' => date('Y-m-d', strtotime('+1 year')), // Valid for a year
                            'closes' => '00:00', // Required by schema
                            'opens' => '00:00'   // Required by schema
                        );
                        continue;
                    }

                    // Parse opening hours (assuming format like "10:00 AM - 9:00 PM")
                    $time_parts = explode( ' - ', $time );

                    if ( count( $time_parts ) === 2 ) {
                        $opening_time = trim( $time_parts[0] );
                        $closing_time = trim( $time_parts[1] );

                        // Convert to 24-hour format for schema
                        $opening_time_24h = date( 'H:i', strtotime( $opening_time ) );
                        $closing_time_24h = date( 'H:i', strtotime( $closing_time ) );

                        $opening_hours_spec[] = array(
                            '@type' => 'OpeningHoursSpecification',
                            'dayOfWeek' => $day_name,
                            'opens' => $opening_time_24h,
                            'closes' => $closing_time_24h
                        );
                    }
                }
            }
        }
    }
    // 3. Try to parse from hoursDisplay as a last resort
    elseif ( ! empty( $hoursDisplay ) ) {
        // Common patterns for day ranges
        $day_patterns = array(
            'monday' => '/\b(mon|monday)\b/i',
            'tuesday' => '/\b(tue|tues|tuesday)\b/i',
            'wednesday' => '/\b(wed|wednesday)\b/i',
            'thursday' => '/\b(thu|thur|thurs|thursday)\b/i',
            'friday' => '/\b(fri|friday)\b/i',
            'saturday' => '/\b(sat|saturday)\b/i',
            'sunday' => '/\b(sun|sunday)\b/i'
        );

        // Pattern for time ranges like "10:00 AM - 9:00 PM"
        $hours_pattern = '/(\d{1,2}:\d{2}\s*(?:AM|PM))\s*[-–]\s*(\d{1,2}:\d{2}\s*(?:AM|PM))/i';

        // Pattern for day ranges like "Mon-Fri"
        $day_range_pattern = '/\b(mon|monday|tue|tues|tuesday|wed|wednesday|thu|thur|thurs|thursday|fri|friday|sat|saturday|sun|sunday)\s*[-–]\s*(mon|monday|tue|tues|tuesday|wed|wednesday|thu|thur|thurs|thursday|fri|friday|sat|saturday|sun|sunday)\b/i';

        // Split by semicolons if multiple day ranges
        $hours_parts = explode(';', $hoursDisplay);

        foreach ($hours_parts as $part) {
            $part = trim($part);
            if (empty($part)) continue;

            // Check for day range pattern
            if (preg_match($day_range_pattern, $part, $range_matches)) {
                $start_day_abbr = strtolower($range_matches[1]);
                $end_day_abbr = strtolower($range_matches[2]);

                // Map abbreviated days to full day keys
                $day_map = array(
                    'mon' => 'monday', 'monday' => 'monday',
                    'tue' => 'tuesday', 'tues' => 'tuesday', 'tuesday' => 'tuesday',
                    'wed' => 'wednesday', 'wednesday' => 'wednesday',
                    'thu' => 'thursday', 'thur' => 'thursday', 'thurs' => 'thursday', 'thursday' => 'thursday',
                    'fri' => 'friday', 'friday' => 'friday',
                    'sat' => 'saturday', 'saturday' => 'saturday',
                    'sun' => 'sunday', 'sunday' => 'sunday'
                );

                if (isset($day_map[$start_day_abbr]) && isset($day_map[$end_day_abbr])) {
                    $start_day = $day_map[$start_day_abbr];
                    $end_day = $day_map[$end_day_abbr];

                    // Extract hours from this part
                    $hours_found = false;
                    if (preg_match($hours_pattern, $part, $time_matches)) {
                        $opening_time = $time_matches[1];
                        $closing_time = $time_matches[2];
                        $hours_found = true;
                    }

                    if ($hours_found) {
                        // Convert to 24-hour format
                        $opening_time_24h = date('H:i', strtotime($opening_time));
                        $closing_time_24h = date('H:i', strtotime($closing_time));

                        // Get all days in the range
                        $days_in_range = array();
                        $day_order = array_keys($day_mapping);
                        $start_index = array_search($start_day, $day_order);
                        $end_index = array_search($end_day, $day_order);

                        // Handle wrap-around ranges (e.g., Sun-Wed)
                        if ($start_index > $end_index) {
                            $end_index += 7;
                        }

                        for ($i = $start_index; $i <= $end_index; $i++) {
                            $day_index = $i % 7;
                            $current_day = $day_order[$day_index];
                            $days_in_range[] = $day_mapping[$current_day];
                            $processed_days[$day_mapping[$current_day]] = true;
                        }

                        // Add specification for this range
                        $opening_hours_spec[] = array(
                            '@type' => 'OpeningHoursSpecification',
                            'dayOfWeek' => $days_in_range,
                            'opens' => $opening_time_24h,
                            'closes' => $closing_time_24h
                        );
                    }
                }
            }
            // Check for individual days
            else {
                foreach ($day_patterns as $day_key => $pattern) {
                    if (preg_match($pattern, $part)) {
                        $day_name = $day_mapping[$day_key];
                        $processed_days[$day_name] = true;

                        // Extract hours for this day
                        if (preg_match($hours_pattern, $part, $time_matches)) {
                            $opening_time = $time_matches[1];
                            $closing_time = $time_matches[2];

                            // Convert to 24-hour format
                            $opening_time_24h = date('H:i', strtotime($opening_time));
                            $closing_time_24h = date('H:i', strtotime($closing_time));

                            $opening_hours_spec[] = array(
                                '@type' => 'OpeningHoursSpecification',
                                'dayOfWeek' => $day_name,
                                'opens' => $opening_time_24h,
                                'closes' => $closing_time_24h
                            );
                        }
                        // Check if closed
                        elseif (preg_match('/\bclosed\b/i', $part)) {
                            $opening_hours_spec[] = array(
                                '@type' => 'OpeningHoursSpecification',
                                'dayOfWeek' => $day_name,
                                'validFrom' => date('Y-m-d'), // Current date
                                'validThrough' => date('Y-m-d', strtotime('+1 year')), // Valid for a year
                                'closes' => '00:00', // Required by schema
                                'opens' => '00:00'   // Required by schema
                            );
                        }
                    }
                }
            }
        }
    }

    // Add default hours for any days not specified (assume standard business hours)
    foreach ($day_mapping as $day_key => $day_name) {
        if (!isset($processed_days[$day_name])) {
            // For missing days, assume standard business hours (10 AM - 6 PM)
            // This ensures complete schema data for all days of the week
            $opening_hours_spec[] = array(
                '@type' => 'OpeningHoursSpecification',
                'dayOfWeek' => $day_name,
                'opens' => '10:00',
                'closes' => '18:00'
            );
        }
    }

    // Consolidate hours specifications for days with the same hours
    $consolidated_hours = array();
    $hours_by_pattern = array();

    foreach ($opening_hours_spec as $spec) {
        $days = is_array($spec['dayOfWeek']) ? $spec['dayOfWeek'] : array($spec['dayOfWeek']);
        $hours_pattern = isset($spec['opens']) && isset($spec['closes']) ?
            $spec['opens'] . '-' . $spec['closes'] : 'closed';

        foreach ($days as $day) {
            $hours_by_pattern[$hours_pattern][] = $day;
        }
    }

    // Create consolidated specifications
    foreach ($hours_by_pattern as $pattern => $days) {
        if ($pattern === 'closed' || $pattern === '00:00-00:00') {
            $consolidated_hours[] = array(
                '@type' => 'OpeningHoursSpecification',
                'dayOfWeek' => array_unique($days),
                'validFrom' => date('Y-m-d'),
                'validThrough' => date('Y-m-d', strtotime('+1 year')),
                'closes' => '00:00',
                'opens' => '00:00'
            );
        } else {
            list($opens, $closes) = explode('-', $pattern);
            $consolidated_hours[] = array(
                '@type' => 'OpeningHoursSpecification',
                'dayOfWeek' => array_unique($days),
                'opens' => $opens,
                'closes' => $closes
            );
        }
    }

    // Use consolidated hours if available, otherwise use the original specs
    $opening_hours_spec = !empty($consolidated_hours) ? $consolidated_hours : $opening_hours_spec;

    // Get images
    $images = get_post_meta( $post_id, 'images', true );
    $image_urls = array();

    if ( ! empty( $images ) && is_array( $images ) ) {
        foreach ( $images as $image ) {
            if ( ! empty( $image['url'] ) ) {
                $image_urls[] = $image['url'];
            }
        }
    } elseif ( has_post_thumbnail( $post_id ) ) {
        $image_urls[] = get_the_post_thumbnail_url( $post_id, 'large' );
    }

    // Get FAQ data with enhanced structure
    $faq = get_post_meta( $post_id, 'faq', true );
    $faq_items = array();

    if ( ! empty( $faq ) && is_array( $faq ) ) {
        foreach ( $faq as $item ) {
            if ( ! empty( $item['question'] ) && ! empty( $item['answer'] ) ) {
                // Create a properly structured Question with enhanced properties
                // Explicitly avoid adding @context entries to prevent them from being added later
                $question = array(
                    '@type' => 'Question',
                    'name' => $item['question'],
                    'acceptedAnswer' => array(
                        '@type' => 'Answer',
                        'text' => $item['answer']
                    )
                );

                // Ensure no @context entries are present
                if (isset($question['@context'])) {
                    unset($question['@context']);
                }
                if (isset($question['acceptedAnswer']['@context'])) {
                    unset($question['acceptedAnswer']['@context']);
                }

                $faq_items[] = $question;
            }
        }
    }

    // Build schema with improved type and core information for local business
    $schema = array(
        '@context' => 'https://schema.org',
        '@graph' => array()
    );

    // Create the main business entity
    // Ensure we're using the correct permalink URL, not a social media URL
    $permalink_url = get_permalink($post_id);

    $business_entity = array(
        '@type' => array('HealthAndBeautyBusiness', 'DaySpa', 'LocalBusiness'),
        '@id' => $permalink_url . '#business',
        'name' => $name,
        'description' => $description,
        'url' => $permalink_url,
        'address' => array(
            '@type' => 'PostalAddress',
            'streetAddress' => $address,
            'addressLocality' => $city,
            'addressRegion' => 'Catalonia',
            'postalCode' => !empty($location['zip']) ? $location['zip'] : '',
            'addressCountry' => 'ES'
        ),
        'isAccessibleForFree' => false,
        'publicAccess' => true,
        // Add local business specific properties
        'paymentAccepted' => 'Cash, Credit Card',
        'currenciesAccepted' => 'EUR'
    );

    // Add alternateName based on neighborhood
    if (!empty($neighborhood_name)) {
        $business_entity['alternateName'] = $name . ' ' . $neighborhood_name . ' Barcelona';
    }

    // Add postal code if available
    if (!empty($location['zip'])) {
        $business_entity['address']['postalCode'] = $location['zip'];
    }

    // Add state/region if available
    if (!empty($location['state'])) {
        $business_entity['address']['addressRegion'] = $location['state'];
    }

    // Add business-specific information
    $business_entity['isAccessibleForFree'] = false; // Spas typically charge for services
    $business_entity['publicAccess'] = true;

    // Add accessibility information
    $accessibility = get_post_meta( $post_id, 'accessibility', true );
    if (!empty($accessibility) && is_array($accessibility)) {
        // Store accessibility information in a more schema-compliant way

        // Add physical accessibility features as amenities
        if (!isset($business_entity['amenityFeature'])) {
            $business_entity['amenityFeature'] = array();
        }

        // We're not adding physical accessibility features to amenityFeature anymore
        // as per the requirement to only include amenities, public transportation, and preparation tips

        // Add accessibility advice as separate amenity features with more accurate naming
        foreach ($accessibility as $key => $value) {
            if ($value && strpos($key, 'piece of advice') !== false) {
                // Use "Visit Preparation" as the name for all preparation tips
                $name = 'Visit Preparation';

                // Always set value to True for preparation tips
                $feature_value = 'True';

                $business_entity['amenityFeature'][] = array(
                    '@type' => 'LocationFeatureSpecification',
                    'name' => $name,
                    'value' => $feature_value,
                    'description' => $value
                );
            }
        }
    }

    // Add business category and type information
    $business_entity['mainEntityOfPage'] = array('@id' => $permalink_url . '#webpage');

    // Add hasMap with proper structured data
    if (!empty($location['coordinates']['latitude']) && !empty($location['coordinates']['longitude'])) {
        $business_entity['hasMap'] = array(
            '@type' => 'Map',
            'url' => sprintf(
                'https://www.google.com/maps/place/%s/@%s,%s,15z',
                urlencode($name . ', ' . $address . ', ' . $city),
                $location['coordinates']['latitude'],
                $location['coordinates']['longitude']
            )
        );
    }

    // Add local business hours
    if (!empty($opening_hours_spec)) {
        $business_entity['openingHoursSpecification'] = $opening_hours_spec;
    }

    // No longer using additionalProperty array in the business entity

    // Add areaServed property for local SEO with enhanced structure
    $neighborhood_terms = get_the_terms($post_id, 'spa_neighborhood');
    if (!empty($neighborhood_terms) && !is_wp_error($neighborhood_terms)) {
        // Use only the first neighborhood term to avoid duplication
        $term = reset($neighborhood_terms);

        // Use a structured Place for areaServed
        $business_entity['areaServed'] = array(
            '@type' => 'Place',
            'name' => $term->name,
            'containedInPlace' => array(
                '@type' => 'City',
                'name' => 'Barcelona'
            )
        );

        // Also add to the address for better local SEO
        if (!isset($business_entity['address']['addressRegion'])) {
            $business_entity['address']['addressRegion'] = 'Catalonia';
        }

        // Don't add neighborhood description to the main description
        // Keep the original description from the JSON file

        // No longer adding neighborhood as a tag to additionalProperty
    } elseif (!empty($neighborhood)) {
        // Fallback to simple string if no taxonomy terms
        $business_entity['areaServed'] = array(
            '@type' => 'Place',
            'name' => $neighborhood,
            'containedInPlace' => array(
                '@type' => 'City',
                'name' => 'Barcelona'
            )
        );

        // Also add to the address for better local SEO
        if (!isset($business_entity['address']['addressRegion'])) {
            $business_entity['address']['addressRegion'] = 'Catalonia';
        }

        // No longer adding neighborhood as a tag to additionalProperty
    }

    // Add comprehensive contact information
    if ( ! empty( $phone ) ) {
        $business_entity['telephone'] = $phone;

        // Add structured telephone data for better machine readability
        // Format international number for better recognition
        $formatted_phone = $phone;
        if (strpos($phone, '+') === false) {
            // If no international prefix, assume Spain (+34)
            $formatted_phone = '+34 ' . preg_replace('/^0+/', '', $phone);
        }
        $business_entity['contactPoint'] = array(
            '@type' => 'ContactPoint',
            'telephone' => $formatted_phone,
            'contactType' => 'customer service',
            'areaServed' => 'ES',
            'availableLanguage' => array('Spanish', 'English')
        );
    }

    if ( ! empty( $email ) ) {
        $business_entity['email'] = $email;
    }

    // Initialize sameAs array for website and social media
    $business_entity['sameAs'] = array();

    if ( ! empty( $website ) ) {
        // Add website to sameAs array if not already there
        $business_entity['sameAs'][] = $website;
    }

    // Add social media profiles
    $social_media = get_post_meta( $post_id, 'social_media', true );
    if (!empty($social_media) && is_array($social_media)) {
        // Add each social media profile
        foreach ($social_media as $platform => $url) {
            if (!empty($url)) {
                $business_entity['sameAs'][] = $url;
            }
        }
    }

    // Add images
    if ( ! empty( $image_urls ) ) {
        $business_entity['image'] = $image_urls;
    } else {
        // Add a default image if none is available
        $business_entity['image'] = array(
            home_url('/wp-content/themes/spasinbarcelona/assets/images/default-spa.jpg')
        );
    }

    // Add services as offered services
    $services = get_post_meta( $post_id, 'services', true );
    if ( ! empty( $services ) && is_array( $services ) ) {
        // Create an array to hold all services
        $service_offers = array();

        foreach ( $services as $service_name ) {
            // Check if this is a location/city keyword rather than a service
            $location_keywords = array('barcelona', 'spain', 'catalunya', 'catalonia');
            $is_location = false;

            $service_lower = strtolower($service_name);
            foreach ($location_keywords as $keyword) {
                if (stripos($service_lower, $keyword) !== false) {
                    $is_location = true;
                    break;
                }
            }

            if (!$is_location) {
                // Create a Service object for actual services
                $service_type = 'Wellness Service'; // Default service type

                // Determine service type based on common keywords
                if (strpos($service_lower, 'massage') !== false) {
                    $service_type = 'Massage Therapy';
                } elseif (strpos($service_lower, 'facial') !== false) {
                    $service_type = 'Facial Treatment';
                } elseif (strpos($service_lower, 'body') !== false) {
                    $service_type = 'Body Treatment';
                } elseif (strpos($service_lower, 'nail') !== false || strpos($service_lower, 'manicure') !== false || strpos($service_lower, 'pedicure') !== false) {
                    $service_type = 'Nail Care';
                } elseif (strpos($service_lower, 'hair') !== false) {
                    $service_type = 'Hair Care';
                } elseif (strpos($service_lower, 'wax') !== false || strpos($service_lower, 'depil') !== false) {
                    $service_type = 'Hair Removal';
                }

                // Create an Offer with itemOffered structure for makesOffer
                $service = array(
                    '@type' => 'Offer',
                    'name' => $service_name,
                    'itemOffered' => array(
                        '@type' => 'Service',
                        'name' => $service_name,
                        'serviceType' => $service_type
                    )
                );

                $service_offers[] = $service;
            }
        }

        // Store services for later addition to makesOffer
        if (!empty($service_offers)) {
            if (!isset($business_entity['makesOffer'])) {
                $business_entity['makesOffer'] = $service_offers;
            } else {
                $business_entity['makesOffer'] = array_merge($business_entity['makesOffer'], $service_offers);
            }
        }
    }

    // Get specialties but don't add them to keywords
    $specialties = get_post_meta( $post_id, 'specialties', true );
    // No longer adding specialties to additionalProperty as they're already in makesOffer

    // Add amenities as proper features
    $amenities = get_post_meta( $post_id, 'amenities', true );
    if ( ! empty( $amenities ) && is_array( $amenities ) ) {
        if (!isset($business_entity['amenityFeature'])) {
            $business_entity['amenityFeature'] = array();
        }

        foreach ( $amenities as $amenity ) {
            // Create a LocationFeatureSpecification for each amenity
            $feature = array(
                '@type' => 'LocationFeatureSpecification',
                'name' => 'Amenities',
                'value' => 'True',
                'description' => $amenity
            );

            $business_entity['amenityFeature'][] = $feature;
        }
    }

    // Add enhanced geo coordinates and map information
    if ( ! empty( $location['coordinates']['latitude'] ) && ! empty( $location['coordinates']['longitude'] ) ) {
        // Add geo coordinates with proper typing
        $business_entity['geo'] = array(
            '@type' => 'GeoCoordinates',
            'latitude' => floatval($location['coordinates']['latitude']),
            'longitude' => floatval($location['coordinates']['longitude'])
        );
    }

    // Add nearby attractions if available
    $nearby_attractions = get_post_meta( $post_id, 'nearby_attractions', true );
    if ( ! empty( $nearby_attractions ) && is_array( $nearby_attractions ) ) {
        // Track which attractions have been added to avoid duplication
        $added_attractions = array();
        $tourist_attractions = array();

        foreach ($nearby_attractions as $attraction) {
            // Handle both array of strings and array of objects formats
            $attraction_name = '';
            $attraction_description = '';
            $attraction_distance = '';
            $attraction_travel_time = '';
            $attraction_category = '';
            $attraction_is_free = true; // Default to free
            $attraction_geo = null;
            $attraction_address = null;

            // Check if attraction is a string (old format) or an array (new format)
            if (is_string($attraction)) {
                // Simple string format - just the name
                $attraction_name = $attraction;

                // Try to extract category from name if it contains parentheses
                if (preg_match('/\((.*?)\)/', $attraction_name, $matches)) {
                    $attraction_category = trim($matches[1]);
                    // Remove the category part from the name
                    $attraction_name = trim(str_replace('(' . $matches[1] . ')', '', $attraction_name));
                }
            } elseif (is_array($attraction) && !empty($attraction['name'])) {
                // Array format with detailed information
                $attraction_name = $attraction['name'];
                $attraction_description = !empty($attraction['description']) ? $attraction['description'] : '';

                // Handle different distance/travel time fields
                if (!empty($attraction['travelTime'])) {
                    $attraction_travel_time = $attraction['travelTime'];
                } elseif (!empty($attraction['distance'])) {
                    $attraction_distance = $attraction['distance'];
                }

                // Get category if available
                $attraction_category = !empty($attraction['category']) ? $attraction['category'] : '';

                // Check if it's free
                if (isset($attraction['isAccessibleForFree'])) {
                    $attraction_is_free = (bool)$attraction['isAccessibleForFree'];
                }

                // Get geo coordinates
                if (!empty($attraction['geo'])) {
                    $attraction_geo = $attraction['geo'];
                } elseif (!empty($attraction['coordinates'])) {
                    $attraction_geo = $attraction['coordinates'];
                }

                // Get address
                if (!empty($attraction['address'])) {
                    $attraction_address = $attraction['address'];
                }
            } else {
                // Skip invalid entries
                continue;
            }

            // Skip if empty name
            if (empty($attraction_name)) {
                continue;
            }

            // Create a unique key for this attraction
            $attraction_key = sanitize_title($attraction_name);

            // Skip if already added
            if (isset($added_attractions[$attraction_key])) {
                continue;
            }

            // Mark as added
            $added_attractions[$attraction_key] = true;

            // Create a proper TouristAttraction object
            $tourist_attraction = array(
                '@type' => 'TouristAttraction',
                '@id' => $permalink_url . '#' . $attraction_key,
                'name' => $attraction_name
                // Removed isPartOf property as it incorrectly implies the attraction is part of the webpage
            );

            // Add description if available
            if (!empty($attraction_description)) {
                $tourist_attraction['description'] = $attraction_description;
            }

            // Add address with proper structure
            if (!empty($attraction_address)) {
                $tourist_attraction['address'] = array(
                    '@type' => 'PostalAddress',
                    'streetAddress' => !empty($attraction_address['streetAddress']) ? $attraction_address['streetAddress'] : 'Barcelona',
                    'addressLocality' => 'Barcelona',
                    'addressCountry' => 'ES'
                );

                if (!empty($attraction_address['postalCode'])) {
                    $tourist_attraction['address']['postalCode'] = $attraction_address['postalCode'];
                }
            } else {
                // Default address for Barcelona attractions
                $tourist_attraction['address'] = array(
                    '@type' => 'PostalAddress',
                    'streetAddress' => 'Barcelona',
                    'addressLocality' => 'Barcelona',
                    'addressCountry' => 'ES'
                );
            }

            // Add geo coordinates if available
            if (!empty($attraction_geo)) {
                $tourist_attraction['geo'] = array(
                    '@type' => 'GeoCoordinates',
                    'latitude' => floatval(isset($attraction_geo['latitude']) ? $attraction_geo['latitude'] : 0),
                    'longitude' => floatval(isset($attraction_geo['longitude']) ? $attraction_geo['longitude'] : 0)
                );
            }

            // Add accessibility information
            $tourist_attraction['isAccessibleForFree'] = $attraction_is_free;

            // Initialize additionalProperty array
            $tourist_attraction['additionalProperty'] = array();

            // Add category if available
            if (!empty($attraction_category)) {
                $tourist_attraction['additionalProperty'][] = array(
                    '@type' => 'PropertyValue',
                    'name' => 'Category',
                    'value' => $attraction_category
                );
            }

            // Add travel time or distance as additionalProperty
            if (!empty($attraction_travel_time)) {
                $tourist_attraction['additionalProperty'][] = array(
                    '@type' => 'PropertyValue',
                    'name' => 'Approx. Travel Time',
                    'value' => $attraction_travel_time
                );
            } elseif (!empty($attraction_distance)) {
                $tourist_attraction['additionalProperty'][] = array(
                    '@type' => 'PropertyValue',
                    'name' => 'Distance',
                    'value' => $attraction_distance
                );
            }

            // Add Google Maps link if we have coordinates
            if (!empty($tourist_attraction['geo'])) {
                $tourist_attraction['hasMap'] = array(
                    '@type' => 'Map',
                    'url' => sprintf(
                        'https://www.google.com/maps/place/%s/@%s,%s,15z',
                        urlencode($attraction_name . ', Barcelona'),
                        $tourist_attraction['geo']['latitude'],
                        $tourist_attraction['geo']['longitude']
                    )
                );
            }

            // Add to tourist attractions array
            $tourist_attractions[] = $tourist_attraction;

            // No longer adding attractions to keywords
        }

        // Add tourist attractions to the schema graph
        if (!empty($tourist_attractions)) {
            foreach ($tourist_attractions as $attraction) {
                $schema['@graph'][] = $attraction;
            }
        }
    }

    // Add transportation information if available
    $transportation = get_post_meta( $post_id, 'transportation', true );
    if ( ! empty( $transportation ) && is_array( $transportation ) ) {
        // Set publicAccess to true if public transportation is available
        $business_entity['publicAccess'] = true;

        // Add transportation details as amenityFeature
        if (!isset($business_entity['amenityFeature'])) {
            $business_entity['amenityFeature'] = array();
        }

        if ( ! empty( $transportation['metro'] ) ) {
            $business_entity['amenityFeature'][] = array(
                '@type' => 'LocationFeatureSpecification',
                'name' => 'Metro Access',
                'value' => 'True',
                'description' => 'Metro: ' . $transportation['metro']
            );
        }

        if ( ! empty( $transportation['bus'] ) ) {
            $business_entity['amenityFeature'][] = array(
                '@type' => 'LocationFeatureSpecification',
                'name' => 'Metro Access',
                'value' => 'True',
                'description' => 'Nearby stops: ' . $transportation['bus']
            );
        }

        if ( ! empty( $transportation['train'] ) ) {
            $business_entity['amenityFeature'][] = array(
                '@type' => 'LocationFeatureSpecification',
                'name' => 'Metro Access',
                'value' => 'True',
                'description' => 'Nearby stations: ' . $transportation['train']
            );
        }

        // Don't add transportation information to the description
        // Instead, keep it only in the amenityFeature property
    }

    // Get popular features but don't add them to keywords
    $popular_features = get_post_meta( $post_id, 'popular', true );
    // No longer adding popular features to additionalProperty to avoid keyword stuffing

    // Add enhanced aggregate rating if available
    if ( $rating && $review_count ) {
        $business_entity['aggregateRating'] = array(
            '@type' => 'AggregateRating',
            'ratingValue' => floatval($rating),
            'reviewCount' => intval($review_count),
            'bestRating' => 5,
            'worstRating' => 1,
            'ratingExplanation' => 'Weighted average of ' . intval($review_count) . ' reviews from multiple sources',
            'itemReviewed' => array('@id' => $permalink_url . '#business')
        );
    }

    // Add review schema items with enhanced structure
    if ( ! empty( $review_schema_items ) ) {
        // Update each review to reference the business entity
        foreach ($review_schema_items as &$review) {
            // Always use the permalink URL for itemReviewed references
            $review['itemReviewed'] = array('@id' => $permalink_url . '#business');

            // Add language if not present
            if (!isset($review['inLanguage'])) {
                $review['inLanguage'] = 'en';
            }
        }

        // Add reviews directly to the business entity
        $business_entity['review'] = $review_schema_items;
    }

    // No longer adding pros and cons to additionalProperty to avoid keyword stuffing
    // We still collect them for display on the page, but not for schema markup
    $pros_cons = get_post_meta( $post_id, 'pros_cons', true );

    // Add services with specific treatments and their prices using makesOffer with enhanced structure
    $packages = get_post_meta( $post_id, 'packages', true );
    if ( ! empty( $packages ) && is_array( $packages ) ) {
        $offers = array();
        $package_categories = array();

        foreach ( $packages as $package ) {
            if ( is_array( $package ) && ! empty( $package['name'] ) ) {
                // Create a Service object for each package with enhanced details
                $service = array(
                    '@type' => 'Service',
                    'name' => $package['name'],
                    'description' => ! empty( $package['description'] ) ? $package['description'] : 'Popular package.',
                    'provider' => array('@id' => $permalink_url . '#business'),
                    'category' => 'Wellness Package',
                    'areaServed' => array('@type' => 'City', 'name' => 'Barcelona'),
                    'serviceOutput' => 'Enhanced wellness and relaxation experience'
                );

                // Add service category based on name keywords
                $package_name_lower = strtolower($package['name']);

                if (strpos($package_name_lower, 'massage') !== false) {
                    $service['category'] = 'Massage Therapy';
                    $package_categories['Massage Therapy'] = true;
                } elseif (strpos($package_name_lower, 'facial') !== false) {
                    $service['category'] = 'Facial Treatment';
                    $package_categories['Facial Treatment'] = true;
                } elseif (strpos($package_name_lower, 'body') !== false) {
                    $service['category'] = 'Body Treatment';
                    $package_categories['Body Treatment'] = true;
                } elseif (strpos($package_name_lower, 'couple') !== false || strpos($package_name_lower, 'couples') !== false) {
                    $service['category'] = 'Couples Treatment';
                    $package_categories['Couples Treatment'] = true;
                } elseif (strpos($package_name_lower, 'package') !== false || strpos($package_name_lower, 'ritual') !== false) {
                    $service['category'] = 'Spa Package';
                    $package_categories['Spa Package'] = true;
                } else {
                    $service['category'] = 'Wellness Treatment';
                    $package_categories['Wellness Treatment'] = true;
                }

                // Add audience if specified
                if (!empty($package['audience'])) {
                    $service['audience'] = array(
                        '@type' => 'Audience',
                        'audienceType' => $package['audience']
                    );
                }

                // Add service area
                $service['areaServed'] = array(
                    '@type' => 'City',
                    'name' => 'Barcelona'
                );

                // Add service URL if available
                if (!empty($package['url'])) {
                    $service['url'] = $package['url'];
                } else {
                    $service['url'] = $permalink_url . '#packages';
                }

                // Create an enhanced Offer object for the service following the required structure
                $offer = array(
                    '@type' => 'Offer',
                    'name' => $package['name'],
                    'itemOffered' => $service,
                    'seller' => array('@id' => $permalink_url . '#business'),
                    'availability' => 'https://schema.org/InStock' // Always set availability to InStock
                );

                // Add price with enhanced handling following the required structure
                if (!empty($package['price'])) {
                    // Create a PriceSpecification object
                    $offer['priceSpecification'] = array(
                        '@type' => 'PriceSpecification',
                        'priceCurrency' => !empty($package['currency']) ? $package['currency'] : 'EUR',
                        'validThrough' => '2026-05-01' // Fixed date for all offers
                    );

                    // Check if price is already a number (new format)
                    if (is_numeric($package['price'])) {
                        $offer['priceSpecification']['price'] = floatval($package['price']);
                    }
                    // Handle string price (old format)
                    else if ($package['price'] !== 'Varies') {
                        // Extract numeric value and currency
                        preg_match('/([A-Z]{3})\s*(\d+(?:\.\d+)?)|\$?(\d+(?:\.\d+)?)|\€?(\d+(?:\.\d+)?)/', $package['price'], $matches);

                        if (!empty($matches[2])) {
                            // Format: "EUR 275" or similar
                            $offer['priceSpecification']['price'] = floatval($matches[2]);
                            $offer['priceSpecification']['priceCurrency'] = $matches[1];
                        } elseif (!empty($matches[3])) {
                            // Format: "$275" or "275"
                            $offer['priceSpecification']['price'] = floatval($matches[3]);
                        } elseif (!empty($matches[4])) {
                            // Format: "€275"
                            $offer['priceSpecification']['price'] = floatval($matches[4]);
                        } else {
                            // Try to extract just numbers
                            preg_match('/\d+(?:\.\d+)?/', $package['price'], $num_matches);
                            if (!empty($num_matches[0])) {
                                $offer['priceSpecification']['price'] = floatval($num_matches[0]);
                            }
                        }
                    } else {
                        // Handle "Varies" price with a price range if min/max are available
                        if (!empty($package['priceMin']) && !empty($package['priceMax'])) {
                            $offer['priceSpecification']['minPrice'] = floatval($package['priceMin']);
                            $offer['priceSpecification']['maxPrice'] = floatval($package['priceMax']);
                        } else {
                            // Just note that price varies
                            $offer['priceSpecification']['description'] = 'Price varies based on options selected';
                        }
                    }
                }

                // Add eligibility requirements if available
                if (!empty($package['eligibleCustomerType'])) {
                    $offer['eligibleCustomerType'] = $package['eligibleCustomerType'];
                }

                // Add warranty information if available
                if (!empty($package['warranty'])) {
                    $offer['warranty'] = array(
                        '@type' => 'WarrantyPromise',
                        'description' => $package['warranty']
                    );
                }

                // Add duration with enhanced ISO 8601 formatting
                if (!empty($package['durationMinutes']) && is_numeric($package['durationMinutes'])) {
                    // Use ISO 8601 duration format
                    $hours = floor(intval($package['durationMinutes']) / 60);
                    $minutes = intval($package['durationMinutes']) % 60;
                    $duration = 'PT';
                    if ($hours > 0) {
                        $duration .= $hours . 'H';
                    }
                    if ($minutes > 0) {
                        $duration .= $minutes . 'M';
                    }
                    $service['timeRequired'] = $duration;

                    // Also add as a human-readable description
                    $time_description = '';
                    if ($hours > 0) {
                        $time_description .= $hours . ' hour' . ($hours > 1 ? 's' : '');
                    }
                    if ($minutes > 0) {
                        if ($hours > 0) {
                            $time_description .= ' and ';
                        }
                        $time_description .= $minutes . ' minute' . ($minutes > 1 ? 's' : '');
                    }

                    if (!empty($time_description)) {
                        if (!isset($service['additionalProperty'])) {
                            $service['additionalProperty'] = array();
                        }

                        $service['additionalProperty'][] = array(
                            '@type' => 'PropertyValue',
                            'name' => 'Duration',
                            'value' => $time_description
                        );
                    }
                } elseif (!empty($package['duration'])) {
                    // Try to parse duration from string format with enhanced pattern matching
                    if (preg_match('/(\d+)\s*(?:hour|hr)s?\s*(?:and)?\s*(\d+)?\s*(?:min|minute)s?/i', $package['duration'], $matches)) {
                        // Format: "2 hours and 30 minutes" or "2 hours 30 minutes"
                        $hours = (int)$matches[1];
                        $minutes = !empty($matches[2]) ? (int)$matches[2] : 0;
                        $service['timeRequired'] = 'PT' . $hours . 'H' . ($minutes > 0 ? $minutes . 'M' : '');
                    } elseif (preg_match('/(\d+)\s*(?:hour|hr)s?/i', $package['duration'], $matches)) {
                        // Format: "2 hours"
                        $hours = (int)$matches[1];
                        $service['timeRequired'] = 'PT' . $hours . 'H';
                    } elseif (preg_match('/(\d+)\s*(?:min|minute)s?/i', $package['duration'], $matches)) {
                        // Format: "30 minutes"
                        $minutes = (int)$matches[1];
                        $service['timeRequired'] = 'PT' . $minutes . 'M';
                    } elseif (preg_match('/(\d+)(?::(\d+))?/i', $package['duration'], $matches)) {
                        // Format: "2:30" or "2"
                        $hours = (int)$matches[1];
                        $minutes = !empty($matches[2]) ? (int)$matches[2] : 0;
                        $service['timeRequired'] = 'PT' . $hours . 'H' . ($minutes > 0 ? $minutes . 'M' : '');
                    }

                    // Also add the original duration string as a property
                    if (!isset($service['additionalProperty'])) {
                        $service['additionalProperty'] = array();
                    }

                    $service['additionalProperty'][] = array(
                        '@type' => 'PropertyValue',
                        'name' => 'Duration',
                        'value' => $package['duration']
                    );
                }

                // Add included items if available
                if (!empty($package['includes']) && is_array($package['includes'])) {
                    if (!isset($service['additionalProperty'])) {
                        $service['additionalProperty'] = array();
                    }

                    $service['additionalProperty'][] = array(
                        '@type' => 'PropertyValue',
                        'name' => 'Includes',
                        'value' => implode(', ', $package['includes'])
                    );
                }

                // Add benefits if available
                if (!empty($package['benefits']) && is_array($package['benefits'])) {
                    if (!isset($service['additionalProperty'])) {
                        $service['additionalProperty'] = array();
                    }

                    $service['additionalProperty'][] = array(
                        '@type' => 'PropertyValue',
                        'name' => 'Benefits',
                        'value' => implode(', ', $package['benefits'])
                    );
                }

                $offers[] = $offer;
            }
        }

        if (!empty($offers)) {
            // Ensure each offer has proper structure and references
            foreach ($offers as &$offer) {
                // Make sure each offer has the proper availability status
                if (!isset($offer['availability'])) {
                    $offer['availability'] = 'https://schema.org/InStock';
                }

                // Ensure each offer has a proper seller reference
                if (!isset($offer['seller']) || !isset($offer['seller']['@id'])) {
                    $offer['seller'] = array('@id' => $permalink_url . '#business');
                }

                // Ensure each offer's itemOffered has a proper provider reference
                if (isset($offer['itemOffered']) && isset($offer['itemOffered']['provider'])) {
                    $offer['itemOffered']['provider'] = array('@id' => $permalink_url . '#business');
                }
            }

            // Add the offers to the business entity
            if (!isset($business_entity['makesOffer'])) {
                $business_entity['makesOffer'] = $offers;
            } else {
                $business_entity['makesOffer'] = array_merge($business_entity['makesOffer'], $offers);
            }
        }
    }

    // Ensure additionalProperty is completely removed from the business entity
    if (isset($business_entity['additionalProperty'])) {
        unset($business_entity['additionalProperty']);
    }

    // Filter amenityFeature to only include the specified types
    if (isset($business_entity['amenityFeature']) && is_array($business_entity['amenityFeature'])) {
        $filtered_amenities = array();
        foreach ($business_entity['amenityFeature'] as $feature) {
            // Only keep features with name 'Amenities', 'Metro Access', or 'Visit Preparation'
            if (isset($feature['name']) &&
                ($feature['name'] === 'Amenities' ||
                 $feature['name'] === 'Metro Access' ||
                 $feature['name'] === 'Visit Preparation')) {
                $filtered_amenities[] = $feature;
            }
        }
        $business_entity['amenityFeature'] = $filtered_amenities;
    }

    // Remove keywords property if it exists
    if (isset($business_entity['keywords'])) {
        unset($business_entity['keywords']);
    }

    // Add the business entity to the graph
    $schema['@graph'][] = $business_entity;

    // Create Website entity
    $website_entity = array(
        '@type' => 'WebSite',
        '@id' => home_url() . '#website',
        'url' => home_url(),
        'name' => 'Spas in Barcelona',
        'description' => 'Directory of the best spas in Barcelona',
        'publisher' => array(
            '@type' => 'Organization',
            'name' => 'Spas in Barcelona'
        ),
        'inLanguage' => 'en'
    );

    // Add the Website entity to the graph
    $schema['@graph'][] = $website_entity;

    // Create WebPage entity with improved URL and name handling
    // Ensure we're using the correct permalink URL, not a social media URL
    $permalink_url = get_permalink($post_id);

    $webpage_entity = array(
        '@type' => 'WebPage',
        '@id' => $permalink_url . '#webpage', // Always use the permalink URL, not social media URLs
        'url' => $permalink_url, // Ensure URL is always the permalink
        'name' => $name . ' | Spas in Barcelona', // Proper page title format
        'description' => $meta_description ? $meta_description : $description,
        'isPartOf' => array('@id' => home_url() . '#website'),
        'mainEntity' => array('@id' => $permalink_url . '#business'),
        'breadcrumb' => array('@id' => $permalink_url . '#breadcrumb'),
        'inLanguage' => 'en',
        'datePublished' => get_the_date('c', $post_id),
        'dateModified' => get_the_modified_date('c', $post_id)
    );

    // Add the WebPage entity to the graph
    $schema['@graph'][] = $webpage_entity;

    // Create BreadcrumbList entity - no neighborhood level as requested
    $breadcrumb_entity = array(
        '@type' => 'BreadcrumbList',
        '@id' => $permalink_url . '#breadcrumb',
        'itemListElement' => array(
            array(
                '@type' => 'ListItem',
                'position' => 1,
                'name' => 'Home',
                'item' => home_url()
            ),
            array(
                '@type' => 'ListItem',
                'position' => 2,
                'name' => 'Spas Barcelona',
                'item' => get_post_type_archive_link('spa')
            ),
            array(
                '@type' => 'ListItem',
                'position' => 3,
                'name' => $name, // Always use the spa name for the last item
                'item' => $permalink_url   // Always use the spa permalink URL for the last item
            )
        )
    );

    // Ensure no @context attributes in breadcrumb items
    foreach ($breadcrumb_entity['itemListElement'] as $index => $item) {
        if (isset($breadcrumb_entity['itemListElement'][$index]['@context'])) {
            unset($breadcrumb_entity['itemListElement'][$index]['@context']);
        }
    }

    // Add the BreadcrumbList entity to the graph
    $schema['@graph'][] = $breadcrumb_entity;

    // Add FAQ items if available
    if ( ! empty( $faq_items ) ) {
        // Clean up FAQ items to remove any redundant @context entries
        foreach ( $faq_items as $key => $item ) {
            // Remove @context from questions
            if ( isset( $item['@context'] ) ) {
                unset( $faq_items[$key]['@context'] );
            }

            // Remove @context from answers
            if ( isset( $item['acceptedAnswer'] ) && isset( $item['acceptedAnswer']['@context'] ) ) {
                unset( $faq_items[$key]['acceptedAnswer']['@context'] );
            }
        }

        // Create a separate FAQPage schema with enhanced structure
        $faq_entity = array(
            '@type' => 'FAQPage',
            '@id' => $permalink_url . '#faq', // Use the full permalink URL for the @id
            'mainEntity' => $faq_items,
            'isPartOf' => array('@id' => $permalink_url . '#webpage')
        );

        // Add the FAQPage entity to the graph
        $schema['@graph'][] = $faq_entity;
    }

    // Remove redundant @context entries
    $schema = spa_remove_nested_context($schema);

    // Validate schema before output
    $schema = spa_validate_schema($schema);

    // Additional validation for breadcrumb entities in the graph
    if (isset($schema['@graph']) && is_array($schema['@graph'])) {
        foreach ($schema['@graph'] as $key => $entity) {
            if (isset($entity['@type']) && $entity['@type'] === 'BreadcrumbList') {
                // Apply breadcrumb-specific validation
                $schema['@graph'][$key] = spa_validate_breadcrumb_list($entity);
            }
        }
    }

    // Final cleanup of entities in the graph to ensure proper formatting
    if (isset($schema['@graph']) && is_array($schema['@graph'])) {
        foreach ($schema['@graph'] as $key => $entity) {
            // Handle FAQPage entities
            if (isset($entity['@type']) && $entity['@type'] === 'FAQPage') {
                // Ensure @id uses the full URL
                if (isset($entity['@id']) && $entity['@id'] === '#faq') {
                    global $post;
                    if ($post && 'spa' === $post->post_type) {
                        $permalink_url = get_permalink($post->ID);
                        $schema['@graph'][$key]['@id'] = $permalink_url . '#faq';
                    } else {
                        // Fallback to a generic ID
                        $schema['@graph'][$key]['@id'] = home_url() . '#faq';
                    }
                }

                // Clean up mainEntity
                if (isset($entity['mainEntity']) && is_array($entity['mainEntity'])) {
                    foreach ($entity['mainEntity'] as $index => $question) {
                        // Remove @context from questions
                        if (isset($question['@context'])) {
                            unset($schema['@graph'][$key]['mainEntity'][$index]['@context']);
                        }

                        // Remove @context from answers
                        if (isset($question['acceptedAnswer']) && isset($question['acceptedAnswer']['@context'])) {
                            unset($schema['@graph'][$key]['mainEntity'][$index]['acceptedAnswer']['@context']);
                        }
                    }
                }
            }

            // Handle WebPage entities
            if (isset($entity['@type']) && $entity['@type'] === 'WebPage') {
                // Ensure URL is present and valid
                if (!isset($entity['url']) || empty($entity['url']) || $entity['url'] === null) {
                    global $post;
                    if ($post && 'spa' === $post->post_type) {
                        $schema['@graph'][$key]['url'] = get_permalink($post->ID);
                    } else {
                        // Fallback to home URL
                        $schema['@graph'][$key]['url'] = home_url();
                    }
                }

                // Ensure name is properly formatted and never contains "Preparation"
                if (!isset($entity['name']) || empty($entity['name']) || strpos($entity['name'], 'Preparation') !== false) {
                    global $post;
                    if ($post && 'spa' === $post->post_type) {
                        $schema['@graph'][$key]['name'] = get_the_title($post->ID) . ' | Spas in Barcelona';
                    } else {
                        // Fallback to generic name
                        $schema['@graph'][$key]['name'] = 'Spa Directory Barcelona';
                    }
                }

                // Ensure breadcrumb reference is correct
                if (isset($entity['breadcrumb']) && is_array($entity['breadcrumb'])) {
                    // Make sure breadcrumb reference uses the correct URL
                    if (isset($entity['url']) && !empty($entity['url'])) {
                        $schema['@graph'][$key]['breadcrumb'] = array('@id' => $entity['url'] . '#breadcrumb');
                    }
                }
            }
        }
    }

    // Final verification to ensure no nested @context entries remain
    $schema = spa_verify_no_nested_context($schema);

    // Prepare the output with all schema types
    $output = '<script type="application/ld+json">' . wp_json_encode( $schema ) . '</script>';

    // Add schema.org testing comment for developers
    $output .= '<!-- Schema.org markup validated and generated by SpasinBarcelona. Test using:
    - Google Rich Results Test: https://search.google.com/test/rich-results
    - Schema.org Validator: https://validator.schema.org/
    - Google Structured Data Testing Tool (Legacy): https://search.google.com/structured-data/testing-tool
    -->';

    // Final fix for breadcrumb schema - directly modify the output to ensure the third item is correct
    if (is_singular('spa')) {
        $post_id = get_the_ID();
        $spa_name = get_the_title($post_id);
        $spa_url = get_permalink($post_id);

        // Use regex to find and replace any incorrect breadcrumb items
        $pattern = '/"position":\s*3,\s*"name":\s*"(Preparation|Visit Preparation)",\s*"item":\s*"[^"]*"/';
        $replacement = '"position": 3, "name": "' . esc_js($spa_name) . '", "item": "' . esc_js($spa_url) . '"';
        $output = preg_replace($pattern, $replacement, $output);
    }

    return $output;
}

/**
 * Validate schema.org markup to ensure it meets requirements
 *
 * @param array $schema The schema to validate
 * @return array The validated schema
 */
function spa_validate_schema($schema) {
    // Skip validation if schema is not an array
    if (!is_array($schema)) {
        return $schema;
    }

    // Remove keywords property if it exists
    if (isset($schema['keywords'])) {
        unset($schema['keywords']);
    }

    // Ensure required properties for all schema types
    if (isset($schema['@type'])) {
        // Validate common properties for all types
        $schema = spa_validate_common_properties($schema);

        // Validate specific schema types
        switch ($schema['@type']) {
            case 'HealthAndBeautyBusiness':
            case 'LocalBusiness':
            case 'DaySpa':
                $schema = spa_validate_local_business($schema);
                break;

            case 'FAQPage':
                $schema = spa_validate_faq_page($schema);
                break;

            case 'WebPage':
                // If this is a review page (has reviews in mainEntity), validate it as a review page
                if (isset($schema['mainEntity']) && isset($schema['mainEntity']['review'])) {
                    $schema = spa_validate_review_page($schema);
                } else {
                    // Validate regular WebPage entity
                    $schema = spa_validate_webpage($schema);
                }
                break;

            case 'BreadcrumbList':
                $schema = spa_validate_breadcrumb_list($schema);
                break;
        }
    }

    // Recursively validate nested objects
    foreach ($schema as $key => $value) {
        if (is_array($value)) {
            // Always remove redundant @context entries in nested objects
            if ($key !== '@context' && isset($value['@context'])) {
                unset($schema[$key]['@context']);
            }

            if (isset($value['@type'])) {
                // This is a nested schema object
                $schema[$key] = spa_validate_schema($value);
            } else if (is_numeric(key($value)) || empty($value)) {
                // This is an array of items
                foreach ($value as $index => $item) {
                    if (is_array($item)) {
                        // Always remove redundant @context entries in array items
                        if (isset($item['@context'])) {
                            unset($schema[$key][$index]['@context']);
                        }

                        if (isset($item['@type'])) {
                            // Validate each schema object in the array
                            $schema[$key][$index] = spa_validate_schema($item);
                        } else {
                            // Even if it doesn't have @type, process it recursively
                            // to remove any nested @context entries
                            $schema[$key][$index] = spa_remove_nested_context($item);
                        }
                    }
                }
            } else {
                // Process other nested arrays to remove any @context entries
                $schema[$key] = spa_remove_nested_context($value);
            }
        }
    }

    return $schema;
}

/**
 * Validate common properties for all schema types
 *
 * @param array $schema The schema to validate
 * @return array The validated schema
 */
function spa_validate_common_properties($schema) {
    // Ensure @context is present
    if (!isset($schema['@context'])) {
        $schema['@context'] = 'https://schema.org';
    }

    // Ensure all URLs are valid
    foreach ($schema as $key => $value) {
        if (in_array($key, array('url', 'image', 'logo', 'sameAs')) && !empty($value)) {
            if (is_array($value)) {
                foreach ($value as $index => $url) {
                    if (!filter_var($url, FILTER_VALIDATE_URL)) {
                        // Remove invalid URLs
                        unset($schema[$key][$index]);
                    }
                }
                // Re-index array if items were removed
                if (is_array($schema[$key])) {
                    $schema[$key] = array_values($schema[$key]);
                }
            } else if (!filter_var($value, FILTER_VALIDATE_URL)) {
                // Remove invalid URL
                unset($schema[$key]);
            }
        }
    }

    // Ensure numeric values are properly formatted
    foreach ($schema as $key => $value) {
        if (in_array($key, array('ratingValue', 'reviewCount', 'price', 'latitude', 'longitude'))) {
            if (is_numeric($value)) {
                $schema[$key] = floatval($value);
            }
        }
    }

    return $schema;
}

/**
 * Validate LocalBusiness schema
 *
 * @param array $schema The schema to validate
 * @return array The validated schema
 */
function spa_validate_local_business($schema) {
    // Required properties for LocalBusiness
    $required_props = array('name', 'address');

    foreach ($required_props as $prop) {
        if (!isset($schema[$prop]) || empty($schema[$prop])) {
            // Add default values for missing required properties
            switch ($prop) {
                case 'name':
                    $schema[$prop] = 'Spa in Barcelona';
                    break;
                case 'address':
                    $schema[$prop] = array(
                        '@type' => 'PostalAddress',
                        'addressLocality' => 'Barcelona',
                        'addressCountry' => 'Spain'
                    );
                    break;
            }
        }
    }

    // Validate address
    if (isset($schema['address']) && is_array($schema['address'])) {
        if (!isset($schema['address']['@type'])) {
            $schema['address']['@type'] = 'PostalAddress';
        }
    }

    // Validate geo coordinates
    if (isset($schema['geo']) && is_array($schema['geo'])) {
        if (!isset($schema['geo']['@type'])) {
            $schema['geo']['@type'] = 'GeoCoordinates';
        }

        // Ensure latitude and longitude are numeric
        if (isset($schema['geo']['latitude'])) {
            $schema['geo']['latitude'] = floatval($schema['geo']['latitude']);
        }

        if (isset($schema['geo']['longitude'])) {
            $schema['geo']['longitude'] = floatval($schema['geo']['longitude']);
        }
    }

    return $schema;
}

/**
 * Validate FAQPage schema
 *
 * @param array $schema The schema to validate
 * @return array The validated schema
 */
function spa_validate_faq_page($schema) {
    // Ensure mainEntity is present
    if (!isset($schema['mainEntity']) || !is_array($schema['mainEntity'])) {
        $schema['mainEntity'] = array();
    }

    // Ensure @id is present for proper linking
    if (!isset($schema['@id']) || empty($schema['@id'])) {
        // Try to generate a valid @id from context
        global $post;
        if ($post && 'spa' === $post->post_type) {
            $permalink_url = get_permalink($post->ID);
            $schema['@id'] = $permalink_url . '#faq';
        } else {
            // Fallback to a generic ID
            $schema['@id'] = home_url() . '#faq';
        }
    }

    // Validate each question
    foreach ($schema['mainEntity'] as $index => $question) {
        if (!isset($question['@type']) || $question['@type'] !== 'Question') {
            $schema['mainEntity'][$index]['@type'] = 'Question';
        }

        if (!isset($question['name']) || empty($question['name'])) {
            // Remove questions without a name
            unset($schema['mainEntity'][$index]);
            continue;
        }

        // Ensure acceptedAnswer is present and valid
        if (!isset($question['acceptedAnswer']) || !is_array($question['acceptedAnswer'])) {
            $schema['mainEntity'][$index]['acceptedAnswer'] = array(
                '@type' => 'Answer',
                'text' => 'Please contact us for more information.'
            );
        } else if (!isset($question['acceptedAnswer']['@type'])) {
            $schema['mainEntity'][$index]['acceptedAnswer']['@type'] = 'Answer';
        }

        // Ensure answer text is present
        if (!isset($question['acceptedAnswer']['text']) || empty($question['acceptedAnswer']['text'])) {
            $schema['mainEntity'][$index]['acceptedAnswer']['text'] = 'Please contact us for more information.';
        }

        // Remove any redundant @context entries from questions and answers
        if (isset($schema['mainEntity'][$index]['@context'])) {
            unset($schema['mainEntity'][$index]['@context']);
        }
        if (isset($schema['mainEntity'][$index]['acceptedAnswer']['@context'])) {
            unset($schema['mainEntity'][$index]['acceptedAnswer']['@context']);
        }
    }

    // Re-index array if items were removed
    $schema['mainEntity'] = array_values($schema['mainEntity']);

    // Ensure we have at least one question, otherwise the FAQPage is not valid
    if (empty($schema['mainEntity'])) {
        // Add a default question if none exist
        $schema['mainEntity'][] = array(
            '@type' => 'Question',
            'name' => 'What services does this spa offer?',
            'acceptedAnswer' => array(
                '@type' => 'Answer',
                'text' => 'Please contact the spa directly for information about their current services and offerings.'
            )
        );
    }

    return $schema;
}

/**
 * Validate WebPage schema with review content
 *
 * @param array $schema The schema to validate
 * @return array The validated schema
 */
function spa_validate_review_page($schema) {
    // Ensure mainEntity is present
    if (!isset($schema['mainEntity']) || !is_array($schema['mainEntity'])) {
        return $schema; // Cannot fix without a main entity
    }

    // Ensure mainEntity has required properties
    if (!isset($schema['mainEntity']['@type'])) {
        $schema['mainEntity']['@type'] = 'HealthAndBeautyBusiness';
    }

    if (!isset($schema['mainEntity']['name']) || empty($schema['mainEntity']['name'])) {
        $schema['mainEntity']['name'] = 'Spa in Barcelona';
    }

    // Ensure about property is present
    if (!isset($schema['about']) || !is_array($schema['about'])) {
        if (isset($schema['mainEntity']['name'])) {
            $schema['about'] = array(
                '@type' => 'HealthAndBeautyBusiness',
                'name' => $schema['mainEntity']['name']
            );
        }
    }

    // Ensure reviews are properly formatted
    if (isset($schema['mainEntity']['review'])) {
        foreach ($schema['mainEntity']['review'] as $index => $review) {
            if (!isset($review['@type']) || $review['@type'] !== 'Review') {
                $schema['mainEntity']['review'][$index]['@type'] = 'Review';
            }

            // Ensure author is present
            if (!isset($review['author']) || !is_array($review['author'])) {
                $schema['mainEntity']['review'][$index]['author'] = array(
                    '@type' => 'Person',
                    'name' => 'Customer'
                );
            } else if (!isset($review['author']['@type'])) {
                $schema['mainEntity']['review'][$index]['author']['@type'] = 'Person';
            }

            // Ensure reviewRating is present
            if (!isset($review['reviewRating']) || !is_array($review['reviewRating'])) {
                $schema['mainEntity']['review'][$index]['reviewRating'] = array(
                    '@type' => 'Rating',
                    'ratingValue' => 5,
                    'bestRating' => 5,
                    'worstRating' => 1
                );
            } else if (!isset($review['reviewRating']['@type'])) {
                $schema['mainEntity']['review'][$index]['reviewRating']['@type'] = 'Rating';
            }
        }
    }

    return $schema;
}

/**
 * Validate WebPage schema
 *
 * @param array $schema The schema to validate
 * @return array The validated schema
 */
function spa_validate_webpage($schema) {
    // Ensure URL is present and valid
    if (!isset($schema['url']) || empty($schema['url']) || $schema['url'] === null) {
        // Try to get the URL from the current post
        global $post;
        if ($post && 'spa' === $post->post_type) {
            $schema['url'] = get_permalink($post->ID);
        } else {
            // Fallback to home URL
            $schema['url'] = home_url();
        }
    }

    // Ensure name is properly formatted and never contains "Preparation"
    if (!isset($schema['name']) || empty($schema['name']) || strpos($schema['name'], 'Preparation') !== false) {
        // Try to get the name from the current post
        global $post;
        if ($post && 'spa' === $post->post_type) {
            $schema['name'] = get_the_title($post->ID) . ' | Spas in Barcelona';
        } else {
            // Fallback to generic name
            $schema['name'] = 'Spa Directory Barcelona';
        }
    }

    // Ensure @id is present
    if (!isset($schema['@id']) || empty($schema['@id'])) {
        $schema['@id'] = $schema['url'] . '#webpage';
    }

    // Ensure mainEntity reference is present
    if (!isset($schema['mainEntity']) || empty($schema['mainEntity'])) {
        // Use permalink_url instead of url for consistency
        $permalink_url = $schema['url'];
        $schema['mainEntity'] = array('@id' => $permalink_url . '#business');
    }

    // Ensure isPartOf reference is present
    if (!isset($schema['isPartOf']) || empty($schema['isPartOf'])) {
        $schema['isPartOf'] = array('@id' => home_url() . '#website');
    }

    return $schema;
}

/**
 * Validate BreadcrumbList schema
 *
 * @param array $schema The schema to validate
 * @return array The validated schema
 */
function spa_validate_breadcrumb_list($schema) {
    // Ensure itemListElement is present
    if (!isset($schema['itemListElement']) || !is_array($schema['itemListElement'])) {
        $schema['itemListElement'] = array(
            array(
                '@type' => 'ListItem',
                'position' => 1,
                'name' => 'Home',
                'item' => home_url()
            )
        );
    }

    // Validate each list item
    foreach ($schema['itemListElement'] as $index => $item) {
        if (!isset($item['@type']) || $item['@type'] !== 'ListItem') {
            $schema['itemListElement'][$index]['@type'] = 'ListItem';
        }

        // Ensure position is present and numeric
        if (!isset($item['position']) || !is_numeric($item['position'])) {
            $schema['itemListElement'][$index]['position'] = $index + 1;
        } else {
            $schema['itemListElement'][$index]['position'] = intval($item['position']);
        }

        // Ensure name is present
        if (!isset($item['name']) || empty($item['name'])) {
            $schema['itemListElement'][$index]['name'] = 'Item ' . ($index + 1);
        }

        // Ensure item URL is present and valid
        if (!isset($item['item']) || empty($item['item']) || !filter_var($item['item'], FILTER_VALIDATE_URL)) {
            $schema['itemListElement'][$index]['item'] = home_url();
        }
    }

    // Special validation for the last item in breadcrumb on spa pages
    if (count($schema['itemListElement']) >= 3 && is_singular('spa')) {
        $last_index = count($schema['itemListElement']) - 1;
        $post_id = get_the_ID();

        // Always update the last item to ensure it's correct
        // Get the correct spa name and URL
        $spa_name = get_the_title($post_id);
        $spa_url = get_permalink($post_id);

        // Update the last breadcrumb item
        $schema['itemListElement'][$last_index]['name'] = $spa_name;
        $schema['itemListElement'][$last_index]['item'] = $spa_url;

        // Remove @context from all items to prevent issues
        foreach ($schema['itemListElement'] as $index => $item) {
            if (isset($schema['itemListElement'][$index]['@context'])) {
                unset($schema['itemListElement'][$index]['@context']);
            }
        }
    }

    return $schema;
}

/**
 * Helper function to remove redundant @context entries from nested objects
 *
 * @param array $schema The schema to process
 * @return array The schema with redundant @context entries removed
 */
function spa_remove_nested_context($schema) {
    if (!is_array($schema)) {
        return $schema;
    }

    // Process all keys in the schema
    foreach ($schema as $key => $value) {
        // Skip the root level @context (only keep @context at the top level of the entire schema)
        if ($key === '@context') {
            // If this is not the root level (has @type), remove the @context
            if (isset($schema['@type'])) {
                unset($schema[$key]);
            }
            // Otherwise keep it (it's the root level @context)
            continue;
        }

        // Process nested arrays
        if (is_array($value)) {
            // Process nested objects with @type
            if (isset($value['@type'])) {
                // Always remove @context from any object with @type
                if (isset($value['@context'])) {
                    unset($schema[$key]['@context']);
                }
                // Recursively process this object
                $schema[$key] = spa_remove_nested_context($value);
            }
            // Process arrays of objects (numeric keys)
            else if (is_numeric(key($value)) || empty($value)) {
                foreach ($value as $index => $item) {
                    if (is_array($item)) {
                        // Remove @context from array items
                        if (isset($item['@context'])) {
                            unset($schema[$key][$index]['@context']);
                        }
                        // Recursively process this item
                        $schema[$key][$index] = spa_remove_nested_context($item);
                    }
                }
            }
            // Process other nested arrays that might contain @context
            else {
                $schema[$key] = spa_remove_nested_context($value);
            }
        }
    }

    // Special handling for FAQPage entity
    if (isset($schema['@type']) && $schema['@type'] === 'FAQPage' && isset($schema['mainEntity']) && is_array($schema['mainEntity'])) {
        foreach ($schema['mainEntity'] as $index => $question) {
            // Remove @context from questions
            if (isset($question['@context'])) {
                unset($schema['mainEntity'][$index]['@context']);
            }

            // Remove @context from answers
            if (isset($question['acceptedAnswer']) && isset($question['acceptedAnswer']['@context'])) {
                unset($schema['mainEntity'][$index]['acceptedAnswer']['@context']);
            }
        }
    }

    // Special handling for BreadcrumbList entity
    if (isset($schema['@type']) && $schema['@type'] === 'BreadcrumbList' && isset($schema['itemListElement']) && is_array($schema['itemListElement'])) {
        foreach ($schema['itemListElement'] as $index => $item) {
            // Remove @context from breadcrumb items
            if (isset($item['@context'])) {
                unset($schema['itemListElement'][$index]['@context']);
            }

            // Ensure the last item is not "Preparation" or "Visit Preparation" on spa pages
            if (is_singular('spa') && $index === count($schema['itemListElement']) - 1) {
                if ($item['name'] === 'Preparation' || $item['name'] === 'Visit Preparation' || $item['item'] === home_url()) {
                    $post_id = get_the_ID();
                    $schema['itemListElement'][$index]['name'] = get_the_title($post_id);
                    $schema['itemListElement'][$index]['item'] = get_permalink($post_id);
                }
            }
        }
    }

    // Special handling for @graph structure
    if (isset($schema['@graph']) && is_array($schema['@graph'])) {
        foreach ($schema['@graph'] as $index => $entity) {
            // Remove @context from all graph entities
            if (isset($entity['@context'])) {
                unset($schema['@graph'][$index]['@context']);
            }

            // Recursively process each entity in the graph
            $schema['@graph'][$index] = spa_remove_nested_context($entity);
        }
    }

    return $schema;
}

/**
 * Final verification function to ensure no nested @context entries remain in the schema
 * This is a safety check that runs after all other processing
 *
 * @param array $schema The schema to verify
 * @return array The schema with any remaining @context entries removed
 */
function spa_verify_no_nested_context($schema) {
    if (!is_array($schema)) {
        return $schema;
    }

    // Keep track of whether we're at the root level
    $is_root = !isset($schema['@type']);

    // Process all keys in the schema
    foreach ($schema as $key => $value) {
        // Handle @context - only keep it at the root level
        if ($key === '@context' && !$is_root) {
            unset($schema[$key]);
            continue;
        }

        // Process nested arrays
        if (is_array($value)) {
            // For @graph structure, process each entity
            if ($key === '@graph' && is_array($value)) {
                foreach ($value as $index => $entity) {
                    if (is_array($entity)) {
                        // Remove @context from all graph entities
                        if (isset($entity['@context'])) {
                            unset($schema[$key][$index]['@context']);
                        }
                        // Recursively process each entity
                        $schema[$key][$index] = spa_verify_no_nested_context($entity);
                    }
                }
            }
            // For other arrays, process recursively
            else {
                $schema[$key] = spa_verify_no_nested_context($value);
            }
        }
    }

    return $schema;
}