/**
 * Single spa page styles for Spas in Barcelona theme
 */

/* Spa Single */
.spa-single {
    margin-bottom: 80px;
    position: relative;
}

/* Override any hover effects from other stylesheets */
.spa-single article,
.spa-single .spa-section-content,
.spa-single section,
.spa-single .spa-services,
.spa-single .spa-amenities,
.spa-single .spa-package,
.spa-single .spa-review-source,
.spa-single .spa-review,
.spa-single .spa-hours,
.spa-single .spa-contact-info,
.spa-single .spa-social,
.spa-single .spa-service-item,
.spa-single .spa-amenity-item {
    transform: none !important;
    transition: none !important;
    box-shadow: none !important;
}

.spa-single article:hover,
.spa-single .spa-section-content:hover,
.spa-single section:hover,
.spa-single .spa-services:hover,
.spa-single .spa-amenities:hover,
.spa-single .spa-package:hover,
.spa-single .spa-review-source:hover,
.spa-single .spa-review:hover,
.spa-single .spa-hours:hover,
.spa-single .spa-contact-info:hover,
.spa-single .spa-social:hover,
.spa-single .spa-service-item:hover,
.spa-single .spa-amenity-item:hover {
    transform: none !important;
    box-shadow: none !important;
    border-color: rgba(210, 180, 140, 0.2) !important;
}

/* Ensure gallery links don't have default link styling */
.spa-gallery-item,
.spa-gallery a {
    text-decoration: none !important;
    color: inherit !important;
    background-color: transparent !important;
    outline: none !important;
}

/* Override any theme styles that might affect gallery links */
.spa-gallery a:hover,
.spa-gallery a:focus,
.spa-gallery a:active {
    text-decoration: none !important;
    color: inherit !important;
    background-color: transparent !important;
}

/* Force gallery items to use our custom styling */
.spa-gallery-grid a.spa-gallery-item {
    display: block !important;
    background-color: #ffffff !important;
    border: 1px solid rgba(210, 180, 140, 0.15) !important;
    border-radius: 12px !important;
    overflow: hidden !important;
    transition: all 0.4s ease !important;
    height: 100% !important;
    width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08) !important;
    position: relative !important;
}

.spa-gallery-grid a.spa-gallery-item::after {
    content: '' !important;
    position: absolute !important;
    bottom: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 3px !important;
    background: linear-gradient(90deg, rgba(210, 180, 140, 0.7), rgba(210, 180, 140, 0.3)) !important;
    opacity: 0.6 !important;
    transition: all 0.4s ease !important;
}

.spa-gallery-grid a.spa-gallery-item:hover {
    transform: translateY(-5px) !important;
    box-shadow: 0 15px 35px rgba(210, 180, 140, 0.25) !important;
    border-color: rgba(210, 180, 140, 0.4) !important;
}

.spa-gallery-grid a.spa-gallery-item:hover::after {
    opacity: 1 !important;
    height: 4px !important;
}

/* Page Header - Similar to category pages */
.spa-single .page-header {
    text-align: center;
    margin-bottom: 50px;
    padding: 60px 0;
    background-color: var(--light-color);
    border-bottom: 1px solid var(--border-color);
    position: relative;
    overflow: hidden;
}

.spa-single .page-header:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('data:image/svg+xml;utf8,<svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"><path d="M0 20 L20 0 L40 20 L20 40 Z" fill="none" stroke="rgba(104, 160, 160, 0.05)" stroke-width="1"/></svg>');
    opacity: 0.5;
    pointer-events: none;
}

.spa-single .page-title {
    font-size: 2.8rem;
    margin-bottom: 18px;
    color: var(--dark-color);
    letter-spacing: 0.02em;
    line-height: 1.2;
    font-weight: 600;
    text-align: center;
}

.spa-single .archive-description {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
}

.spa-location {
    display: flex;
    align-items: center;
    margin-bottom: 18px;
    color: #777;
    letter-spacing: 0.01em;
    font-size: 1.05rem;
    justify-content: center;
}

.spa-location i {
    margin-right: 8px;
    color: var(--primary-color);
}

.spa-rating {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    justify-content: center;
}

/* Custom styling for the header rating badge */
.spa-header-rating-badge {
    display: flex;
    align-items: center;
    justify-content: center;
}

.spa-header-rating-number {
    color: #333;
    font-size: 1.5rem;
    font-weight: 700;
}

.spa-header-rating-star {
    color: #FFD700;
    font-size: 1.3rem;
    margin-left: 5px;
}

.spa-review-count {
    font-size: 0.9rem;
    color: var(--secondary-color);
    margin-left: 8px;
}

.spa-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 15px;
    justify-content: center;
}

.spa-tag {
    background-color: var(--secondary-color);
    color: #fff;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    letter-spacing: 0.02em;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    transition: var(--transition);
}

.spa-tag:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
}

.spa-cta {
    margin-top: 20px;
}

.spa-cta-button {
    display: inline-flex;
    align-items: center;
    background-color: var(--primary-color);
    color: #fff;
    padding: 14px 28px;
    border-radius: 30px;
    font-weight: 500;
    transition: var(--transition);
    letter-spacing: 0.02em;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
}

.spa-cta-button:hover {
    background-color: var(--secondary-color);
    color: #fff;
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.08);
}

.spa-cta-button i {
    margin-left: 8px;
}

/* Spa Gallery */
.spa-gallery {
    margin-bottom: 40px;
    position: relative;
    overflow: hidden;
    max-width: 1200px;
    margin: 0 auto 40px;
    padding: 0 20px;
    background-color: rgba(245, 245, 240, 0.5);
    border-radius: 16px;
    padding: 30px 20px;
    position: relative;
}

.spa-gallery::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('data:image/svg+xml;utf8,<svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"><path d="M0 20 L20 0 L40 20 L20 40 Z" fill="none" stroke="rgba(210, 180, 140, 0.1)" stroke-width="1"/></svg>');
    opacity: 0.5;
    pointer-events: none;
    z-index: 0;
}

.spa-gallery-grid {
    position: relative;
    z-index: 1;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-auto-rows: 250px;
    gap: 15px;
    overflow: hidden;
    background-color: transparent;
    max-width: 100%;
}

.spa-gallery-item {
    position: relative;
    overflow: hidden;
    height: 100%;
    border-radius: 12px;
    background-color: #ffffff; /* Changed from #000 to white */
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    transition: all 0.4s ease;
    border: 1px solid rgba(210, 180, 140, 0.1); /* Subtle gold border */
    text-decoration: none !important; /* Remove underline from links */
    display: block; /* Ensure proper display */
    color: inherit; /* Prevent blue text color */
}

.spa-gallery-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 30px rgba(210, 180, 140, 0.2); /* Gold-tinted shadow */
    border-color: rgba(210, 180, 140, 0.3); /* Enhanced border on hover */
}

.spa-gallery-item img {
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
    filter: brightness(1.02) saturate(1.05);
    transition: all 0.6s ease !important;
    display: block !important;
    border-radius: 11px !important; /* Slightly smaller than container to account for border */
    margin: 0 !important;
    padding: 0 !important;
    max-width: none !important;
    max-height: none !important;
    position: relative !important;
}

.spa-gallery-item::before {
    content: '' !important;
    position: absolute !important;
    top: -100% !important;
    left: -100% !important;
    width: 120% !important;
    height: 120% !important;
    background: linear-gradient(
        45deg,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 0) 40%,
        rgba(255, 255, 255, 0.3) 50%,
        rgba(255, 255, 255, 0) 60%,
        rgba(255, 255, 255, 0) 100%
    ) !important;
    z-index: 2 !important;
    transform: rotate(25deg) !important;
    transition: all 1.2s cubic-bezier(0.19, 1, 0.22, 1) !important;
    pointer-events: none !important;
    opacity: 0 !important;
}

.spa-gallery-item:hover::before {
    top: 100% !important;
    left: 100% !important;
    opacity: 1 !important;
}

.spa-gallery-item:hover img {
    filter: brightness(1.05) saturate(1.1) !important; /* Slightly brighter and more vibrant on hover */
    transform: scale(1.03) !important; /* Subtle zoom effect */
}

.spa-gallery-item-overlay {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.4)) !important; /* Slightly darker gradient */
    opacity: 0 !important;
    transition: all 0.5s ease !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border-radius: 11px !important;
}

.spa-gallery-item:hover .spa-gallery-item-overlay {
    opacity: 1 !important;
}

.spa-gallery-zoom {
    width: 55px !important;
    height: 55px !important;
    background: linear-gradient(145deg, #ffffff, #f8f8f5) !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    color: #d2b48c !important; /* Tan/gold color for icon */
    font-size: 1.2rem !important;
    transform: scale(0.8) translateY(10px) !important;
    opacity: 0 !important;
    transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275) !important; /* Bouncy animation */
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2), inset 0 1px 1px rgba(255, 255, 255, 0.8) !important;
    border: 1px solid rgba(210, 180, 140, 0.4) !important;
    position: relative !important;
    overflow: hidden !important;
}

.spa-gallery-zoom::before {
    content: '' !important;
    position: absolute !important;
    top: -50% !important;
    left: -50% !important;
    width: 200% !important;
    height: 200% !important;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%) !important;
    opacity: 0 !important;
    transition: opacity 0.5s ease !important;
}

.spa-gallery-item:hover .spa-gallery-zoom {
    transform: scale(1) translateY(0) !important;
    opacity: 1 !important;
}

.spa-gallery-item:hover .spa-gallery-zoom::before {
    opacity: 1 !important;
}

.spa-gallery-zoom i {
    transition: all 0.3s ease !important;
    position: relative !important;
    z-index: 2 !important;
}

.spa-gallery-item:hover .spa-gallery-zoom i {
    transform: scale(1.1) !important;
    color: #b8860b !important; /* Darker gold color on hover */
}

/* Spa Content Wrapper */
.spa-content-wrapper {
    display: block;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Section Navigation */
.spa-section-nav {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 30px;
    background-color: var(--light-color);
    padding: 20px;
    border-radius: 12px;
    box-shadow: var(--box-shadow);
    border: 1px solid rgba(210, 180, 140, 0.2);
    position: sticky;
    top: 80px;
    z-index: 10;
    justify-content: center;
}

.spa-section-nav-link {
    padding: 10px 20px;
    background-color: var(--light-color);
    color: var(--dark-color);
    border-radius: 30px;
    font-weight: 500;
    transition: all 0.3s ease;
    font-size: 0.95rem;
    border: 1px solid rgba(143, 185, 170, 0.2);
    text-align: center;
}

.spa-section-nav-link:hover,
.spa-section-nav-link.active {
    background-color: var(--accent-color);
    color: #fff;
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(143, 185, 170, 0.2);
}

/* Spa Sections */
.spa-section-content {
    margin-bottom: 50px;
    padding: 0;
}

/* Override any potential white backgrounds */
.spa-single,
.spa-single article,
.spa-single .inside-article,
.spa-single .site-content,
.spa-single #page,
.spa-single .site,
.spa-single .content-area,
.spa-single #primary,
.spa-single .site-main {
    background-color: #f5f5f0 !important;
}

/* Card Container Styling */
.spa-card-container {
    background-color: #ffffff;
    border-radius: 15px;
    padding: 35px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
    margin-bottom: 30px;
    border: 1px solid rgba(210, 180, 140, 0.1);
    position: relative;
    overflow: hidden;
    margin-left: -20px;
    margin-right: -20px;
    width: calc(100% + 40px);
}

.spa-card-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 5px;
    height: 100%;
    background-color: var(--primary-color);
    opacity: 0.7;
}

.spa-section-title {
    font-size: 1.8rem;
    margin-bottom: 25px;
    color: var(--dark-color);
    position: relative;
    padding-bottom: 15px;
    font-family: var(--font-secondary);
    border-bottom: 1px solid rgba(210, 180, 140, 0.2);
}

.spa-section-title::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 60px;
    height: 3px;
    background-color: var(--primary-color);
    border-radius: 3px;
}

.spa-subsection-title {
    font-size: 1.4rem;
    margin-bottom: 20px;
    margin-top: 0;
    color: var(--dark-color);
    font-family: var(--font-secondary);
    position: relative;
    padding-bottom: 10px;
}

.spa-subsection-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40px;
    height: 2px;
    background-color: var(--primary-color);
    border-radius: 2px;
    transition: width 0.3s ease;
}

.spa-hours:hover .spa-subsection-title::after,
.spa-contact-info:hover .spa-subsection-title::after,
.spa-social:hover .spa-subsection-title::after,
.spa-transportation:hover .spa-subsection-title::after,
.spa-accessibility:hover .spa-subsection-title::after {
    width: 60px;
    background-color: var(--secondary-color);
}

.spa-no-content {
    color: #777;
    font-style: italic;
    padding: 15px 0;
}

.spa-tab-content h2 {
    font-size: 1.8rem;
    margin-bottom: 20px;
}

.spa-tab-content h3 {
    font-size: 1.4rem;
    margin-bottom: 15px;
    margin-top: 30px;
}

.spa-description {
    margin-bottom: 30px;
    line-height: 1.7;
}

.spa-contact-text {
    margin-top: 25px;
    padding-top: 20px;
    border-top: 1px solid rgba(210, 180, 140, 0.1);
    font-size: 1.05rem;
    line-height: 1.8;
    color: #3a4a4a;
}

.spa-contact-text p {
    margin: 8px 0;
    display: flex;
    align-items: center;
}

.spa-contact-text i {
    color: #68a0a0;
    margin-right: 12px;
    font-size: 1.1rem;
    width: 20px;
    text-align: center;
}

/* Services & Amenities */
.spa-services-amenities {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 25px;
}

.spa-services {
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 20px;
    background-color: rgba(245, 245, 240, 0.7);
}

.spa-amenities {
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 20px;
    background-color: rgba(245, 245, 240, 0.7);
}

/* Services specific styling */
.spa-services {
    display: flex;
    flex-direction: column;
}

.spa-services .spa-subsection-title {
    display: block;
    width: 100%;
    margin-bottom: 20px;
    position: relative;
    order: -1; /* This ensures the title appears first */
    font-family: var(--font-secondary);
    color: var(--dark-color);
}

.spa-services-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.spa-service-item {
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 10px;
    background-color: #ffffff;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.03);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(210, 180, 140, 0.1);
}



.spa-service-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(104, 160, 160, 0.15);
    border-color: var(--primary-color);
}



.spa-service-title {
    margin: 0;
    font-size: 1rem;
    color: var(--dark-color);
    font-weight: 500;
    font-family: var(--font-primary);
    display: flex;
    align-items: center;
}

.spa-service-title i {
    margin-right: 15px;
    color: #68a0a0;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    background-color: rgba(104, 160, 160, 0.1);
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    box-shadow: 0 3px 8px rgba(104, 160, 160, 0.1);
}

.spa-service-item:hover .spa-service-title i {
    color: #ffffff;
    background-color: #68a0a0;
    transform: scale(1.1);
}

/* Amenities styling */
.spa-amenities {
    display: flex;
    flex-direction: column;
}

.spa-amenities .spa-subsection-title {
    display: block;
    width: 100%;
    margin-bottom: 20px;
    position: relative;
    order: -1; /* This ensures the title appears first */
    font-family: var(--font-secondary);
    color: var(--dark-color);
}

.spa-amenities-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.spa-amenity-item {
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 10px;
    background-color: #ffffff;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.03);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(210, 180, 140, 0.1);
}



.spa-amenity-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(104, 160, 160, 0.15);
    border-color: var(--primary-color);
}



.spa-amenity-title {
    margin: 0;
    font-size: 1rem;
    color: var(--dark-color);
    font-weight: 500;
    display: flex;
    align-items: center;
    font-family: var(--font-primary);
}

.spa-amenity-title i {
    margin-right: 15px;
    color: #68a0a0;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    background-color: rgba(104, 160, 160, 0.1);
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    box-shadow: 0 3px 8px rgba(104, 160, 160, 0.1);
}

.spa-amenity-item:hover .spa-amenity-title i {
    color: #ffffff;
    background-color: #68a0a0;
    transform: scale(1.1);
}

/* Specialties list styling */
.spa-specialties {
    margin-top: 10px;
    background-color: rgba(245, 245, 240, 0.7);
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 20px;
}

.spa-specialties-list {
    list-style: none;
    padding: 0;
    margin: 0;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 15px;
}

.spa-specialties-list li {
    background-color: #ffffff;
    border-radius: 10px;
    padding: 15px;
    display: flex;
    align-items: center;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.03);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(210, 180, 140, 0.1);
}



.spa-specialties-list li:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(104, 160, 160, 0.15);
    border-color: var(--primary-color);
}



.spa-specialties-list li i {
    margin-right: 15px;
    color: #68a0a0;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    background-color: rgba(104, 160, 160, 0.1);
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    box-shadow: 0 3px 8px rgba(104, 160, 160, 0.1);
}

.spa-specialties-list li:hover i {
    color: #ffffff;
    background-color: #68a0a0;
    transform: scale(1.1);
}

.spa-specialties-list li span {
    font-family: var(--font-primary);
    font-weight: 500;
    color: var(--dark-color);
    transition: all 0.3s ease;
}

/* Packages */
.spa-packages {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 25px;
    margin-top: 30px;
}

.spa-package {
    background-color: #ffffff;
    border-radius: 12px;
    overflow: hidden;
    margin-bottom: 20px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    display: flex;
    flex-direction: column;
    border: 1px solid rgba(210, 180, 140, 0.1);
    transition: all 0.3s ease;
    position: relative;
}

.spa-package:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(104, 160, 160, 0.15);
}

.spa-package::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 3px;
    background-color: var(--secondary-color);
    transition: all 0.4s ease;
}

.spa-package:hover::after {
    width: 100%;
}

.spa-package-title {
    font-size: 1.2rem;
    margin: 0;
    padding: 15px 20px;
    color: #ffffff;
    font-weight: 500;
    background-color: #68a0a0;
    font-family: var(--font-secondary);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.spa-package-title::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: all 0.6s ease;
}

.spa-package:hover .spa-package-title::before {
    left: 100%;
}

.spa-package-description {
    font-size: 0.95rem;
    color: #666;
    padding: 20px;
    line-height: 1.6;
    flex-grow: 1;
    background-color: #ffffff;
    transition: all 0.3s ease;
}

.spa-package:hover .spa-package-description {
    color: #333;
}

.spa-package-details {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 10px;
    background-color: rgba(245, 245, 240, 0.7);
    padding: 15px 20px;
    margin-top: auto;
    transition: all 0.3s ease;
}

.spa-package:hover .spa-package-details {
    background-color: rgba(245, 245, 240, 0.9);
}

.spa-package-price, .spa-package-duration {
    display: flex;
    align-items: center;
    font-size: 0.95rem;
    color: var(--dark-color);
    font-weight: 500;
    transition: all 0.3s ease;
}

.spa-package-price i, .spa-package-duration i {
    margin-right: 8px;
    color: var(--primary-color);
    transition: all 0.3s ease;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: rgba(104, 160, 160, 0.1);
}

.spa-package:hover .spa-package-price i,
.spa-package:hover .spa-package-duration i {
    color: #ffffff;
    background-color: var(--primary-color);
    transform: scale(1.1);
}

/* Reviews */
.spa-review-sources {
    margin-bottom: 40px;
}

.spa-review-sources-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 25px;
    margin-top: 30px;
}

.spa-review-source {
    background: linear-gradient(135deg, #ffffff 0%, #f8f8f5 100%);
    border-radius: 16px;
    padding: 30px 20px;
    text-align: center;
    border: 1px solid rgba(210, 180, 140, 0.15);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
}

.spa-review-source::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    opacity: 0.8;
    transition: all 0.4s ease;
}

.spa-review-source:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 35px rgba(104, 160, 160, 0.2);
    border-color: rgba(210, 180, 140, 0.3);
}

.spa-review-source:hover::before {
    height: 8px;
    opacity: 1;
}

.spa-review-source-name {
    font-weight: 600;
    margin-bottom: 20px;
    color: var(--dark-color);
    font-size: 1.3rem;
    font-family: var(--font-secondary);
    position: relative;
    padding-bottom: 12px;
    display: inline-block;
    letter-spacing: 0.02em;
}

.spa-review-source-name:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: 3px;
    transition: all 0.3s ease;
}

.spa-review-source:hover .spa-review-source-name:after {
    width: 70px;
}

.spa-review-source-rating {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
}

/* Elegant Star Rating Styling */
.star-rating {
    display: inline-block;
    margin-bottom: 5px;
    transition: all 0.3s ease;
    font-size: 1.5rem;
    letter-spacing: 3px;
    line-height: 1;
}

/* Rating Badge Style (matching spa cards) */
.spa-rating-badge-stars {
    font-size: 1.5rem;
    font-weight: 800;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.8);
    letter-spacing: 0.05em;
}

/* New Rating Badge Styles for Review Sources */
.spa-rating-badge-new {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px 16px;
    border-radius: 12px;
    background-color: #333333;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    min-width: 90px;
}

/* Source-specific badge colors */
.google-badge {
    background-color: #4b5320;
}

.tripadvisor-badge {
    background-color: #333333;
}

.yelp-badge {
    background-color: #333333;
}

.booking-badge {
    background-color: #333333;
}

.trustpilot-badge {
    background-color: #333333;
}

.default-badge {
    background-color: #333333;
}

.rating-number-new {
    color: #ffffff;
    font-size: 1.8rem;
    font-weight: 700;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.star-icon-new {
    color: #FFD700;
    font-size: 1.8rem;
    margin-left: 8px;
    display: inline-block;
    text-shadow: 0 0 8px rgba(255, 215, 0, 0.5);
}

/* Header Rating Badge Style */
.spa-rating-badge-header {
    display: flex;
    align-items: center;
    justify-content: center;
}

.rating-number-header {
    color: #333333;
    font-size: 1.8rem;
    font-weight: 700;
}

.star-icon-header {
    color: #FFD700;
    font-size: 1.8rem;
    margin-left: 8px;
    display: inline-block;
    text-shadow: 0 0 8px rgba(255, 215, 0, 0.5);
}

/* Original Rating Badge Style */
.spa-review-source .spa-rating-badge {
    position: relative;
    background: linear-gradient(145deg, #B8860B, #DAA520);
    color: #ffffff; /* Ensure white text */
    padding: 14px 22px; /* Further increased padding for taller badge */
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(184, 134, 11, 0.4), inset 0 1px 1px rgba(255, 255, 255, 0.3);
    border: 2px solid #FFD700;
    transition: all 0.3s ease;
    animation: badgePulse 2s infinite;
    z-index: 1;
    overflow: hidden;
}

@keyframes badgePulse {
    0% {
        box-shadow: 0 4px 15px rgba(184, 134, 11, 0.4);
        border-color: #FFD700;
    }
    50% {
        box-shadow: 0 8px 25px rgba(255, 215, 0, 0.6);
        border-color: #FFDF00;
    }
    100% {
        box-shadow: 0 4px 15px rgba(184, 134, 11, 0.4);
        border-color: #FFD700;
    }
}

@keyframes rotateGradient {
    0% {
        background-position: 0% 0%;
    }
    100% {
        background-position: 200% 0%;
    }
}

.rating-number {
    color: #ffffff;
    font-size: 1.8rem;
    font-weight: 900;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
    letter-spacing: 0.05em;
}

.star-icon {
    color: #FFFFFF; /* White star */
    font-size: 1.8rem;
    margin-left: 8px;
    display: inline-block;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
    transform: translateY(-1px);
}

@keyframes starGlow {
    0% { opacity: 0.3; transform: scale(1); }
    100% { opacity: 0.7; transform: scale(1.2); }
}

.spa-review-source:hover .spa-rating-badge {
    transform: scale(1.05);
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.6);
    background: linear-gradient(145deg, #DAA520, #CD853F);
    border-color: #FFDF00;
    animation: none;
    position: relative;
    overflow: hidden;
}

.spa-review-source .spa-rating-badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.3), transparent);
    transition: all 0.6s ease;
    z-index: 1;
}

.spa-review-source:hover .spa-rating-badge::before {
    left: 100%;
}



/* Original FontAwesome star styling for other places */
.star-rating i {
    color: #d2b48c;
    text-shadow: 0 2px 4px rgba(210, 180, 140, 0.4);
    transition: all 0.3s ease;
}

.star-rating .fa-star,
.star-rating .fa-star-half-alt {
    position: relative;
}

.spa-review-source:hover .star-rating i {
    color: #d4af37;
    text-shadow: 0 3px 6px rgba(212, 175, 55, 0.5);
}

.spa-review-source-count {
    font-size: 1rem;
    color: #555;
    margin-top: 5px;
    background-color: rgba(104, 160, 160, 0.1);
    padding: 8px 16px;
    border-radius: 30px;
    font-weight: 600;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.spa-review-source:hover .spa-review-source-count {
    background-color: rgba(104, 160, 160, 0.2);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    color: #333;
}

.spa-featured-reviews {
    margin-top: 40px;
}

.spa-featured-reviews-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 30px;
    align-items: stretch;
    justify-content: center;
}

.spa-review {
    background-color: #f9f9f7;
    border-radius: 12px;
    padding: 25px;
    border: 1px solid rgba(210, 180, 140, 0.1);
    position: relative;
    margin-bottom: 0;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    overflow: visible;
    display: flex;
    flex-direction: column;
    transition: all 0.3s ease;
    height: 100%;
    justify-content: space-between;
    max-width: 400px;
    margin: 0 auto;
    border-left: 4px solid var(--primary-color);
}

.spa-review:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(104, 160, 160, 0.1);
    border-color: rgba(210, 180, 140, 0.2);
}

.spa-review-content {
    font-size: 1rem;
    line-height: 1.7;
    font-style: italic;
    position: relative;
    z-index: 1;
    margin-bottom: 20px;
    flex-grow: 1;
    min-height: 100px;
    padding: 0 0 15px 0;
    border-bottom: 1px solid rgba(210, 180, 140, 0.1);
    color: #68705e;
}

.spa-review-footer {
    margin-top: 15px;
    position: relative;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.spa-review-author-container {
    display: flex;
    align-items: center;
}

.spa-review-author-avatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: rgba(104, 160, 160, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
    color: var(--primary-color);
    font-size: 0.8rem;
}

.spa-review-author {
    font-weight: 600;
    color: #68705e;
    font-size: 0.95rem;
    font-family: var(--font-secondary);
}

.spa-review-date {
    font-size: 0.85rem;
    color: #888;
    display: flex;
    align-items: center;
}

.spa-review-date i {
    margin-right: 6px;
    color: var(--primary-color);
    font-size: 0.85rem;
}

.spa-review-rating {
    margin-top: 5px;
}

.spa-rating-stars {
    display: flex;
    align-items: center;
    gap: 3px;
}

.spa-rating-stars i {
    color: #FFD700;
    font-size: 1rem;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.spa-rating-stars .fa-star {
    color: #FFD700;
}

.spa-rating-stars .fa-star-half-alt {
    color: #FFD700;
}

.spa-rating-stars .fa-star.far {
    color: #d8d8d8;
}

.spa-rating-value {
    margin-left: 8px;
    font-weight: 600;
    color: #68705e;
    font-size: 0.9rem;
}

/* Featured review stars styling */
.featured-review-stars {
    display: inline-flex;
    align-items: center;
}

.featured-review-stars i {
    color: #d2b48c;
    font-size: 1.1rem;
    margin-right: 2px;
}

/* Rating with number */
.spa-rating-badge-stars {
    display: flex;
    align-items: center;
    color: #ffffff; /* Changed to white for better readability */
    font-size: 1.2rem;
    font-weight: 500;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3); /* Added text shadow for better contrast */
}

.spa-review-source {
    display: inline-block;
    font-weight: 600;
    font-size: 0.95rem;
    color: #555;
    text-decoration: none;
    margin-bottom: 4px;
}

/* About Section Details */
.spa-about-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 40px;
    padding-top: 30px;
    border-top: 1px solid rgba(210, 180, 140, 0.1);
}

/* Location Section */
.spa-location-details {
}

.spa-address {
    margin-bottom: 25px;
}

.spa-address-content {
    display: flex;
    align-items: flex-start;
    background-color: #ffffff;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(210, 180, 140, 0.1);
    transition: all 0.3s ease;
    margin-top: 15px;
}

.spa-address-content:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(104, 160, 160, 0.15);
    border-color: var(--primary-color);
}

.spa-address-content i {
    margin-right: 15px;
    color: #68a0a0;
    font-size: 1.3rem;
    transition: all 0.3s ease;
    background-color: rgba(104, 160, 160, 0.1);
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    box-shadow: 0 3px 8px rgba(104, 160, 160, 0.1);
    flex-shrink: 0;
}

.spa-address-content:hover i {
    color: #ffffff;
    background-color: #68a0a0;
    transform: scale(1.1);
}

.spa-address-text {
    font-size: 1.05rem;
    line-height: 1.6;
    color: var(--dark-color);
}

/* How to Get There Section */
.spa-transportation-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 25px;
    margin-top: 25px;
    margin-bottom: 40px;
}

.spa-transport-card {
    background-color: #ffffff;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    border: 1px solid rgba(210, 180, 140, 0.1);
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding-bottom: 25px;
}

.spa-transport-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 30px rgba(104, 160, 160, 0.15);
    border-color: var(--primary-color);
}

.spa-transport-icon {
    width: 110px;
    height: 110px;
    background-color: rgba(104, 160, 160, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 25px auto 15px;
    transition: all 0.3s ease;
}

.spa-transport-icon i {
    font-size: 3.2rem;
    color: #68a0a0;
    transition: all 0.3s ease;
}

.spa-transport-card:hover .spa-transport-icon {
    background-color: #68a0a0;
    transform: scale(1.1);
    box-shadow: 0 10px 20px rgba(104, 160, 160, 0.2);
}

.spa-transport-card:hover .spa-transport-icon i {
    color: #ffffff;
}

.spa-transport-title {
    font-size: 1.3rem;
    margin: 0 0 15px;
    color: var(--dark-color);
    font-family: var(--font-secondary);
    font-weight: 600;
}

.spa-transport-details {
    color: #666;
    line-height: 1.6;
    padding: 0 20px;
    font-size: 0.95rem;
}

.spa-transportation-intro {
    margin-bottom: 25px;
    color: #666;
    line-height: 1.6;
}

.spa-transport-near-me {
    margin-top: 10px;
    font-size: 0.9rem;
    color: var(--primary-color);
    background-color: rgba(104, 160, 160, 0.05);
    padding: 8px 12px;
    border-radius: 6px;
    font-style: italic;
}

.spa-transport-near-me i {
    margin-right: 5px;
    color: var(--primary-color);
}

.spa-public-transport-link {
    margin-top: 25px;
    text-align: center;
}

.spa-transport-directions-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background-color: var(--secondary-color);
    color: #fff;
    padding: 12px 25px;
    border-radius: 30px;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    text-decoration: none;
}

.spa-transport-directions-button:hover {
    background-color: var(--primary-color);
    transform: translateY(-3px);
    box-shadow: 0 8px 15px rgba(104, 160, 160, 0.2);
    color: #fff;
}

.spa-transport-directions-button i {
    margin-right: 8px;
}

.spa-accessibility-list {
    margin-top: 25px;
}



.spa-hours, .spa-contact-info, .spa-social, .spa-transportation, .spa-accessibility {
    background-color: #ffffff;
    border-radius: 12px;
    padding: 25px;
    border: 1px solid rgba(210, 180, 140, 0.1);
    margin-bottom: 20px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}



.spa-hours:hover, .spa-contact-info:hover, .spa-social:hover, .spa-transportation:hover, .spa-accessibility:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(104, 160, 160, 0.15);
    border-color: var(--primary-color);
}



.spa-hours-display {
    background-color: #ffffff;
    padding: 15px 20px;
    border-radius: 10px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.03);
    line-height: 1.6;
    color: #666;
    font-weight: 500;
    border: 1px solid rgba(210, 180, 140, 0.1);
}

.spa-hours-list {
    list-style: none;
    padding: 0;
    margin: 0;
    background-color: rgba(255, 255, 255, 0.5);
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.03);
}

.spa-hours-list li {
    display: flex;
    padding: 12px 15px;
    border-bottom: 1px solid var(--border-color);
    transition: all 0.3s ease;
    background-color: #ffffff;
    justify-content: space-between;
    align-items: center;
}

.spa-hours-list li:nth-child(odd) {
    background-color: rgba(245, 245, 240, 0.5);
}

.spa-hours-list li:last-child {
    border-bottom: none;
}

.spa-hours-list li:hover {
    background-color: rgba(245, 245, 240, 0.9);
    transform: translateX(3px);
}

.spa-accessibility-list {
    list-style: none;
    padding: 0;
    margin: 0;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.spa-accessibility-list li {
    display: flex;
    align-items: center;
    padding: 15px;
    background-color: #ffffff;
    border-radius: 10px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.03);
    transition: all 0.3s ease;
    border: 1px solid rgba(210, 180, 140, 0.1);
}

.spa-accessibility-list li:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(104, 160, 160, 0.15);
    border-color: var(--primary-color);
}

.spa-accessibility-list li i {
    margin-right: 15px;
    color: #68a0a0;
    font-size: 1.4rem;
    transition: all 0.3s ease;
    background-color: rgba(104, 160, 160, 0.1);
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    box-shadow: 0 3px 8px rgba(104, 160, 160, 0.1);
    flex-shrink: 0;
}

.spa-accessibility-list li:hover i {
    color: #ffffff;
    background-color: #68a0a0;
    transform: scale(1.1);
}

.spa-accessibility-list li span {
    flex: 1;
    line-height: 1.5;
}

.spa-transport-type {
    font-weight: 600;
    color: var(--dark-color);
    margin-right: 8px;
    display: inline-block;
    margin-bottom: 5px;
}

.spa-transport-info {
    color: #666;
    display: block;
    padding-left: 47px;
    margin-top: 5px;
    line-height: 1.5;
}

/* Advice Section Styles */
.spa-advice-intro {
    margin-bottom: 25px;
    color: #666;
    line-height: 1.6;
}

.spa-advice-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.spa-advice-card {
    display: flex;
    align-items: flex-start;
    padding: 20px;
    background-color: #ffffff;
    border-radius: 10px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.03);
    transition: all 0.3s ease;
    border: 1px solid rgba(210, 180, 140, 0.1);
}

.spa-advice-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(104, 160, 160, 0.15);
    border-color: var(--primary-color);
}

.spa-advice-icon {
    margin-right: 15px;
    color: #f5a623;
    font-size: 1.4rem;
    transition: all 0.3s ease;
    background-color: rgba(245, 166, 35, 0.1);
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    box-shadow: 0 3px 8px rgba(245, 166, 35, 0.1);
    flex-shrink: 0;
}

.spa-advice-card:hover .spa-advice-icon {
    color: #ffffff;
    background-color: #f5a623;
    transform: scale(1.1);
}

.spa-advice-content {
    flex: 1;
    line-height: 1.5;
}

.spa-advice-footer {
    margin-top: 25px;
    padding: 15px;
    background-color: rgba(245, 245, 240, 0.7);
    border-radius: 8px;
    border-left: 3px solid #f5a623;
    font-style: italic;
    color: #555;
    line-height: 1.6;
}

.spa-advice-footer i {
    color: #f5a623;
    margin-right: 8px;
}

.spa-day {
    font-weight: 600;
    color: var(--dark-color);
    min-width: 80px;
    display: inline-block;
    position: relative;
}

.spa-day::after {
    content: '';
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background-color: rgba(104, 160, 160, 0.4);
}

.spa-time {
    color: #666;
    font-weight: 500;
    flex: 1;
    text-align: right;
}

.spa-time:empty::before {
    content: 'Hours not specified';
    color: #999;
    font-style: italic;
}



/* All contact icons use the same color */

.spa-social-links {
    display: flex;
    gap: 20px;
    margin-top: 20px;
    flex-wrap: wrap;
}

.spa-social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    border-radius: 16px;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    font-size: 1.5rem;
    color: #ffffff;
}

.spa-social-link:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.spa-social-link i {
    transition: all 0.3s ease;
}

.spa-social-link:hover i {
    transform: scale(1.2);
}

/* Platform-specific colors */
.spa-social-link.facebook {
    background-color: #3b5998;
}

.spa-social-link.instagram {
    background: linear-gradient(45deg, #833AB4, #FD1D1D, #FCAF45);
}

.spa-social-link.twitter, .spa-social-link.x {
    background-color: #1DA1F2;
}

.spa-social-link.pinterest {
    background-color: #E60023;
}

.spa-social-link.youtube {
    background-color: #FF0000;
}

.spa-social-link.linkedin {
    background-color: #0077B5;
}

.spa-social-link.tiktok {
    background: linear-gradient(45deg, #000000, #EE1D52, #69C9D0);
}

.spa-map {
    margin-top: 40px;
}

.spa-map-container {
    border-radius: 12px;
    overflow: hidden;
    height: 350px;
    border: 1px solid rgba(210, 180, 140, 0.1);
}

/* Enhanced Location & Neighborhood Section */
.spa-neighborhood-header {
    margin-bottom: 15px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 8px;
}

.spa-neighborhood-name {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--primary-color);
}

.spa-district-name {
    font-size: 1.1rem;
    color: #666;
}

.spa-neighborhood-description {
    margin-top: 15px;
    line-height: 1.7;
    color: #444;
    background-color: rgba(245, 245, 240, 0.5);
    padding: 15px;
    border-radius: 8px;
    border-left: 3px solid var(--primary-color);
}

.spa-near-me-text {
    margin-top: 15px;
    font-weight: 500;
}

.spa-near-me-text i {
    color: var(--primary-color);
    margin-right: 8px;
}

.spa-near-me-text a {
    color: var(--primary-color);
    text-decoration: underline;
    transition: all 0.3s ease;
}

.spa-near-me-text a:hover {
    color: var(--secondary-color);
}

.spa-nearby-landmarks {
    margin-top: 30px;
}

.spa-landmarks-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.spa-attractions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 25px;
    margin-top: 30px;
}

.spa-attraction-card {
    background-color: #ffffff;
    border-radius: 12px;
    border: 1px solid rgba(210, 180, 140, 0.1);
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.spa-attraction-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(104, 160, 160, 0.1);
    border-color: var(--primary-color);
}

.spa-attraction-name {
    font-size: 1.2rem;
    margin: 0;
    padding: 15px 20px;
    color: #ffffff;
    font-weight: 500;
    background-color: #68a0a0;
    font-family: var(--font-secondary);
}

.spa-attraction-distance {
    color: var(--primary-color);
    font-size: 0.95rem;
    display: flex;
    align-items: center;
    padding: 15px 20px 5px;
    font-weight: 500;
}

.spa-attraction-distance i {
    margin-right: 8px;
    color: var(--primary-color);
}

.spa-attraction-description {
    color: var(--text-color);
    font-size: 0.95rem;
    line-height: 1.6;
    padding: 0 20px 15px;
    flex-grow: 1;
}

.spa-attraction-details {
    padding: 0 20px 15px;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.spa-attraction-address {
    display: flex;
    align-items: center;
    color: #666;
    font-size: 0.9rem;
}

.spa-attraction-address i {
    margin-right: 8px;
    color: #68a0a0;
}

.spa-attraction-category {
    display: flex;
    align-items: center;
    color: #666;
    font-size: 0.9rem;
}

.spa-attraction-category i {
    margin-right: 8px;
    color: #68a0a0;
}

.spa-attraction-fee {
    display: flex;
    align-items: center;
    font-size: 0.9rem;
}

.spa-attraction-fee i {
    margin-right: 8px;
    color: #68a0a0;
}

.spa-free-entry {
    color: #4CAF50;
    font-weight: 500;
}

.spa-paid-entry {
    color: #FF9800;
    font-weight: 500;
}

.spa-attraction-map-link {
    padding: 0 20px 15px;
    margin-top: 5px;
}

.spa-attraction-map-link a {
    display: inline-flex;
    align-items: center;
    color: #68a0a0;
    font-size: 0.9rem;
    text-decoration: none;
    padding: 8px 15px;
    border-radius: 20px;
    background-color: rgba(104, 160, 160, 0.1);
    transition: all 0.3s ease;
}

.spa-attraction-map-link a:hover {
    background-color: #68a0a0;
    color: #ffffff;
    transform: translateY(-2px);
    box-shadow: 0 5px 10px rgba(104, 160, 160, 0.2);
}

.spa-attraction-map-link a i {
    margin-right: 5px;
}

.spa-attractions-near-me {
    margin-top: 20px;
    text-align: center;
    background-color: rgba(245, 245, 240, 0.7);
    padding: 12px 15px;
    border-radius: 8px;
}

.spa-map-section {
    margin-top: 30px;
}

.spa-directions-link {
    margin-top: 15px;
    text-align: center;
}

.spa-get-directions-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background-color: var(--primary-color);
    color: #fff;
    padding: 12px 25px;
    border-radius: 30px;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 4px 10px rgba(104, 160, 160, 0.2);
    text-decoration: none;
}

.spa-get-directions-button:hover {
    background-color: var(--secondary-color);
    transform: translateY(-3px);
    box-shadow: 0 8px 15px rgba(104, 160, 160, 0.3);
    color: #fff;
}

.spa-get-directions-button i {
    margin-right: 8px;
}

/* Nearby Attractions Section */
.spa-attractions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 25px;
    margin-top: 30px;
}

.spa-attraction-card {
    background-color: #ffffff;
    border-radius: 12px;
    border: 1px solid rgba(210, 180, 140, 0.1);
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.spa-attraction-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(104, 160, 160, 0.1);
    border-color: var(--primary-color);
}

.spa-attraction-name {
    font-size: 1.2rem;
    margin: 0;
    padding: 15px 20px;
    color: #ffffff;
    font-weight: 500;
    background-color: #68a0a0;
    font-family: var(--font-secondary);
}

.spa-attraction-distance {
    color: var(--primary-color);
    font-size: 0.95rem;
    display: flex;
    align-items: center;
    padding: 15px 20px 5px;
}

.spa-attraction-distance i {
    margin-right: 8px;
    color: var(--primary-color);
}

.spa-attraction-description {
    color: var(--text-color);
    font-size: 0.95rem;
    line-height: 1.6;
    padding: 0 20px 20px;
}

/* Sustainability Section */
.spa-sustainability-practices, .spa-sustainability-certifications {
    margin-bottom: 30px;
}

.spa-sustainability-list, .spa-certifications-list {
    list-style: none;
    padding: 0;
    margin: 0;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.spa-sustainability-list li, .spa-certifications-list li {
    background-color: #ffffff;
    padding: 20px;
    border-radius: 12px;
    display: flex;
    align-items: flex-start;
    line-height: 1.6;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    border: 1px solid rgba(210, 180, 140, 0.1);
    position: relative;
    overflow: hidden;
    font-size: 0.95rem;
}

.spa-sustainability-list li::before, .spa-certifications-list li::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background-color: #68a0a0;
    opacity: 0.7;
    transition: all 0.3s ease;
}

.spa-sustainability-list li:hover::before, .spa-certifications-list li:hover::before {
    background-color: var(--secondary-color);
    width: 6px;
}

.spa-sustainability-list li:hover, .spa-certifications-list li:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(104, 160, 160, 0.15);
    border-color: var(--primary-color);
}

.spa-sustainability-list li i, .spa-certifications-list li i {
    margin-right: 15px;
    color: #68a0a0;
    margin-top: 3px;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    background-color: rgba(104, 160, 160, 0.1);
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    box-shadow: 0 3px 8px rgba(104, 160, 160, 0.1);
}

.spa-sustainability-list li span, .spa-certifications-list li span {
    flex: 1;
}

.spa-sustainability-list li:hover i, .spa-certifications-list li:hover i {
    color: #ffffff;
    background-color: #68a0a0;
    transform: scale(1.1);
}

/* Awards Section */
.spa-awards-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 25px;
    margin-top: 30px;
}

.spa-award {
    background-color: var(--light-color);
    border-radius: 12px;
    padding: 25px;
    border: 1px solid rgba(210, 180, 140, 0.1);
    display: flex;
    align-items: flex-start;
    transition: all 0.3s ease;
}

.spa-award:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(104, 160, 160, 0.1);
    border-color: var(--primary-color);
}

.spa-award-icon {
    margin-right: 20px;
    font-size: 2rem;
    color: var(--secondary-color);
}

.spa-award-details {
    flex: 1;
}

.spa-award-name {
    font-size: 1.2rem;
    margin-bottom: 10px;
    color: var(--dark-color);
}

.spa-award-year {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 8px;
}

.spa-award-description {
    color: var(--text-color);
    font-size: 0.95rem;
    line-height: 1.6;
}

/* FAQ Section */
.spa-faq-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-top: 30px;
}

.spa-faq-item {
    background-color: #ffffff;
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(210, 180, 140, 0.1);
    overflow: hidden;
    transition: all 0.3s ease;
}

.spa-faq-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(104, 160, 160, 0.15);
    border-color: var(--primary-color);
}

.spa-faq-question {
    font-size: 1.2rem;
    margin: 0;
    padding: 20px;
    background-color: rgba(245, 245, 240, 0.7);
    color: var(--dark-color);
    font-weight: 600;
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border-bottom: 1px solid rgba(210, 180, 140, 0.1);
}

.spa-faq-question i {
    margin-right: 15px;
    color: #68a0a0;
    font-size: 1.2rem;
    transition: all 0.3s ease;
    background-color: rgba(104, 160, 160, 0.1);
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    box-shadow: 0 3px 8px rgba(104, 160, 160, 0.1);
}

.spa-faq-item:hover .spa-faq-question i {
    color: #ffffff;
    background-color: #68a0a0;
    transform: scale(1.1);
}

.spa-faq-answer {
    padding: 20px;
    font-size: 1rem;
    line-height: 1.7;
    color: #555;
    background-color: #ffffff;
}

/* Navigation Buttons */
.spa-navigation-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin: 50px 0 30px;
    flex-wrap: wrap;
}

.spa-navigation-buttons .spa-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 14px 28px;
    background-color: var(--primary-color);
    color: var(--light-text-color);
    border-radius: 30px;
    font-weight: 500;
    text-align: center;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    letter-spacing: 0.03em;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
    text-decoration: none;
}

.spa-navigation-buttons .spa-button:hover {
    background-color: var(--secondary-color);
    color: var(--light-text-color);
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.08);
}

.spa-navigation-buttons .spa-button i {
    margin-right: 8px;
}

/* Sidebar */
.spa-sidebar-widget {
    background-color: var(--light-color);
    border-radius: 12px;
    box-shadow: var(--box-shadow);
    padding: 30px;
    margin-bottom: 30px;
    border: 1px solid rgba(210, 180, 140, 0.2);
    transition: all 0.3s ease;
}

.spa-sidebar-widget:hover {
    box-shadow: 0 15px 30px rgba(104, 160, 160, 0.1);
    transform: translateY(-5px);
}

.spa-sidebar-widget h3 {
    font-size: 1.4rem;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(210, 180, 140, 0.2);
    letter-spacing: 0.01em;
    color: var(--dark-color);
    font-family: var(--font-secondary);
    position: relative;
}

.spa-sidebar-widget h3::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 50px;
    height: 3px;
    background-color: var(--primary-color);
    border-radius: 3px;
}

.spa-contact-buttons {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.spa-contact-button {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 16px;
    border-radius: 30px;
    font-weight: 500;
    transition: all 0.3s ease;
    letter-spacing: 0.02em;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
}

.spa-contact-button i {
    margin-right: 10px;
    font-size: 1.1rem;
}

.spa-contact-button.phone {
    background-color: var(--primary-color);
    color: #fff;
}

.spa-contact-button.email {
    background-color: var(--secondary-color);
    color: #fff;
}

.spa-contact-button.website {
    background-color: #f8f9fa;
    color: var(--dark-color);
    border: 1px solid var(--border-color);
}

.spa-contact-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.spa-related-spas {
    display: grid;
    grid-template-columns: 1fr;
    gap: 20px;
}

.spa-related-spa {
    margin-bottom: 0;
    transition: all 0.3s ease;
}

.spa-related-spa:hover {
    transform: translateX(5px);
}

.spa-related-spa-link {
    display: flex;
    align-items: center;
    color: var(--dark-color);
    transition: all 0.3s ease;
    padding: 10px;
    border-radius: 8px;
    background-color: rgba(255, 255, 255, 0.5);
}

.spa-related-spa-link:hover {
    color: var(--primary-color);
    background-color: #fff;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.spa-related-spa-link img {
    width: 70px;
    height: 70px;
    object-fit: cover;
    border-radius: 8px;
    margin-right: 15px;
    transition: all 0.3s ease;
}

.spa-related-spa-link:hover img {
    transform: scale(1.05);
}

.spa-related-spa-title {
    font-weight: 500;
    font-size: 1.05rem;
    line-height: 1.4;
}

/* Lightbox */
.spa-lightbox {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.4s ease;
    backdrop-filter: blur(5px);
}

.spa-lightbox.active {
    opacity: 1;
    visibility: visible;
}

.spa-lightbox-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.85);
    cursor: pointer;
}

.spa-lightbox-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    max-width: 90%;
    max-height: 90%;
    background-color: transparent;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
    animation: lightboxFadeIn 0.4s ease;
}

@keyframes lightboxFadeIn {
    from {
        opacity: 0;
        transform: translate(-50%, -48%);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%);
    }
}

.spa-lightbox-content img {
    display: block;
    max-width: 100%;
    max-height: 85vh;
    border-radius: 8px 8px 0 0;
    object-fit: contain;
}

.spa-lightbox-caption {
    padding: 15px 20px;
    text-align: center;
    background-color: rgba(255, 255, 255, 0.9);
    color: var(--dark-color);
    font-weight: 500;
    border-radius: 0 0 8px 8px;
    font-family: var(--font-secondary);
}

.spa-lightbox-close {
    position: absolute;
    top: 15px;
    right: 15px;
    width: 40px;
    height: 40px;
    background-color: rgba(0, 0, 0, 0.6);
    color: #fff;
    border: none;
    border-radius: 50%;
    font-size: 1.2rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    z-index: 10;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.spa-lightbox-close:hover {
    background-color: var(--primary-color);
    transform: rotate(90deg);
}

/* Lightbox Navigation */
.spa-lightbox-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 50px;
    height: 50px;
    background-color: rgba(0, 0, 0, 0.6);
    color: #fff;
    border: none;
    border-radius: 50%;
    font-size: 1.5rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    z-index: 10;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.spa-lightbox-prev {
    left: 20px;
}

.spa-lightbox-next {
    right: 20px;
}

.spa-lightbox-nav:hover {
    background-color: var(--primary-color);
    transform: scale(1.1);
}

/* Responsive Styles */
@media (min-width: 1400px) {
    .spa-gallery {
        max-width: 1400px;
    }

    .spa-gallery-grid {
        grid-auto-rows: 280px;
    }
}

@media (max-width: 992px) {
    .spa-gallery-grid {
        grid-template-columns: repeat(2, 1fr);
        grid-auto-rows: 220px;
    }

    /* Content wrapper styles for tablet */

    .spa-section-nav {
        overflow-x: auto;
        white-space: nowrap;
        padding: 15px;
        justify-content: flex-start;
        top: 60px;
    }

    .spa-section-nav-link {
        padding: 8px 15px;
        font-size: 0.9rem;
    }

    .spa-lightbox-nav {
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
    }

    .spa-lightbox-prev {
        left: 10px;
    }

    .spa-lightbox-next {
        right: 10px;
    }

    .spa-gallery-zoom {
        width: 50px;
        height: 50px;
        font-size: 1.1rem;
    }

    /* Improved gallery spacing for tablets */
    .spa-gallery {
        padding: 25px 15px;
        border-radius: 14px;
    }

    .spa-gallery-grid {
        gap: 12px;
    }
}

@media (max-width: 768px) {
    .spa-single .page-header {
        padding: 40px 0;
    }

    .spa-single .page-title {
        font-size: 2.2rem;
    }

    .spa-gallery-grid {
        grid-template-columns: repeat(2, 1fr);
        grid-auto-rows: 200px;
    }

    .spa-cta-button {
        width: 100%;
        justify-content: center;
    }

    .spa-section {
        padding: 25px;
    }

    .spa-section-title {
        font-size: 1.6rem;
    }

    .spa-subsection-title {
        font-size: 1.3rem;
        margin-bottom: 15px;
    }

    .spa-review-sources-grid,
    .spa-featured-reviews-grid {
        grid-template-columns: 1fr;
    }

    .spa-services-amenities {
        flex-direction: column;
    }
}

@media (max-width: 576px) {
    .spa-title {
        font-size: 2rem;
    }

    .spa-gallery {
        padding: 20px 10px;
        margin-bottom: 30px;
        border-radius: 12px;
    }

    .spa-gallery-grid {
        grid-template-columns: 1fr;
        grid-auto-rows: 200px;
        gap: 12px;
    }

    .spa-gallery-item {
        height: 200px;
        border-radius: 10px;
    }

    .spa-gallery-item img {
        border-radius: 9px;
    }

    .spa-gallery-item-overlay {
        border-radius: 9px;
    }

    .spa-lightbox-content {
        max-width: 95%;
    }

    .spa-lightbox-caption {
        padding: 10px 15px;
        font-size: 0.9rem;
    }

    .spa-lightbox-close {
        top: 10px;
        right: 10px;
        width: 35px;
        height: 35px;
    }

    .spa-lightbox-nav {
        width: 35px;
        height: 35px;
        font-size: 1rem;
    }

    .spa-gallery-zoom {
        width: 45px;
        height: 45px;
        font-size: 1rem;
    }

    .spa-section {
        padding: 20px;
        margin-bottom: 20px;
    }

    .spa-section-title {
        font-size: 1.5rem;
        margin-bottom: 20px;
    }

    .spa-subsection-title {
        font-size: 1.2rem;
        margin-bottom: 15px;
    }

    .spa-section-nav {
        padding: 12px;
        margin-bottom: 20px;
        top: 50px;
    }

    .spa-section-nav-link {
        padding: 6px 12px;
        font-size: 0.85rem;
    }

    .spa-packages {
        grid-template-columns: 1fr;
    }

    .spa-info-grid {
        grid-template-columns: 1fr;
    }

    .spa-card-container {
        margin-left: -15px;
        margin-right: -15px;
        width: calc(100% + 30px);
        padding: 25px 15px;
        border-radius: 10px;
    }

    .spa-specialties-list {
        grid-template-columns: 1fr;
    }
}
