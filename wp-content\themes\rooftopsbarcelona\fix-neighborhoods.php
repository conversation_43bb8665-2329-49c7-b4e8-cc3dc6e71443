<?php
/**
 * Fix Neighborhood Assignments
 * 
 * This script fixes the neighborhood assignments for all spas
 */

// Load WordPress
require_once( dirname( __FILE__ ) . '/../../wp-load.php' );

// Security check - only administrators can access this page
if (!current_user_can('administrator')) {
    wp_die('You do not have permission to access this page.');
}

echo '<h1>Fix Spa Neighborhood Assignments</h1>';

// Define the neighborhood mappings (normalized names)
$neighborhood_mappings = array(
    'vila olimpica' => 'Vila Olímpica',
    'vila olímpica' => 'Vila Olímpica',
    'vila olimpica del poblenou' => 'Vila Olímpica',
    'vila olímpica del poblenou' => 'Vila Olímpica',
    'gothic quarter' => 'Gothic Quarter',
    'barri gotic' => 'Gothic Quarter',
    'barri gòtic' => 'Gothic Quarter',
    'el gotic' => 'Gothic Quarter',
    'el gòtic' => 'Gothic Quarter',
    'eixample' => 'Eixample',
    'l\'eixample' => 'Eixample',
    'sants' => 'Sants',
    'sants-montjuic' => 'Sants',
    'sants-montjuïc' => 'Sants',
    'les corts' => 'Les Corts',
    'pedralbes' => 'Pedralbes',
    'la barceloneta' => 'La Barceloneta',
    'barceloneta' => 'La Barceloneta',
    'gracia' => 'Gràcia',
    'gràcia' => 'Gràcia',
    'vila de gracia' => 'Gràcia',
    'vila de gràcia' => 'Gràcia'
);

// Process the fix
echo '<div style="background: #d1ffd1; padding: 10px; margin: 10px 0; border: 1px solid #00a000;">';
echo '<p><strong>Fixing neighborhood assignments...</strong></p>';

// Get all spas
$all_spas = get_posts(array(
    'post_type' => 'spa',
    'posts_per_page' => -1,
));

$fixed_count = 0;
$created_terms = array();
$assigned_terms = array();

foreach ($all_spas as $spa) {
    // Get the spa's meta data for location
    $location = get_post_meta($spa->ID, 'location', true);
    
    if (!empty($location) && !empty($location['neighborhood'])) {
        $neighborhood_name = $location['neighborhood'];
        
        // Normalize the neighborhood name (lowercase for comparison)
        $normalized_key = strtolower($neighborhood_name);
        
        // Use the mapping if available
        if (isset($neighborhood_mappings[$normalized_key])) {
            $normalized_name = $neighborhood_mappings[$normalized_key];
        } else {
            // If no mapping, use the original with first letter capitalized
            $normalized_name = ucfirst($neighborhood_name);
        }
        
        // Get the term by name
        $term = get_term_by('name', $normalized_name, 'spa_neighborhood');
        
        if (!$term) {
            // Try to get by slug
            $slug = sanitize_title($normalized_name);
            $term = get_term_by('slug', $slug, 'spa_neighborhood');
            
            if (!$term) {
                // Create the term if it doesn't exist
                $result = wp_insert_term($normalized_name, 'spa_neighborhood', array(
                    'description' => sprintf('Spas located in the %s neighborhood of Barcelona', $normalized_name),
                    'slug' => $slug
                ));
                
                if (!is_wp_error($result)) {
                    $term_id = $result['term_id'];
                    echo '<p>Created new neighborhood term: ' . esc_html($normalized_name) . ' (ID: ' . $term_id . ')</p>';
                    $created_terms[] = $normalized_name;
                } else {
                    echo '<p>Error creating term: ' . $result->get_error_message() . '</p>';
                    continue;
                }
            } else {
                $term_id = $term->term_id;
            }
        } else {
            $term_id = $term->term_id;
        }
        
        // Assign the term to the spa
        $result = wp_set_object_terms($spa->ID, $term_id, 'spa_neighborhood');
        
        if (!is_wp_error($result)) {
            echo '<p>Assigned ' . esc_html($normalized_name) . ' to spa: ' . esc_html($spa->post_title) . '</p>';
            $fixed_count++;
            $assigned_terms[$normalized_name] = isset($assigned_terms[$normalized_name]) ? $assigned_terms[$normalized_name] + 1 : 1;
        } else {
            echo '<p>Error assigning term to spa: ' . $result->get_error_message() . '</p>';
        }
    } else {
        echo '<p>No neighborhood data found for spa: ' . esc_html($spa->post_title) . '</p>';
    }
}

// Flush rewrite rules to ensure the new terms work
flush_rewrite_rules();

echo '<p><strong>Fixed ' . $fixed_count . ' spa neighborhood assignments.</strong></p>';

if (!empty($created_terms)) {
    echo '<p><strong>Created ' . count($created_terms) . ' new neighborhood terms:</strong></p>';
    echo '<ul>';
    foreach ($created_terms as $term) {
        echo '<li>' . esc_html($term) . '</li>';
    }
    echo '</ul>';
}

if (!empty($assigned_terms)) {
    echo '<p><strong>Neighborhood assignment summary:</strong></p>';
    echo '<ul>';
    foreach ($assigned_terms as $term => $count) {
        echo '<li>' . esc_html($term) . ': ' . $count . ' ' . _n('spa', 'spas', $count) . '</li>';
    }
    echo '</ul>';
}

echo '</div>';

// Add a link back to the debug page
echo '<p><a href="' . esc_url(home_url('/wp-content/themes/spasinbarcelona/debug-neighborhoods.php')) . '">Back to Neighborhood Debug</a></p>';

// Add a link to test a neighborhood page
echo '<p><a href="' . esc_url(home_url('/spa-neighborhood/vila-olimpica/')) . '" target="_blank">Test Vila Olímpica Neighborhood Page</a></p>';
