/**
 * Footer styles for Spas in Barcelona theme
 */

/* Footer Container */
.site-footer {
    background-color: #3a4a4a !important;
    color: #ffffff !important;
    font-family: 'Poppins', sans-serif !important;
    padding: 30px 0 !important;
    position: relative !important;
    z-index: 10 !important;
    margin-top: 0 !important;
    border-top: none !important;
}

/* Footer Bottom */
.footer-bottom {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    flex-wrap: wrap !important;
}

.footer-copyright {
    margin-bottom: 10px !important;
    color: #ffffff !important;
    font-family: 'Poppins', sans-serif !important;
}

/* Footer Navigation */
.footer-navigation {
    margin-top: 0 !important;
}

/* Footer Menu Styles */
.footer-menu {
    display: flex !important;
    flex-wrap: wrap !important;
    list-style: none !important;
    padding: 0 !important;
    margin: 0 !important;
}

.footer-menu li {
    margin: 0 15px 10px 0 !important;
}

.footer-menu li a {
    color: #ffffff !important;
    text-decoration: none !important;
    font-family: 'Poppins', sans-serif !important;
    font-size: 14px !important;
    transition: all 0.3s ease !important;
    padding: 5px 0 !important;
    position: relative !important;
}

.footer-menu li a:hover {
    color: #68a0a0 !important;
}

.footer-menu li a:after {
    content: '' !important;
    position: absolute !important;
    width: 0 !important;
    height: 2px !important;
    bottom: 0 !important;
    left: 0 !important;
    background-color: #68a0a0 !important;
    transition: width 0.3s ease !important;
}

.footer-menu li a:hover:after {
    width: 100% !important;
}

@media (max-width: 768px) {
    .footer-bottom {
        flex-direction: column !important;
        align-items: center !important;
        text-align: center !important;
    }

    .footer-menu {
        justify-content: center !important;
        margin-top: 15px !important;
    }

    .footer-menu li {
        margin: 0 10px 10px !important;
    }
}

/* Back to Top Button */
.back-to-top {
    position: fixed !important;
    bottom: 20px !important;
    right: 20px !important;
    width: 40px !important;
    height: 40px !important;
    background-color: #68a0a0 !important;
    color: #ffffff !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    cursor: pointer !important;
    opacity: 0 !important;
    visibility: hidden !important;
    transition: all 0.3s ease !important;
    z-index: 99 !important;
}

.back-to-top.visible {
    opacity: 1 !important;
    visibility: visible !important;
}

.back-to-top:hover {
    background-color: #d2b48c !important;
    transform: translateY(-5px) !important;
}
