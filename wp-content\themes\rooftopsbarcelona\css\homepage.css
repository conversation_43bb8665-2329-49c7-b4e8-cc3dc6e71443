/**
 * Homepage styles for Spas in Barcelona theme
 */

/* Hero Section */
.spa-hero {
    background-image: linear-gradient(rgba(58, 74, 74, 0.6), rgba(58, 74, 74, 0.7)), url('../images/barcelona-aerial-view.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    color: #fff;
    padding: 120px 0;
    text-align: center;
    position: relative;
    overflow: hidden;
}

/* Add a subtle overlay pattern to the hero */
.spa-hero:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('data:image/svg+xml;utf8,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><path d="M30 5 L55 30 L30 55 L5 30 Z" fill="none" stroke="rgba(255, 255, 255, 0.05)" stroke-width="1"/></svg>');
    pointer-events: none;
}

.spa-hero-content {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 20px;
}

.spa-hero-title {
    font-size: 3.2rem;
    margin-bottom: 25px;
    color: #fff;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    letter-spacing: 0.03em;
    font-weight: 600;
    line-height: 1.2;
}

.spa-hero-subtitle {
    font-size: 1.3rem;
    margin-bottom: 35px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    letter-spacing: 0.02em;
    line-height: 1.6;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
}

.spa-hero-search {
    max-width: 600px;
    margin: 0 auto 30px;
    position: relative;
}

.spa-hero-search input {
    width: 100%;
    padding: 18px 25px;
    border-radius: 50px;
    border: none;
    font-size: 1rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    background-color: rgba(255, 255, 255, 0.95);
    transition: var(--transition);
    letter-spacing: 0.01em;
}

.spa-hero-search input:focus {
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
    outline: none;
    background-color: #fff;
}

.spa-hero-search button {
    position: absolute;
    right: 6px;
    top: 6px;
    background-color: var(--primary-color);
    color: #fff;
    border: none;
    border-radius: 50px;
    width: 48px;
    height: 48px;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
}

.spa-hero-search button:hover {
    background-color: var(--secondary-color);
    transform: scale(1.05);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
}

.spa-hero-categories {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 10px;
}

.spa-hero-category {
    background-color: rgba(255, 255, 255, 0.15);
    color: #fff;
    padding: 12px 22px;
    border-radius: 50px;
    font-size: 0.9rem;
    transition: var(--transition);
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    letter-spacing: 0.02em;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
}

.spa-hero-category:hover {
    background-color: var(--primary-color);
    color: #fff;
    transform: translateY(-3px);
    box-shadow: 0 5px 12px rgba(0, 0, 0, 0.15);
    border-color: var(--primary-color);
}

.spa-hero-category.view-all {
    background-color: var(--primary-color);
}

.spa-hero-category.view-all:hover {
    background-color: var(--secondary-color);
}

/* Featured Section */
.spa-featured {
    padding: 40px 0;
}

/* Categories Section */
.spa-categories {
    padding: 40px 0;
    background-color: var(--light-color) !important;
    position: relative;
    overflow: hidden;
}

/* Add a subtle pattern overlay to the categories section */
.spa-categories:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('data:image/svg+xml;utf8,<svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"><path d="M0 20 L20 0 L40 20 L20 40 Z" fill="none" stroke="rgba(104, 160, 160, 0.05)" stroke-width="1"/></svg>');
    opacity: 0.5;
    pointer-events: none;
}

.spa-categories-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 30px;
    margin-top: 20px;
}

/* Common card base styles */
.spa-category-card,
.spa-specialty-card,
.spa-feature-card,
.spa-service-card,
.spa-tag-card {
    box-sizing: border-box;
    width: 100%; /* Ensure cards take full width of their grid cell */
}

.spa-category-card {
    background-color: #fff;
    border-radius: 12px;
    box-shadow: var(--box-shadow);
    padding: 35px 25px;
    text-align: center;
    transition: var(--transition);
    display: flex;
    flex-direction: column;
    align-items: center;
    color: var(--dark-color);
    border: 1px solid rgba(210, 180, 140, 0.2);
    text-decoration: none;
}

.spa-category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(104, 160, 160, 0.15);
    color: var(--dark-color);
    border-color: var(--primary-color);
}

.spa-category-icon {
    width: 70px;
    height: 70px;
    background-color: var(--primary-color);
    color: #fff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    margin-bottom: 20px;
    transition: var(--transition);
}

.spa-category-card:hover .spa-category-icon {
    background-color: var(--secondary-color);
    transform: scale(1.1);
}

.spa-category-title {
    font-size: 1.2rem;
    margin-bottom: 10px;
}

.spa-category-count {
    color: #666;
    font-size: 0.9rem;
}

/* Specialties Section */
.spa-specialties {
    padding: 40px 0;
    background-color: #f5f5f0 !important;
    position: relative;
    overflow: hidden;
    box-shadow: none;
}

/* Add a subtle pattern overlay to the specialties section */
.spa-specialties:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('data:image/svg+xml;utf8,<svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"><path d="M0 20 L20 0 L40 20 L20 40 Z" fill="none" stroke="rgba(104, 160, 160, 0.05)" stroke-width="1"/></svg>');
    opacity: 0.5;
    pointer-events: none;
}

.spa-specialties-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 30px;
    margin-top: 20px;
}

.spa-specialty-card {
    background-color: #fff;
    border-radius: 12px;
    box-shadow: var(--box-shadow);
    padding: 35px 25px;
    text-align: center;
    transition: var(--transition);
    display: flex;
    flex-direction: column;
    align-items: center;
    color: var(--dark-color);
    border: 1px solid rgba(210, 180, 140, 0.2);
    text-decoration: none;
}

.spa-specialty-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(104, 160, 160, 0.15);
    color: var(--dark-color);
    border-color: var(--primary-color);
}

.spa-specialty-icon {
    width: 70px;
    height: 70px;
    background-color: var(--primary-color);
    color: #fff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    margin-bottom: 20px;
    transition: var(--transition);
}

.spa-specialty-card:hover .spa-specialty-icon {
    background-color: var(--secondary-color);
    transform: scale(1.1);
}

.spa-specialty-title {
    font-size: 1.2rem;
    margin-bottom: 10px;
    font-family: var(--font-secondary);
    font-weight: 600;
}

.spa-specialty-count {
    color: #666;
    font-size: 0.9rem;
}

/* Services Section */
.spa-services {
    padding: 80px 0;
    background-color: #f5f5f0 !important;
    position: relative;
    overflow: hidden;
    box-shadow: none;
}

.spa-services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

/* Features Section */
.spa-features {
    padding: 80px 0;
    background-color: #f5f5f0 !important;
    position: relative;
    overflow: hidden;
    box-shadow: none;
}

/* Add a subtle pattern overlay to the features section */
.spa-features:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('data:image/svg+xml;utf8,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><circle cx="30" cy="30" r="20" fill="none" stroke="rgba(210, 180, 140, 0.05)" stroke-width="1"/></svg>');
    opacity: 0.5;
    pointer-events: none;
}

.spa-features-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 30px;
    margin-top: 40px;
}

.spa-feature-card {
    background-color: #fff;
    border-radius: 12px;
    box-shadow: var(--box-shadow);
    padding: 35px 25px;
    text-align: center;
    transition: var(--transition);
    display: flex;
    flex-direction: column;
    align-items: center;
    color: var(--dark-color);
    border: 1px solid rgba(210, 180, 140, 0.2);
    text-decoration: none;
}

.spa-feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(104, 160, 160, 0.15);
    color: var(--dark-color);
    border-color: var(--primary-color);
}

.spa-feature-icon {
    width: 70px;
    height: 70px;
    background-color: var(--primary-color);
    color: #fff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    margin-bottom: 20px;
    transition: var(--transition);
}

.spa-feature-card:hover .spa-feature-icon {
    background-color: var(--secondary-color);
    transform: scale(1.1);
}

.spa-feature-title {
    font-size: 1.2rem;
    margin-bottom: 10px;
    font-family: var(--font-secondary);
    font-weight: 600;
}

.spa-feature-count {
    color: #666;
    font-size: 0.9rem;
}

/* Tags Section */
.spa-tags {
    padding: 40px 0;
    position: relative;
    overflow: hidden;
}

/* Add a subtle pattern overlay to the tags section */
.spa-tags:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('data:image/svg+xml;utf8,<svg width="50" height="50" viewBox="0 0 50 50" xmlns="http://www.w3.org/2000/svg"><path d="M10 10 L40 10 L40 40 L10 40 Z" fill="none" stroke="rgba(104, 160, 160, 0.05)" stroke-width="1"/></svg>');
    opacity: 0.5;
    pointer-events: none;
}

/* Add a subtle pattern overlay to the services section */
.spa-services:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('data:image/svg+xml;utf8,<svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"><path d="M0 20 L20 0 L40 20 L20 40 Z" fill="none" stroke="rgba(104, 160, 160, 0.05)" stroke-width="1"/></svg>');
    opacity: 0.5;
    pointer-events: none;
}

/* Service Card Styles */
.spa-service-card {
    background-color: #fff;
    border-radius: 12px;
    box-shadow: var(--box-shadow);
    padding: 35px 25px;
    text-align: center;
    transition: var(--transition);
    display: flex;
    flex-direction: column;
    align-items: center;
    color: var(--dark-color);
    border: 1px solid rgba(210, 180, 140, 0.2);
    text-decoration: none;
}

.spa-section-footer {
    text-align: center;
    margin-top: 40px;
}

.spa-service-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(104, 160, 160, 0.15);
    color: var(--dark-color);
    border-color: var(--primary-color);
}

.spa-service-icon {
    width: 70px;
    height: 70px;
    background-color: var(--primary-color);
    color: #fff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    margin-bottom: 20px;
    transition: var(--transition);
}

.spa-service-card:hover .spa-service-icon {
    background-color: var(--secondary-color);
    transform: scale(1.1);
}

.spa-service-title {
    font-size: 1.2rem;
    margin-bottom: 10px;
    font-family: var(--font-secondary);
    font-weight: 600;
}

.spa-service-count {
    color: #666;
    font-size: 0.9rem;
}

.spa-tags-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 30px;
    margin-top: 20px;
}

.spa-tag-card {
    background-color: #fff;
    border-radius: 12px;
    box-shadow: var(--box-shadow);
    padding: 35px 25px;
    text-align: center;
    transition: var(--transition);
    display: flex;
    flex-direction: column;
    align-items: center;
    color: var(--dark-color);
    border: 1px solid rgba(210, 180, 140, 0.2);
    text-decoration: none;
}

.spa-tag-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(104, 160, 160, 0.15);
    color: var(--dark-color);
    border-color: var(--primary-color);
}

.spa-tag-icon {
    width: 70px;
    height: 70px;
    background-color: var(--primary-color);
    color: #fff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    margin-bottom: 20px;
    transition: var(--transition);
}

.spa-tag-card:hover .spa-tag-icon {
    background-color: var(--secondary-color);
    transform: scale(1.1);
}

.spa-tag-title {
    font-size: 1.2rem;
    margin-bottom: 10px;
    font-family: var(--font-secondary);
    font-weight: 600;
}

.spa-tag-count {
    color: #666;
    font-size: 0.9rem;
}

/* CTA Section */
.cta-section {
    padding: 80px 0;
    background-image: linear-gradient(rgba(58, 74, 74, 0.75), rgba(58, 74, 74, 0.85)), url('https://images.unsplash.com/photo-1494194069000-cb794f31d82c?q=80&w=1470&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    color: #fff;
    text-align: center;
    position: relative;
    overflow: hidden;
    box-shadow: 0 5px 25px rgba(0, 0, 0, 0.1);
}

/* Add a subtle overlay pattern to the CTA */
.cta-section:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('data:image/svg+xml;utf8,<svg width="80" height="80" viewBox="0 0 80 80" xmlns="http://www.w3.org/2000/svg"><circle cx="40" cy="40" r="30" fill="none" stroke="rgba(255, 255, 255, 0.05)" stroke-width="1"/></svg>');
    pointer-events: none;
}

.cta-content {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 20px;
    animation: fadeIn 1s ease-in-out;
}

.cta-title {
    font-size: 2.8rem;
    margin-bottom: 25px;
    color: #fff;
    letter-spacing: 0.03em;
    font-weight: 600;
    line-height: 1.2;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    font-family: var(--font-secondary);
}

.cta-subtitle {
    font-size: 1.2rem;
    margin-bottom: 35px;
    line-height: 1.7;
    letter-spacing: 0.01em;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.cta-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

.cta-button {
    display: inline-flex;
    align-items: center;
    padding: 18px 36px;
    color: #fff;
    border-radius: 50px;
    font-weight: 500;
    transition: var(--transition);
    letter-spacing: 0.03em;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.2);
    text-decoration: none;
}

.cta-button.primary {
    background-color: var(--primary-color);
}

.cta-button.secondary {
    background-color: var(--secondary-color);
}

.cta-button:hover {
    color: #fff;
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
}

.cta-button.primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.cta-button.secondary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Responsive Styles */
@media (max-width: 1200px) {
    /* Adjust grid columns for larger screens */
    .spa-features-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

@media (max-width: 992px) {
    /* Adjust grid columns for medium screens */
    .spa-features-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .spa-hero {
        padding: 80px 0;
    }

    .spa-hero-title {
        font-size: 2.5rem;
    }

    .spa-categories-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .spa-features-grid,
    .spa-tags-grid,
    .spa-specialties-grid,
    .spa-services-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }
}

@media (max-width: 576px) {
    .spa-hero {
        padding: 60px 0;
    }

    .spa-hero-title {
        font-size: 2rem;
    }

    .spa-hero-subtitle {
        font-size: 1rem;
    }

    .spa-categories-grid,
    .spa-tags-grid,
    .spa-specialties-grid,
    .spa-services-grid,
    .spa-features-grid {
        grid-template-columns: 1fr 1fr;
        gap: 20px;
    }

    /* Apply text wrapping for titles when in two-column layout */
    .spa-category-title,
    .spa-service-title,
    .spa-specialty-title,
    .spa-tag-title,
    .spa-feature-title {
        word-wrap: break-word; /* For older browsers */
        overflow-wrap: break-word; /* Standard property */
        white-space: normal; /* Ensure it's not set to nowrap elsewhere */
        word-break: break-all; /* Force break for long words */
    }

    /* Ensure cards stretch their content and icons remain centered */
    .spa-category-card,
    .spa-specialty-card,
    .spa-feature-card,
    .spa-service-card,
    .spa-tag-card {
        align-items: stretch;
    }

    .spa-category-icon,
    .spa-service-icon,
    .spa-specialty-icon,
    .spa-tag-icon,
    .spa-feature-icon {
        margin-left: auto;
        margin-right: auto;
    }
}

@media (max-width: 576px) {
    .cta-title {
        font-size: 2rem;
    }

    .cta-subtitle {
        font-size: 1rem;
    }

    .cta-button {
        padding: 15px 30px;
    }

    .spa-category-icon,
    .spa-service-icon,
    .spa-specialty-icon,
    .spa-tag-icon,
    .spa-feature-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .spa-category-title,
    .spa-service-title,
    .spa-specialty-title,
    .spa-tag-title,
    .spa-feature-title {
        font-size: 1.1rem;
        /* Text wrapping properties moved to @media (max-width: 768px) */
    }

    /* Adjust padding for cards on small screens */
    .spa-category-card,
    .spa-specialty-card,
    .spa-feature-card,
    .spa-service-card,
    .spa-tag-card {
        padding-left: 15px;
        padding-right: 15px;
    }
}
