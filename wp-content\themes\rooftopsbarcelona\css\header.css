/**
 * Header styles for Spas in Barcelona theme
 */

/* Header Container */
.site-header {
    background-color: var(--light-color);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    position: relative;
    z-index: 100;
    transition: all 0.3s ease;
    padding: 15px 0;
    border-bottom: 1px solid rgba(210, 180, 140, 0.2);
}

/* Container for the header content */
.site-header .spa-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 40px;
}

.inside-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 40px;
    max-width: 1200px;
    margin: 0 auto;
}

/* Ensure site title is left-aligned */
.site-title,
.site-description {
    text-align: left;
}

.sticky-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        transform: translateY(-100%);
    }
    to {
        transform: translateY(0);
    }
}

/* Logo and Site Branding */
.site-logo {
    display: flex;
    align-items: center;
}

.site-logo img {
    max-height: 60px;
    width: auto;
}

.site-branding {
    display: flex;
    align-items: flex-start;
    flex-direction: column;
    text-align: left;
    margin-right: auto; /* Push to the left */
}

.site-branding-container {
    display: flex;
    align-items: center;
}

.site-title {
    font-family: var(--font-secondary);
    font-size: 2rem;
    margin: 0;
    font-weight: 600;
    letter-spacing: 0.02em;
}

.site-title a {
    color: var(--dark-color);
    text-decoration: none;
    transition: color 0.3s ease;
}

.site-title a:hover {
    color: var(--primary-color);
}

.site-description {
    font-size: 0.9rem;
    margin: 0;
    color: #666;
}

/* Navigation */
.main-navigation {
    display: flex;
    align-items: center;
}

.main-navigation ul {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
}

.main-navigation li {
    position: relative;
    margin: 0 5px;
}

.main-navigation a {
    display: block;
    padding: 15px;
    color: var(--dark-color);
    font-weight: 500;
    text-decoration: none;
    transition: var(--transition);
}

.main-navigation a:hover,
.main-navigation .current-menu-item > a {
    color: var(--primary-color);
}

/* Dropdown Menu */
.main-navigation .sub-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background-color: #fff;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: all 0.3s ease;
    z-index: 100;
    display: block;
}

.main-navigation .menu-item-has-children:hover > .sub-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.main-navigation .sub-menu li {
    margin: 0;
    width: 100%;
}

.main-navigation .sub-menu a {
    padding: 10px 15px;
    border-bottom: 1px solid var(--border-color);
}

.main-navigation .sub-menu a:hover {
    background-color: #f8f9fa;
}

/* Mobile Menu Toggle */
.menu-toggle {
    display: none;
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 10px;
    color: var(--dark-color);
}

/* Breadcrumbs */
.spa-breadcrumbs {
    padding: 15px 0;
    background-color: #f8f9fa;
    border-bottom: 1px solid var(--border-color);
    font-size: 0.9rem;
    color: #666;
}

.spa-breadcrumbs a {
    color: var(--primary-color);
    text-decoration: none;
}

.spa-breadcrumbs a:hover {
    text-decoration: underline;
}

/* Responsive Styles */
@media (max-width: 1024px) {
    .inside-header {
        padding: 0 20px;
    }

    .site-title {
        font-size: 1.8rem;
    }
}

@media (max-width: 768px) {
    .site-header {
        padding: 10px 0;
    }

    .site-header .spa-container {
        flex-direction: column;
        padding: 0 15px;
    }

    .inside-header,
    .header-aligned-center:not([class*="nav-float-"]) .inside-header {
        flex-direction: column;
        padding: 0 15px;
        text-align: center;
    }

    .site-branding {
        margin-bottom: 10px;
        align-items: center;
        text-align: center;
        margin-right: 0; /* Reset margin on mobile */
    }

    /* Center site title and description on mobile */
    .site-title,
    .site-description {
        text-align: center;
    }

    .site-branding-container {
        margin-right: 0;
    }

    .main-navigation {
        width: 100%;
    }

    .menu-toggle {
        display: block;
    }

    .main-navigation ul {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background-color: var(--light-color);
        box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
        flex-direction: column;
        z-index: 999;
    }

    .main-navigation.toggled ul {
        display: block;
    }

    .main-navigation li {
        margin: 0;
        width: 100%;
    }

    .main-navigation a {
        padding: 15px;
        border-bottom: 1px solid var(--border-color);
    }

    .main-navigation .sub-menu {
        position: static;
        opacity: 1;
        visibility: visible;
        transform: none;
        box-shadow: none;
        width: 100%;
        display: none;
    }

    .main-navigation .menu-item-has-children.toggled > .sub-menu {
        display: block;
    }

    .main-navigation .sub-menu a {
        padding-left: 30px;
    }
}
