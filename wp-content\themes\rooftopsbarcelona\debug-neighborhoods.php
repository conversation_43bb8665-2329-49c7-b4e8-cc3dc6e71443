<?php
/**
 * Debug Neighborhoods
 *
 * This file helps diagnose issues with the spa_neighborhood taxonomy
 */

// Load WordPress
require_once( dirname( __FILE__ ) . '/../../wp-load.php' );

// Security check - only administrators can access this page
if (!current_user_can('administrator')) {
    wp_die('You do not have permission to access this page.');
}

echo '<h1>Spa Neighborhoods Debug</h1>';

// Check if we need to flush rewrite rules
if (isset($_GET['flush'])) {
    // Force update taxonomy slugs
    global $wp_taxonomies;
    if (isset($wp_taxonomies['spa_neighborhood'])) {
        $wp_taxonomies['spa_neighborhood']->rewrite['slug'] = 'spa-neighborhood';
        $wp_taxonomies['spa_neighborhood']->rewrite['with_front'] = false;
        $wp_taxonomies['spa_neighborhood']->rewrite['hierarchical'] = true;
        echo '<div style="background: #d1ffd1; padding: 10px; margin: 10px 0; border: 1px solid #00a000;">Updated spa_neighborhood taxonomy rewrite rules</div>';
    }

    // Flush rewrite rules
    flush_rewrite_rules();
    echo '<div style="background: #d1ffd1; padding: 10px; margin: 10px 0; border: 1px solid #00a000;">Rewrite rules have been flushed successfully.</div>';
}

// Get all neighborhoods
$neighborhoods = get_terms(array(
    'taxonomy' => 'spa_neighborhood',
    'hide_empty' => false,
));

// Check for specific spa
if (isset($_GET['check_spa'])) {
    $spa_id = intval($_GET['check_spa']);
    $spa = get_post($spa_id);

    if ($spa && $spa->post_type === 'spa') {
        echo '<div style="background: #e0f0ff; padding: 15px; margin: 15px 0; border: 1px solid #0073aa;">';
        echo '<h3>Spa Information: ' . esc_html($spa->post_title) . ' (ID: ' . $spa->ID . ')</h3>';

        // Get neighborhood terms for this spa
        $spa_neighborhoods = wp_get_post_terms($spa->ID, 'spa_neighborhood');

        if (!empty($spa_neighborhoods) && !is_wp_error($spa_neighborhoods)) {
            echo '<p><strong>Assigned Neighborhoods:</strong></p>';
            echo '<ul>';
            foreach ($spa_neighborhoods as $n) {
                echo '<li>' . esc_html($n->name) . ' (ID: ' . $n->term_id . ', Slug: ' . $n->slug . ')</li>';
            }
            echo '</ul>';
        } else {
            echo '<p><strong>No neighborhoods assigned to this spa.</strong></p>';
        }

        // Get the spa's meta data for location
        $location = get_post_meta($spa->ID, 'location', true);
        if (!empty($location)) {
            echo '<p><strong>Location Meta Data:</strong></p>';
            echo '<pre>' . print_r($location, true) . '</pre>';
        }

        echo '</div>';
    } else {
        echo '<div style="background: #ffd1d1; padding: 10px; margin: 10px 0; border: 1px solid #a00000;">';
        echo '<p>Spa not found with ID: ' . $spa_id . '</p>';
        echo '</div>';
    }
}

// Add a form to check a specific spa
echo '<div style="margin: 20px 0; padding: 15px; background: #f9f9f9; border: 1px solid #ddd;">';
echo '<h3>Check Spa Neighborhoods</h3>';
echo '<form method="get">';
echo '<label for="check_spa">Enter Spa ID: </label>';
echo '<input type="number" name="check_spa" id="check_spa" min="1" step="1">';
echo '<button type="submit" style="margin-left: 10px; padding: 5px 10px;">Check Spa</button>';
echo '</form>';

// Or select from a dropdown
$spas = get_posts(array(
    'post_type' => 'spa',
    'posts_per_page' => -1,
    'orderby' => 'title',
    'order' => 'ASC'
));

if (!empty($spas)) {
    echo '<form method="get" style="margin-top: 15px;">';
    echo '<label for="check_spa_select">Or select a spa: </label>';
    echo '<select name="check_spa" id="check_spa_select">';
    echo '<option value="">-- Select a Spa --</option>';
    foreach ($spas as $spa) {
        echo '<option value="' . $spa->ID . '">' . esc_html($spa->post_title) . '</option>';
    }
    echo '</select>';
    echo '<button type="submit" style="margin-left: 10px; padding: 5px 10px;">Check Spa</button>';
    echo '</form>';
}
echo '</div>';

// Add a section to fix neighborhood assignments
echo '<div style="margin: 20px 0; padding: 15px; background: #f0fff0; border: 1px solid #0a0;">';
echo '<h3>Fix Neighborhood Assignments</h3>';

if (isset($_GET['fix_neighborhoods']) && $_GET['fix_neighborhoods'] === '1') {
    // Process the fix
    echo '<div style="background: #d1ffd1; padding: 10px; margin: 10px 0; border: 1px solid #00a000;">';
    echo '<p><strong>Fixing neighborhood assignments...</strong></p>';

    // Get all spas
    $all_spas = get_posts(array(
        'post_type' => 'spa',
        'posts_per_page' => -1,
    ));

    $fixed_count = 0;

    foreach ($all_spas as $spa) {
        // Get the spa's meta data for location
        $location = get_post_meta($spa->ID, 'location', true);

        if (!empty($location) && !empty($location['neighborhood'])) {
            $neighborhood_name = $location['neighborhood'];

            // Normalize the neighborhood name
            $normalized_name = str_replace(array('Vila Olímpica', 'Vila Olimpica'), 'Vila Olímpica', $neighborhood_name);

            // Get the term by name
            $term = get_term_by('name', $normalized_name, 'spa_neighborhood');

            if (!$term) {
                // Try to get by slug
                $slug = sanitize_title($normalized_name);
                $term = get_term_by('slug', $slug, 'spa_neighborhood');

                if (!$term) {
                    // Create the term if it doesn't exist
                    $result = wp_insert_term($normalized_name, 'spa_neighborhood');
                    if (!is_wp_error($result)) {
                        $term_id = $result['term_id'];
                        echo '<p>Created new neighborhood term: ' . esc_html($normalized_name) . ' (ID: ' . $term_id . ')</p>';
                    } else {
                        echo '<p>Error creating term: ' . $result->get_error_message() . '</p>';
                        continue;
                    }
                } else {
                    $term_id = $term->term_id;
                }
            } else {
                $term_id = $term->term_id;
            }

            // Assign the term to the spa
            $result = wp_set_object_terms($spa->ID, $term_id, 'spa_neighborhood');

            if (!is_wp_error($result)) {
                echo '<p>Assigned ' . esc_html($normalized_name) . ' to spa: ' . esc_html($spa->post_title) . '</p>';
                $fixed_count++;
            } else {
                echo '<p>Error assigning term to spa: ' . $result->get_error_message() . '</p>';
            }
        }
    }

    echo '<p><strong>Fixed ' . $fixed_count . ' spa neighborhood assignments.</strong></p>';
    echo '</div>';
}

echo '<p>This will scan all spas and ensure they have the correct neighborhood assignments based on their location data.</p>';
echo '<a href="' . esc_url(add_query_arg('fix_neighborhoods', '1')) . '" style="display: inline-block; padding: 10px 15px; background: #0a0; color: #fff; text-decoration: none; border-radius: 3px;">Fix Neighborhood Assignments</a>';
echo '</div>';

echo '<h2>All Neighborhoods (' . count($neighborhoods) . ')</h2>';

if (!empty($neighborhoods) && !is_wp_error($neighborhoods)) {
    echo '<table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">';
    echo '<tr style="background: #f0f0f0;">';
    echo '<th style="border: 1px solid #ccc; padding: 8px; text-align: left;">ID</th>';
    echo '<th style="border: 1px solid #ccc; padding: 8px; text-align: left;">Name</th>';
    echo '<th style="border: 1px solid #ccc; padding: 8px; text-align: left;">Slug</th>';
    echo '<th style="border: 1px solid #ccc; padding: 8px; text-align: left;">Parent</th>';
    echo '<th style="border: 1px solid #ccc; padding: 8px; text-align: left;">Count</th>';
    echo '<th style="border: 1px solid #ccc; padding: 8px; text-align: left;">URL</th>';
    echo '<th style="border: 1px solid #ccc; padding: 8px; text-align: left;">Test</th>';
    echo '</tr>';

    foreach ($neighborhoods as $neighborhood) {
        $term_link = get_term_link($neighborhood);
        $parent_name = '';

        if ($neighborhood->parent) {
            $parent = get_term($neighborhood->parent, 'spa_neighborhood');
            if (!is_wp_error($parent)) {
                $parent_name = $parent->name;
            }
        }

        echo '<tr>';
        echo '<td style="border: 1px solid #ccc; padding: 8px;">' . $neighborhood->term_id . '</td>';
        echo '<td style="border: 1px solid #ccc; padding: 8px;">' . $neighborhood->name . '</td>';
        echo '<td style="border: 1px solid #ccc; padding: 8px;">' . $neighborhood->slug . '</td>';
        echo '<td style="border: 1px solid #ccc; padding: 8px;">' . ($parent_name ? $parent_name : 'None') . '</td>';
        echo '<td style="border: 1px solid #ccc; padding: 8px;">' . $neighborhood->count . '</td>';
        echo '<td style="border: 1px solid #ccc; padding: 8px;">' . (is_wp_error($term_link) ? 'Error: ' . $term_link->get_error_message() : $term_link) . '</td>';
        echo '<td style="border: 1px solid #ccc; padding: 8px;"><a href="' . esc_url($term_link) . '" target="_blank">Test Link</a></td>';
        echo '</tr>';
    }

    echo '</table>';
} else {
    echo '<p>No neighborhoods found or error occurred.</p>';
}

// Display rewrite rules
global $wp_rewrite;
$rules = $wp_rewrite->rewrite_rules();

echo '<h2>Rewrite Rules</h2>';
echo '<p>Showing only rules related to spa-neighborhood:</p>';

echo '<table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">';
echo '<tr style="background: #f0f0f0;">';
echo '<th style="border: 1px solid #ccc; padding: 8px; text-align: left;">Pattern</th>';
echo '<th style="border: 1px solid #ccc; padding: 8px; text-align: left;">Replacement</th>';
echo '</tr>';

$found = false;
foreach ($rules as $pattern => $replacement) {
    if (strpos($pattern, 'spa-neighborhood') !== false || strpos($replacement, 'spa_neighborhood') !== false) {
        $found = true;
        echo '<tr>';
        echo '<td style="border: 1px solid #ccc; padding: 8px;">' . $pattern . '</td>';
        echo '<td style="border: 1px solid #ccc; padding: 8px;">' . $replacement . '</td>';
        echo '</tr>';
    }
}

if (!$found) {
    echo '<tr><td colspan="2" style="border: 1px solid #ccc; padding: 8px;">No spa-neighborhood rules found!</td></tr>';
}

echo '</table>';

// Display taxonomy information
echo '<h2>Taxonomy Information</h2>';

if (isset($wp_taxonomies['spa_neighborhood'])) {
    $tax = $wp_taxonomies['spa_neighborhood'];

    echo '<table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">';
    echo '<tr style="background: #f0f0f0;">';
    echo '<th style="border: 1px solid #ccc; padding: 8px; text-align: left;">Property</th>';
    echo '<th style="border: 1px solid #ccc; padding: 8px; text-align: left;">Value</th>';
    echo '</tr>';

    echo '<tr><td style="border: 1px solid #ccc; padding: 8px;">Name</td><td style="border: 1px solid #ccc; padding: 8px;">' . $tax->name . '</td></tr>';
    echo '<tr><td style="border: 1px solid #ccc; padding: 8px;">Label</td><td style="border: 1px solid #ccc; padding: 8px;">' . $tax->label . '</td></tr>';
    echo '<tr><td style="border: 1px solid #ccc; padding: 8px;">Hierarchical</td><td style="border: 1px solid #ccc; padding: 8px;">' . ($tax->hierarchical ? 'Yes' : 'No') . '</td></tr>';
    echo '<tr><td style="border: 1px solid #ccc; padding: 8px;">Public</td><td style="border: 1px solid #ccc; padding: 8px;">' . ($tax->public ? 'Yes' : 'No') . '</td></tr>';
    echo '<tr><td style="border: 1px solid #ccc; padding: 8px;">Query Var</td><td style="border: 1px solid #ccc; padding: 8px;">' . (is_string($tax->query_var) ? $tax->query_var : ($tax->query_var ? 'True' : 'False')) . '</td></tr>';

    echo '<tr><td style="border: 1px solid #ccc; padding: 8px;">Rewrite Slug</td><td style="border: 1px solid #ccc; padding: 8px;">' . (isset($tax->rewrite['slug']) ? $tax->rewrite['slug'] : 'Not set') . '</td></tr>';
    echo '<tr><td style="border: 1px solid #ccc; padding: 8px;">Rewrite With Front</td><td style="border: 1px solid #ccc; padding: 8px;">' . (isset($tax->rewrite['with_front']) ? ($tax->rewrite['with_front'] ? 'Yes' : 'No') : 'Not set') . '</td></tr>';
    echo '<tr><td style="border: 1px solid #ccc; padding: 8px;">Rewrite Hierarchical</td><td style="border: 1px solid #ccc; padding: 8px;">' . (isset($tax->rewrite['hierarchical']) ? ($tax->rewrite['hierarchical'] ? 'Yes' : 'No') : 'Not set') . '</td></tr>';

    echo '</table>';
} else {
    echo '<p>spa_neighborhood taxonomy not found!</p>';
}

// Add a button to flush rewrite rules
echo '<div style="margin: 20px 0;">';
echo '<a href="' . esc_url(add_query_arg('flush', '1')) . '" style="display: inline-block; padding: 10px 15px; background: #0073aa; color: #fff; text-decoration: none; border-radius: 3px;">Flush Rewrite Rules</a>';
echo '</div>';

// Display permalinks settings
echo '<h2>Permalinks Settings</h2>';
$permalink_structure = get_option('permalink_structure');
echo '<p>Current permalink structure: <code>' . ($permalink_structure ? esc_html($permalink_structure) : 'Plain (Default)') . '</code></p>';

if (empty($permalink_structure)) {
    echo '<div style="background: #ffd1d1; padding: 10px; margin: 10px 0; border: 1px solid #a00000;">';
    echo '<strong>Warning:</strong> You are using plain permalinks. Custom taxonomy URLs will not work correctly. Please go to Settings > Permalinks and choose a different permalink structure.';
    echo '</div>';
}

// Display active plugins
echo '<h2>Active Plugins</h2>';
$active_plugins = get_option('active_plugins');
echo '<ul>';
foreach ($active_plugins as $plugin) {
    echo '<li>' . esc_html($plugin) . '</li>';
}
echo '</ul>';

// Display theme information
echo '<h2>Theme Information</h2>';
$theme = wp_get_theme();
echo '<p>Current theme: ' . $theme->get('Name') . ' (Version ' . $theme->get('Version') . ')</p>';
echo '<p>Parent theme: ' . ($theme->parent() ? $theme->parent()->get('Name') . ' (Version ' . $theme->parent()->get('Version') . ')' : 'None') . '</p>';
