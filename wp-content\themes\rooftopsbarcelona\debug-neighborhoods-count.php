<?php
/**
 * Debug Neighborhoods Count
 * 
 * This script displays all neighborhoods in the database and their spa counts
 */

// Load WordPress
require_once( dirname( __FILE__ ) . '/../../wp-load.php' );

// Security check - only administrators can access this page
if (!current_user_can('administrator')) {
    wp_die('You do not have permission to access this page.');
}

echo '<h1>Debug Neighborhoods Count</h1>';

// Get all neighborhoods
$all_neighborhoods = get_terms(array(
    'taxonomy' => 'spa_neighborhood',
    'hide_empty' => false,
));

echo '<div style="background: #e0f0ff; padding: 15px; margin: 15px 0; border: 1px solid #0073aa;">';
echo '<h2>All Neighborhoods (' . count($all_neighborhoods) . ')</h2>';

if (!empty($all_neighborhoods) && !is_wp_error($all_neighborhoods)) {
    echo '<table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">';
    echo '<tr style="background: #f0f0f0;">';
    echo '<th style="border: 1px solid #ccc; padding: 8px; text-align: left;">ID</th>';
    echo '<th style="border: 1px solid #ccc; padding: 8px; text-align: left;">Name</th>';
    echo '<th style="border: 1px solid #ccc; padding: 8px; text-align: left;">Slug</th>';
    echo '<th style="border: 1px solid #ccc; padding: 8px; text-align: left;">Count</th>';
    echo '<th style="border: 1px solid #ccc; padding: 8px; text-align: left;">Spas</th>';
    echo '</tr>';
    
    foreach ($all_neighborhoods as $neighborhood) {
        echo '<tr>';
        echo '<td style="border: 1px solid #ccc; padding: 8px;">' . $neighborhood->term_id . '</td>';
        echo '<td style="border: 1px solid #ccc; padding: 8px;">' . esc_html($neighborhood->name) . '</td>';
        echo '<td style="border: 1px solid #ccc; padding: 8px;">' . esc_html($neighborhood->slug) . '</td>';
        echo '<td style="border: 1px solid #ccc; padding: 8px;">' . $neighborhood->count . '</td>';
        
        // Get spas in this neighborhood
        $spas = get_posts(array(
            'post_type' => 'spa',
            'posts_per_page' => -1,
            'tax_query' => array(
                array(
                    'taxonomy' => 'spa_neighborhood',
                    'field' => 'term_id',
                    'terms' => $neighborhood->term_id,
                ),
            ),
        ));
        
        echo '<td style="border: 1px solid #ccc; padding: 8px;">';
        if (!empty($spas)) {
            foreach ($spas as $spa) {
                echo esc_html($spa->post_title) . '<br>';
            }
        } else {
            echo 'No spas';
        }
        echo '</td>';
        
        echo '</tr>';
    }
    
    echo '</table>';
} else {
    echo '<p>No neighborhoods found.</p>';
}

echo '</div>';

// Get neighborhoods with spas
$neighborhoods_with_spas = get_terms(array(
    'taxonomy' => 'spa_neighborhood',
    'hide_empty' => true,
));

echo '<div style="background: #d1ffd1; padding: 15px; margin: 15px 0; border: 1px solid #00a000;">';
echo '<h2>Neighborhoods with Spas (' . count($neighborhoods_with_spas) . ')</h2>';

if (!empty($neighborhoods_with_spas) && !is_wp_error($neighborhoods_with_spas)) {
    echo '<table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">';
    echo '<tr style="background: #f0f0f0;">';
    echo '<th style="border: 1px solid #ccc; padding: 8px; text-align: left;">ID</th>';
    echo '<th style="border: 1px solid #ccc; padding: 8px; text-align: left;">Name</th>';
    echo '<th style="border: 1px solid #ccc; padding: 8px; text-align: left;">Slug</th>';
    echo '<th style="border: 1px solid #ccc; padding: 8px; text-align: left;">Count</th>';
    echo '<th style="border: 1px solid #ccc; padding: 8px; text-align: left;">Spas</th>';
    echo '</tr>';
    
    foreach ($neighborhoods_with_spas as $neighborhood) {
        echo '<tr>';
        echo '<td style="border: 1px solid #ccc; padding: 8px;">' . $neighborhood->term_id . '</td>';
        echo '<td style="border: 1px solid #ccc; padding: 8px;">' . esc_html($neighborhood->name) . '</td>';
        echo '<td style="border: 1px solid #ccc; padding: 8px;">' . esc_html($neighborhood->slug) . '</td>';
        echo '<td style="border: 1px solid #ccc; padding: 8px;">' . $neighborhood->count . '</td>';
        
        // Get spas in this neighborhood
        $spas = get_posts(array(
            'post_type' => 'spa',
            'posts_per_page' => -1,
            'tax_query' => array(
                array(
                    'taxonomy' => 'spa_neighborhood',
                    'field' => 'term_id',
                    'terms' => $neighborhood->term_id,
                ),
            ),
        ));
        
        echo '<td style="border: 1px solid #ccc; padding: 8px;">';
        if (!empty($spas)) {
            foreach ($spas as $spa) {
                echo esc_html($spa->post_title) . '<br>';
            }
        } else {
            echo 'No spas';
        }
        echo '</td>';
        
        echo '</tr>';
    }
    
    echo '</table>';
} else {
    echo '<p>No neighborhoods with spas found.</p>';
}

echo '</div>';

// Get all spas
$all_spas = get_posts(array(
    'post_type' => 'spa',
    'posts_per_page' => -1,
));

echo '<div style="background: #fff0e0; padding: 15px; margin: 15px 0; border: 1px solid #ffa500;">';
echo '<h2>All Spas (' . count($all_spas) . ')</h2>';

if (!empty($all_spas)) {
    echo '<table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">';
    echo '<tr style="background: #f0f0f0;">';
    echo '<th style="border: 1px solid #ccc; padding: 8px; text-align: left;">ID</th>';
    echo '<th style="border: 1px solid #ccc; padding: 8px; text-align: left;">Title</th>';
    echo '<th style="border: 1px solid #ccc; padding: 8px; text-align: left;">Slug</th>';
    echo '<th style="border: 1px solid #ccc; padding: 8px; text-align: left;">Neighborhoods</th>';
    echo '</tr>';
    
    foreach ($all_spas as $spa) {
        echo '<tr>';
        echo '<td style="border: 1px solid #ccc; padding: 8px;">' . $spa->ID . '</td>';
        echo '<td style="border: 1px solid #ccc; padding: 8px;">' . esc_html($spa->post_title) . '</td>';
        echo '<td style="border: 1px solid #ccc; padding: 8px;">' . esc_html($spa->post_name) . '</td>';
        
        // Get neighborhoods for this spa
        $neighborhoods = wp_get_object_terms($spa->ID, 'spa_neighborhood');
        
        echo '<td style="border: 1px solid #ccc; padding: 8px;">';
        if (!empty($neighborhoods) && !is_wp_error($neighborhoods)) {
            foreach ($neighborhoods as $neighborhood) {
                echo esc_html($neighborhood->name) . ' (' . esc_html($neighborhood->slug) . ')<br>';
            }
        } else {
            echo 'No neighborhoods';
        }
        echo '</td>';
        
        echo '</tr>';
    }
    
    echo '</table>';
} else {
    echo '<p>No spas found.</p>';
}

echo '</div>';

// Add links for testing
echo '<p><a href="' . esc_url(home_url('/')) . '">Back to Homepage</a></p>';
