<?php
/**
 * Admin page for validating schema
 */

// Add admin menu
function spasinbarcelona_add_schema_validation_page() {
    add_submenu_page(
        'edit.php?post_type=spa',
        'Validate Schema',
        'Validate Schema',
        'manage_options',
        'validate-schema',
        'spasinbarcelona_schema_validation_page'
    );
}
add_action('admin_menu', 'spasinbarcelona_add_schema_validation_page');

// Render the admin page
function spasinbarcelona_schema_validation_page() {
    // Get all spas
    $args = array(
        'post_type' => 'spa',
        'posts_per_page' => -1,
        'orderby' => 'title',
        'order' => 'ASC'
    );
    
    $spas = get_posts($args);
    
    // Get the selected spa ID
    $spa_id = isset($_GET['spa_id']) ? intval($_GET['spa_id']) : 0;
    
    ?>
    <div class="wrap">
        <h1>Validate Schema</h1>
        
        <form method="get">
            <input type="hidden" name="post_type" value="spa">
            <input type="hidden" name="page" value="validate-schema">
            
            <select name="spa_id">
                <option value="">Select a spa</option>
                <?php foreach ($spas as $spa) : ?>
                    <option value="<?php echo $spa->ID; ?>" <?php selected($spa_id, $spa->ID); ?>>
                        <?php echo esc_html($spa->post_title); ?>
                    </option>
                <?php endforeach; ?>
            </select>
            
            <input type="submit" class="button" value="Validate Schema">
        </form>
        
        <?php if ($spa_id) : ?>
            <h2>Schema Validation for <?php echo get_the_title($spa_id); ?></h2>
            
            <?php
            // Get the schema
            $schema_html = spasinbarcelona_get_spa_schema($spa_id);
            
            // Extract JSON from the HTML
            preg_match_all('/<script type="application\/ld\+json">(.*?)<\/script>/s', $schema_html, $matches);
            
            if (empty($matches[1])) {
                echo '<p>No schema found.</p>';
            } else {
                // Process the main schema
                $schema = json_decode($matches[1][0], true);
                if (!$schema) {
                    echo '<p>Error: Invalid JSON in schema.</p>';
                } else {
                    // Check if the schema has a @graph property
                    if (!isset($schema['@graph'])) {
                        echo '<p>Error: Schema does not have a @graph property.</p>';
                    } else {
                        // Find the business entity
                        $business_entity = null;
                        foreach ($schema['@graph'] as $entity) {
                            if (isset($entity['@type'])) {
                                $types = is_array($entity['@type']) ? $entity['@type'] : array($entity['@type']);
                                if (in_array('HealthAndBeautyBusiness', $types) || in_array('DaySpa', $types) || in_array('LocalBusiness', $types)) {
                                    $business_entity = $entity;
                                    break;
                                }
                            }
                        }
                        
                        if (!$business_entity) {
                            echo '<p>Error: No business entity found in the schema.</p>';
                        } else {
                            // Check if the business entity has the required properties
                            echo '<h3>Business Entity Properties</h3>';
                            echo '<table class="widefat">';
                            echo '<thead>';
                            echo '<tr>';
                            echo '<th>Property</th>';
                            echo '<th>Status</th>';
                            echo '<th>Value</th>';
                            echo '</tr>';
                            echo '</thead>';
                            echo '<tbody>';
                            
                            // Check @type
                            echo '<tr>';
                            echo '<td>@type</td>';
                            if (isset($business_entity['@type'])) {
                                $types = is_array($business_entity['@type']) ? $business_entity['@type'] : array($business_entity['@type']);
                                if (in_array('HealthAndBeautyBusiness', $types) && in_array('DaySpa', $types) && in_array('LocalBusiness', $types)) {
                                    echo '<td><span style="color:green;">✓</span></td>';
                                    echo '<td>' . implode(', ', $types) . '</td>';
                                } else {
                                    echo '<td><span style="color:red;">✗</span></td>';
                                    echo '<td>Missing required types</td>';
                                }
                            } else {
                                echo '<td><span style="color:red;">✗</span></td>';
                                echo '<td>Property not found</td>';
                            }
                            echo '</tr>';
                            
                            // Check @id
                            echo '<tr>';
                            echo '<td>@id</td>';
                            if (isset($business_entity['@id'])) {
                                echo '<td><span style="color:green;">✓</span></td>';
                                echo '<td>' . esc_html($business_entity['@id']) . '</td>';
                            } else {
                                echo '<td><span style="color:red;">✗</span></td>';
                                echo '<td>Property not found</td>';
                            }
                            echo '</tr>';
                            
                            // Check name
                            echo '<tr>';
                            echo '<td>name</td>';
                            if (isset($business_entity['name'])) {
                                echo '<td><span style="color:green;">✓</span></td>';
                                echo '<td>' . esc_html($business_entity['name']) . '</td>';
                            } else {
                                echo '<td><span style="color:red;">✗</span></td>';
                                echo '<td>Property not found</td>';
                            }
                            echo '</tr>';
                            
                            // Check alternateName
                            echo '<tr>';
                            echo '<td>alternateName</td>';
                            if (isset($business_entity['alternateName'])) {
                                echo '<td><span style="color:green;">✓</span></td>';
                                echo '<td>' . esc_html($business_entity['alternateName']) . '</td>';
                            } else {
                                echo '<td><span style="color:red;">✗</span></td>';
                                echo '<td>Property not found</td>';
                            }
                            echo '</tr>';
                            
                            // Check description
                            echo '<tr>';
                            echo '<td>description</td>';
                            if (isset($business_entity['description'])) {
                                echo '<td><span style="color:green;">✓</span></td>';
                                echo '<td>' . esc_html(substr($business_entity['description'], 0, 100)) . '...</td>';
                            } else {
                                echo '<td><span style="color:red;">✗</span></td>';
                                echo '<td>Property not found</td>';
                            }
                            echo '</tr>';
                            
                            // Check address
                            echo '<tr>';
                            echo '<td>address</td>';
                            if (isset($business_entity['address']) && isset($business_entity['address']['@type']) && $business_entity['address']['@type'] === 'PostalAddress') {
                                echo '<td><span style="color:green;">✓</span></td>';
                                echo '<td>Has proper PostalAddress structure</td>';
                            } else {
                                echo '<td><span style="color:red;">✗</span></td>';
                                echo '<td>Missing or invalid structure</td>';
                            }
                            echo '</tr>';
                            
                            // Check geo
                            echo '<tr>';
                            echo '<td>geo</td>';
                            if (isset($business_entity['geo']) && isset($business_entity['geo']['@type']) && $business_entity['geo']['@type'] === 'GeoCoordinates') {
                                echo '<td><span style="color:green;">✓</span></td>';
                                echo '<td>Has proper GeoCoordinates structure</td>';
                            } else {
                                echo '<td><span style="color:red;">✗</span></td>';
                                echo '<td>Missing or invalid structure</td>';
                            }
                            echo '</tr>';
                            
                            // Check hasMap
                            echo '<tr>';
                            echo '<td>hasMap</td>';
                            if (isset($business_entity['hasMap']) && isset($business_entity['hasMap']['@type']) && $business_entity['hasMap']['@type'] === 'Map') {
                                echo '<td><span style="color:green;">✓</span></td>';
                                echo '<td>Has proper Map structure</td>';
                            } else {
                                echo '<td><span style="color:red;">✗</span></td>';
                                echo '<td>Missing or invalid structure</td>';
                            }
                            echo '</tr>';
                            
                            echo '</tbody>';
                            echo '</table>';
                            
                            // Check makesOffer
                            if (isset($business_entity['makesOffer']) && is_array($business_entity['makesOffer'])) {
                                $offers = $business_entity['makesOffer'];
                                echo '<h3>Offers (' . count($offers) . ')</h3>';
                                
                                foreach ($offers as $index => $offer) {
                                    echo '<h4>Offer #' . ($index + 1) . ': ' . (isset($offer['name']) ? esc_html($offer['name']) : 'Unnamed') . '</h4>';
                                    echo '<table class="widefat">';
                                    echo '<thead>';
                                    echo '<tr>';
                                    echo '<th>Property</th>';
                                    echo '<th>Status</th>';
                                    echo '<th>Value</th>';
                                    echo '</tr>';
                                    echo '</thead>';
                                    echo '<tbody>';
                                    
                                    // Check @type
                                    echo '<tr>';
                                    echo '<td>@type</td>';
                                    if (isset($offer['@type']) && $offer['@type'] === 'Offer') {
                                        echo '<td><span style="color:green;">✓</span></td>';
                                        echo '<td>Offer</td>';
                                    } else {
                                        echo '<td><span style="color:red;">✗</span></td>';
                                        echo '<td>Missing or invalid</td>';
                                    }
                                    echo '</tr>';
                                    
                                    // Check name
                                    echo '<tr>';
                                    echo '<td>name</td>';
                                    if (isset($offer['name'])) {
                                        echo '<td><span style="color:green;">✓</span></td>';
                                        echo '<td>' . esc_html($offer['name']) . '</td>';
                                    } else {
                                        echo '<td><span style="color:red;">✗</span></td>';
                                        echo '<td>Property not found</td>';
                                    }
                                    echo '</tr>';
                                    
                                    // Check itemOffered
                                    echo '<tr>';
                                    echo '<td>itemOffered</td>';
                                    if (isset($offer['itemOffered']) && isset($offer['itemOffered']['@type']) && $offer['itemOffered']['@type'] === 'Service') {
                                        echo '<td><span style="color:green;">✓</span></td>';
                                        echo '<td>Has proper Service structure</td>';
                                    } else {
                                        echo '<td><span style="color:red;">✗</span></td>';
                                        echo '<td>Missing or invalid structure</td>';
                                    }
                                    echo '</tr>';
                                    
                                    // Check provider reference
                                    echo '<tr>';
                                    echo '<td>provider</td>';
                                    if (isset($offer['itemOffered']['provider']) && isset($offer['itemOffered']['provider']['@id'])) {
                                        echo '<td><span style="color:green;">✓</span></td>';
                                        echo '<td>' . esc_html($offer['itemOffered']['provider']['@id']) . '</td>';
                                    } else {
                                        echo '<td><span style="color:red;">✗</span></td>';
                                        echo '<td>Missing or invalid reference</td>';
                                    }
                                    echo '</tr>';
                                    
                                    // Check seller reference
                                    echo '<tr>';
                                    echo '<td>seller</td>';
                                    if (isset($offer['seller']) && isset($offer['seller']['@id'])) {
                                        echo '<td><span style="color:green;">✓</span></td>';
                                        echo '<td>' . esc_html($offer['seller']['@id']) . '</td>';
                                    } else {
                                        echo '<td><span style="color:red;">✗</span></td>';
                                        echo '<td>Missing or invalid reference</td>';
                                    }
                                    echo '</tr>';
                                    
                                    // Check priceSpecification
                                    echo '<tr>';
                                    echo '<td>priceSpecification</td>';
                                    if (isset($offer['priceSpecification']) && isset($offer['priceSpecification']['@type']) && $offer['priceSpecification']['@type'] === 'PriceSpecification') {
                                        echo '<td><span style="color:green;">✓</span></td>';
                                        echo '<td>Has proper PriceSpecification structure</td>';
                                    } else {
                                        echo '<td><span style="color:red;">✗</span></td>';
                                        echo '<td>Missing or invalid structure</td>';
                                    }
                                    echo '</tr>';
                                    
                                    // Check price
                                    echo '<tr>';
                                    echo '<td>price</td>';
                                    if (isset($offer['priceSpecification']) && isset($offer['priceSpecification']['price'])) {
                                        echo '<td><span style="color:green;">✓</span></td>';
                                        echo '<td>' . esc_html($offer['priceSpecification']['price']) . '</td>';
                                    } else {
                                        echo '<td><span style="color:red;">✗</span></td>';
                                        echo '<td>Property not found</td>';
                                    }
                                    echo '</tr>';
                                    
                                    // Check priceCurrency
                                    echo '<tr>';
                                    echo '<td>priceCurrency</td>';
                                    if (isset($offer['priceSpecification']) && isset($offer['priceSpecification']['priceCurrency'])) {
                                        echo '<td><span style="color:green;">✓</span></td>';
                                        echo '<td>' . esc_html($offer['priceSpecification']['priceCurrency']) . '</td>';
                                    } else {
                                        echo '<td><span style="color:red;">✗</span></td>';
                                        echo '<td>Property not found</td>';
                                    }
                                    echo '</tr>';
                                    
                                    // Check validThrough
                                    echo '<tr>';
                                    echo '<td>validThrough</td>';
                                    if (isset($offer['priceSpecification']) && isset($offer['priceSpecification']['validThrough'])) {
                                        echo '<td><span style="color:green;">✓</span></td>';
                                        echo '<td>' . esc_html($offer['priceSpecification']['validThrough']) . '</td>';
                                    } else {
                                        echo '<td><span style="color:red;">✗</span></td>';
                                        echo '<td>Property not found</td>';
                                    }
                                    echo '</tr>';
                                    
                                    // Check availability
                                    echo '<tr>';
                                    echo '<td>availability</td>';
                                    if (isset($offer['availability'])) {
                                        echo '<td><span style="color:green;">✓</span></td>';
                                        echo '<td>' . esc_html($offer['availability']) . '</td>';
                                    } else {
                                        echo '<td><span style="color:red;">✗</span></td>';
                                        echo '<td>Property not found</td>';
                                    }
                                    echo '</tr>';
                                    
                                    echo '</tbody>';
                                    echo '</table>';
                                }
                            } else {
                                echo '<h3>Offers</h3>';
                                echo '<p>No offers found in the schema.</p>';
                            }
                            
                            // Find the WebPage entity
                            $webpage_entity = null;
                            foreach ($schema['@graph'] as $entity) {
                                if (isset($entity['@type']) && $entity['@type'] === 'WebPage') {
                                    $webpage_entity = $entity;
                                    break;
                                }
                            }
                            
                            if ($webpage_entity) {
                                echo '<h3>WebPage Entity</h3>';
                                echo '<table class="widefat">';
                                echo '<thead>';
                                echo '<tr>';
                                echo '<th>Property</th>';
                                echo '<th>Status</th>';
                                echo '<th>Value</th>';
                                echo '</tr>';
                                echo '</thead>';
                                echo '<tbody>';
                                
                                // Check @id
                                echo '<tr>';
                                echo '<td>@id</td>';
                                if (isset($webpage_entity['@id'])) {
                                    echo '<td><span style="color:green;">✓</span></td>';
                                    echo '<td>' . esc_html($webpage_entity['@id']) . '</td>';
                                } else {
                                    echo '<td><span style="color:red;">✗</span></td>';
                                    echo '<td>Property not found</td>';
                                }
                                echo '</tr>';
                                
                                // Check mainEntity reference
                                echo '<tr>';
                                echo '<td>mainEntity</td>';
                                if (isset($webpage_entity['mainEntity']) && isset($webpage_entity['mainEntity']['@id'])) {
                                    echo '<td><span style="color:green;">✓</span></td>';
                                    echo '<td>' . esc_html($webpage_entity['mainEntity']['@id']) . '</td>';
                                } else {
                                    echo '<td><span style="color:red;">✗</span></td>';
                                    echo '<td>Missing or invalid reference</td>';
                                }
                                echo '</tr>';
                                
                                // Check breadcrumb reference
                                echo '<tr>';
                                echo '<td>breadcrumb</td>';
                                if (isset($webpage_entity['breadcrumb']) && isset($webpage_entity['breadcrumb']['@id'])) {
                                    echo '<td><span style="color:green;">✓</span></td>';
                                    echo '<td>' . esc_html($webpage_entity['breadcrumb']['@id']) . '</td>';
                                } else {
                                    echo '<td><span style="color:red;">✗</span></td>';
                                    echo '<td>Missing or invalid reference</td>';
                                }
                                echo '</tr>';
                                
                                echo '</tbody>';
                                echo '</table>';
                            } else {
                                echo '<h3>WebPage Entity</h3>';
                                echo '<p>No WebPage entity found in the schema.</p>';
                            }
                            
                            // Find the BreadcrumbList entity
                            $breadcrumb_entity = null;
                            foreach ($schema['@graph'] as $entity) {
                                if (isset($entity['@type']) && $entity['@type'] === 'BreadcrumbList') {
                                    $breadcrumb_entity = $entity;
                                    break;
                                }
                            }
                            
                            if ($breadcrumb_entity) {
                                echo '<h3>BreadcrumbList Entity</h3>';
                                echo '<table class="widefat">';
                                echo '<thead>';
                                echo '<tr>';
                                echo '<th>Property</th>';
                                echo '<th>Status</th>';
                                echo '<th>Value</th>';
                                echo '</tr>';
                                echo '</thead>';
                                echo '<tbody>';
                                
                                // Check @id
                                echo '<tr>';
                                echo '<td>@id</td>';
                                if (isset($breadcrumb_entity['@id'])) {
                                    echo '<td><span style="color:green;">✓</span></td>';
                                    echo '<td>' . esc_html($breadcrumb_entity['@id']) . '</td>';
                                } else {
                                    echo '<td><span style="color:red;">✗</span></td>';
                                    echo '<td>Property not found</td>';
                                }
                                echo '</tr>';
                                
                                // Check itemListElement
                                echo '<tr>';
                                echo '<td>itemListElement</td>';
                                if (isset($breadcrumb_entity['itemListElement']) && is_array($breadcrumb_entity['itemListElement'])) {
                                    echo '<td><span style="color:green;">✓</span></td>';
                                    echo '<td>Found ' . count($breadcrumb_entity['itemListElement']) . ' items</td>';
                                } else {
                                    echo '<td><span style="color:red;">✗</span></td>';
                                    echo '<td>Property not found or not an array</td>';
                                }
                                echo '</tr>';
                                
                                // Check breadcrumb levels
                                echo '<tr>';
                                echo '<td>Breadcrumb Levels</td>';
                                if (isset($breadcrumb_entity['itemListElement']) && is_array($breadcrumb_entity['itemListElement'])) {
                                    if (count($breadcrumb_entity['itemListElement']) === 3) {
                                        echo '<td><span style="color:green;">✓</span></td>';
                                        
                                        // Print the breadcrumb path
                                        $breadcrumb_path = array();
                                        foreach ($breadcrumb_entity['itemListElement'] as $item) {
                                            if (isset($item['name'])) {
                                                $breadcrumb_path[] = $item['name'];
                                            }
                                        }
                                        echo '<td>' . esc_html(implode(" > ", $breadcrumb_path)) . '</td>';
                                    } else {
                                        echo '<td><span style="color:red;">✗</span></td>';
                                        echo '<td>Incorrect number of levels (' . count($breadcrumb_entity['itemListElement']) . ' instead of 3)</td>';
                                    }
                                } else {
                                    echo '<td><span style="color:red;">✗</span></td>';
                                    echo '<td>Property not found or not an array</td>';
                                }
                                echo '</tr>';
                                
                                echo '</tbody>';
                                echo '</table>';
                            } else {
                                echo '<h3>BreadcrumbList Entity</h3>';
                                echo '<p>No BreadcrumbList entity found in the schema.</p>';
                            }
                            
                            // Find the FAQPage entity
                            $faq_entity = null;
                            foreach ($schema['@graph'] as $entity) {
                                if (isset($entity['@type']) && $entity['@type'] === 'FAQPage') {
                                    $faq_entity = $entity;
                                    break;
                                }
                            }
                            
                            if ($faq_entity) {
                                echo '<h3>FAQPage Entity</h3>';
                                echo '<table class="widefat">';
                                echo '<thead>';
                                echo '<tr>';
                                echo '<th>Property</th>';
                                echo '<th>Status</th>';
                                echo '<th>Value</th>';
                                echo '</tr>';
                                echo '</thead>';
                                echo '<tbody>';
                                
                                // Check @id
                                echo '<tr>';
                                echo '<td>@id</td>';
                                if (isset($faq_entity['@id'])) {
                                    echo '<td><span style="color:green;">✓</span></td>';
                                    echo '<td>' . esc_html($faq_entity['@id']) . '</td>';
                                } else {
                                    echo '<td><span style="color:red;">✗</span></td>';
                                    echo '<td>Property not found</td>';
                                }
                                echo '</tr>';
                                
                                // Check mainEntity
                                echo '<tr>';
                                echo '<td>mainEntity</td>';
                                if (isset($faq_entity['mainEntity']) && is_array($faq_entity['mainEntity'])) {
                                    echo '<td><span style="color:green;">✓</span></td>';
                                    echo '<td>Found ' . count($faq_entity['mainEntity']) . ' FAQ items</td>';
                                } else {
                                    echo '<td><span style="color:red;">✗</span></td>';
                                    echo '<td>Property not found or not an array</td>';
                                }
                                echo '</tr>';
                                
                                echo '</tbody>';
                                echo '</table>';
                            } else {
                                echo '<h3>FAQPage Entity</h3>';
                                echo '<p>No FAQPage entity found in the schema.</p>';
                            }
                            
                            // Check for TouristAttraction entities
                            $tourist_attractions = array();
                            foreach ($schema['@graph'] as $entity) {
                                if (isset($entity['@type']) && $entity['@type'] === 'TouristAttraction') {
                                    $tourist_attractions[] = $entity;
                                }
                            }
                            
                            if (!empty($tourist_attractions)) {
                                echo '<h3>TouristAttraction Entities (' . count($tourist_attractions) . ')</h3>';
                                
                                // Check the first tourist attraction
                                $attraction = $tourist_attractions[0];
                                
                                echo '<h4>First TouristAttraction: ' . (isset($attraction['name']) ? esc_html($attraction['name']) : 'Unnamed') . '</h4>';
                                echo '<table class="widefat">';
                                echo '<thead>';
                                echo '<tr>';
                                echo '<th>Property</th>';
                                echo '<th>Status</th>';
                                echo '<th>Value</th>';
                                echo '</tr>';
                                echo '</thead>';
                                echo '<tbody>';
                                
                                // Check @id
                                echo '<tr>';
                                echo '<td>@id</td>';
                                if (isset($attraction['@id'])) {
                                    echo '<td><span style="color:green;">✓</span></td>';
                                    echo '<td>' . esc_html($attraction['@id']) . '</td>';
                                } else {
                                    echo '<td><span style="color:red;">✗</span></td>';
                                    echo '<td>Property not found</td>';
                                }
                                echo '</tr>';
                                
                                // Check name
                                echo '<tr>';
                                echo '<td>name</td>';
                                if (isset($attraction['name'])) {
                                    echo '<td><span style="color:green;">✓</span></td>';
                                    echo '<td>' . esc_html($attraction['name']) . '</td>';
                                } else {
                                    echo '<td><span style="color:red;">✗</span></td>';
                                    echo '<td>Property not found</td>';
                                }
                                echo '</tr>';
                                
                                // Check address
                                echo '<tr>';
                                echo '<td>address</td>';
                                if (isset($attraction['address']) && isset($attraction['address']['@type']) && $attraction['address']['@type'] === 'PostalAddress') {
                                    echo '<td><span style="color:green;">✓</span></td>';
                                    echo '<td>Has proper PostalAddress structure</td>';
                                } else {
                                    echo '<td><span style="color:red;">✗</span></td>';
                                    echo '<td>Missing or invalid structure</td>';
                                }
                                echo '</tr>';
                                
                                // Check geo
                                echo '<tr>';
                                echo '<td>geo</td>';
                                if (isset($attraction['geo']) && isset($attraction['geo']['@type']) && $attraction['geo']['@type'] === 'GeoCoordinates') {
                                    echo '<td><span style="color:green;">✓</span></td>';
                                    echo '<td>Has proper GeoCoordinates structure</td>';
                                } else {
                                    echo '<td><span style="color:red;">✗</span></td>';
                                    echo '<td>Missing or invalid structure</td>';
                                }
                                echo '</tr>';
                                
                                // Check isAccessibleForFree
                                echo '<tr>';
                                echo '<td>isAccessibleForFree</td>';
                                if (isset($attraction['isAccessibleForFree'])) {
                                    echo '<td><span style="color:green;">✓</span></td>';
                                    echo '<td>' . ($attraction['isAccessibleForFree'] ? 'true' : 'false') . '</td>';
                                } else {
                                    echo '<td><span style="color:red;">✗</span></td>';
                                    echo '<td>Property not found</td>';
                                }
                                echo '</tr>';
                                
                                // Check additionalProperty
                                echo '<tr>';
                                echo '<td>additionalProperty</td>';
                                if (isset($attraction['additionalProperty']) && is_array($attraction['additionalProperty'])) {
                                    echo '<td><span style="color:green;">✓</span></td>';
                                    echo '<td>Found ' . count($attraction['additionalProperty']) . ' properties</td>';
                                } else {
                                    echo '<td><span style="color:red;">✗</span></td>';
                                    echo '<td>Property not found or not an array</td>';
                                }
                                echo '</tr>';
                                
                                echo '</tbody>';
                                echo '</table>';
                            } else {
                                echo '<h3>TouristAttraction Entities</h3>';
                                echo '<p>No TouristAttraction entities found in the schema.</p>';
                            }
                        }
                    }
                }
            }
            ?>
            
            <h3>Raw Schema JSON</h3>
            <div style="max-height: 300px; overflow: auto; background: #f5f5f5; padding: 10px; border: 1px solid #ddd;">
                <pre><?php echo esc_html(json_encode($schema, JSON_PRETTY_PRINT)); ?></pre>
            </div>
        <?php endif; ?>
    </div>
    <?php
}
