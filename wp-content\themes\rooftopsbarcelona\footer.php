<?php
/**
 * The template for displaying the footer
 */
?>

    <footer id="colophon" class="site-footer">
        <div class="spa-container">
            <div class="footer-bottom">
                <div class="footer-copyright">
                    <?php
                    $footer_text = get_theme_mod( 'spasinbarcelona_footer_text', '© ' . date( 'Y' ) . ' Spas in Barcelona. All rights reserved.' );
                    echo wp_kses_post( $footer_text );
                    ?>
                </div>

                <nav class="footer-navigation">
                    <?php
                    wp_nav_menu(
                        array(
                            'theme_location' => 'footer-menu', // Use the Footer Menu you created
                            'menu_class'     => 'footer-menu',
                            'depth'          => 1,
                            'fallback_cb'    => false,
                            'container'      => false,
                        )
                    );
                    ?>
                </nav>
            </div>
        </div>
    </footer><!-- #colophon -->

    <div class="back-to-top" style="position: fixed !important; bottom: 20px !important; right: 20px !important; width: 40px !important; height: 40px !important; background-color: #68a0a0 !important; color: #ffffff !important; border-radius: 50% !important; display: flex !important; align-items: center !important; justify-content: center !important; cursor: pointer !important; opacity: 0 !important; visibility: hidden !important; transition: all 0.3s ease !important; z-index: 99 !important;">
        <i class="fas fa-chevron-up" style="color: #ffffff !important;"></i>
    </div>
</div><!-- #page -->

<?php wp_footer(); ?>

<!-- Footer styles are now in footer-fix.css -->

<script>
    // Back to top button
    (function() {
        var backToTop = document.querySelector('.back-to-top');

        if (backToTop) {
            // Function to show/hide the back to top button
            function toggleBackToTop() {
                if (window.pageYOffset > 300) {
                    backToTop.style.opacity = '1';
                    backToTop.style.visibility = 'visible';
                } else {
                    backToTop.style.opacity = '0';
                    backToTop.style.visibility = 'hidden';
                }
            }

            // Initial check
            toggleBackToTop();

            // Add scroll event listener
            window.addEventListener('scroll', toggleBackToTop);

            // Add click event listener
            backToTop.addEventListener('click', function(e) {
                e.preventDefault();
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });

            // Add hover effect
            backToTop.addEventListener('mouseenter', function() {
                this.style.backgroundColor = '#d2b48c';
                this.style.transform = 'translateY(-5px)';
            });

            backToTop.addEventListener('mouseleave', function() {
                this.style.backgroundColor = '#68a0a0';
                this.style.transform = 'translateY(0)';
            });
        }
    })();
</script>

</body>
</html>
