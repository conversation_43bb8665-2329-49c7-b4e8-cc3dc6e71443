<?php
/**
 * The template for displaying spa neighborhood archives
 */

get_header();

// Get the neighborhood term - try multiple methods to ensure we have it
$term = get_queried_object();
if (!$term || is_wp_error($term) || !isset($term->taxonomy) || $term->taxonomy !== 'neighborhoods') { // Check for 'neighborhoods'
    // Try to get from custom query var
    $term = get_query_var('spa_neighborhood_term');

    // If still no term, try to get from URL
    if (!$term || is_wp_error($term)) {
        $url_parts = explode('/', trim($_SERVER['REQUEST_URI'], '/'));
        $term_slug = end($url_parts);

        // If the term slug is empty (due to trailing slash), get the previous part
        if (empty($term_slug)) {
            // Remove the last element (empty string)
            array_pop($url_parts);
            // Get the new last element
            $term_slug = end($url_parts);
        }

        // Special case for Vila Olímpica
        $is_vila_olimpica = (strtolower($term_slug) === 'vila-olimpica' || strtolower($term_slug) === 'vila-olímpica');

        if ($is_vila_olimpica) {
            // Get or create the Vila Olímpica term
            $term = get_term_by('slug', 'vila-olimpica', 'neighborhoods'); // Use 'neighborhoods'

            if (!$term || is_wp_error($term)) {
                // Create the term if it doesn't exist
                $result = wp_insert_term(
                    'Vila Olímpica',
                    'neighborhoods', // Use 'neighborhoods'
                    array(
                        'description' => 'Discover the best spas in the Vila Olímpica neighborhood of Barcelona. This modern area near the beach offers luxury spa experiences with sea views.',
                        'slug' => 'vila-olimpica'
                    )
                );

                if (!is_wp_error($result)) {
                    $term = get_term($result['term_id'], 'neighborhoods'); // Use 'neighborhoods'
                }
            }

            // Get the Mandarin Oriental spa
            $mandarin = get_page_by_path('the-spa-at-mandarin-oriental-barcelona', OBJECT, 'spa');

            // If we have both the term and the spa, make sure they're connected
            if ($term && $mandarin) {
                wp_set_object_terms($mandarin->ID, $term->term_id, 'neighborhoods', true); // Use 'neighborhoods'

                // Update the spa's location meta
                $location = get_post_meta($mandarin->ID, 'location', true);

                if (empty($location) || !is_array($location)) {
                    $location = array();
                }

                $location['neighborhood'] = 'Vila Olímpica';
                $location['district'] = 'Sant Martí';

                update_post_meta($mandarin->ID, 'location', $location);

                // Clear caches
                clean_object_term_cache($mandarin->ID, 'spa');
                clean_term_cache($term->term_id, 'neighborhoods'); // Use 'neighborhoods'
            }
        } else {
            // Try to get the term by slug for other neighborhoods
            $term = get_term_by('slug', $term_slug, 'neighborhoods'); // Use 'neighborhoods'
        }
    }
}
?>

<?php $paged_value = ( get_query_var( 'paged' ) ) ? absint( get_query_var( 'paged' ) ) : 1; ?>

<div id="primary" class="content-area">
    <main id="main" class="site-main">
        <header class="page-header">
            <div class="page-title-wrapper">
                <?php if ($term && !is_wp_error($term)) : ?>
                    <?php if ($term->slug === 'vila-olimpica') : ?>
                        <h1 class="page-title">Best Spas in Vila Olímpica, Barcelona</h1>
                    <?php else : ?>
                        <h1 class="page-title">Best Spas in <?php echo esc_html($term->name); ?>, Barcelona</h1>
                    <?php endif; ?>
                <?php else : ?>
                    <h1 class="page-title"><?php echo wp_kses_post(get_the_archive_title()); ?></h1>
                <?php endif; ?>
            </div>
            <div class="archive-description">
                <?php
                if ($term && !is_wp_error($term)) {
                    if ($term->slug === 'vila-olimpica') {
                        echo '<p>Discover the best spas in the Vila Olímpica neighborhood of Barcelona. This modern area near the beach offers luxury spa experiences with sea views.</p>';
                    } else if (!empty($term->description)) {
                        echo wp_kses_post($term->description);
                    } else {
                        // Otherwise, generate a default description
                        echo '<p>Discover the best spas in the ' . esc_html($term->name) . ' neighborhood of Barcelona. Browse our selection of spas in this area.</p>';
                    }
                } else {
                    // Fallback to the default archive description
                    echo wp_kses_post(get_the_archive_description());
                }
                ?>
            </div>
        </header>

        <div class="spa-results" style="width: 100%;">
            <?php
            // First try to use the custom query if available
            $neighborhood_query = get_query_var('neighborhood_query');

            if ($neighborhood_query && $neighborhood_query instanceof WP_Query) {
                // Use the custom query
                if ($neighborhood_query->have_posts()) :
                ?>
                    <div class="spa-grid">
                        <?php
                        while ($neighborhood_query->have_posts()) :
                            $neighborhood_query->the_post();
                            get_template_part('template-parts/content', 'spa-card');
                        endwhile;
                        wp_reset_postdata();
                        ?>
                    </div>
                <?php else : ?>
                    <div class="no-results">
                        <p><?php esc_html_e('No spas found in this neighborhood.', 'spasinbarcelona'); ?></p>
                    </div>
                <?php endif;
            } else if ($term && !is_wp_error($term)) {
                // Special case for Vila Olímpica
                if ($term->slug === 'vila-olimpica') {
                    // First try to get spas with the normal query
                    $args = array(
                        'post_type' => 'spa',
                        'posts_per_page' => -1, // Display all matching spas
                        'paged' => $paged_value,
                        'tax_query' => array(
                            array(
                                'taxonomy' => $term->taxonomy, // Should be 'neighborhoods'
                                'field'    => 'slug',
                                'terms'    => $term->slug,
                            ),
                        ),
                    );

                    $spa_query = new WP_Query($args);

                    // If no posts found, manually add the Mandarin Oriental spa
                    if (!$spa_query->have_posts()) {
                        // Get the Mandarin Oriental spa
                        $mandarin = get_page_by_path('the-spa-at-mandarin-oriental-barcelona', OBJECT, 'spa');

                        if ($mandarin) {
                            // Create a custom query with just this spa
                            $args = array(
                                'post_type' => 'spa',
                                'posts_per_page' => -1,
                                'post__in' => array($mandarin->ID),
                            );

                            $spa_query = new WP_Query($args);
                        }
                    }

                    // Debug info for administrators
                    if (current_user_can('administrator')) {
                        echo '<!-- Vila Olímpica Special Case -->';
                        echo '<!-- Query Args: ' . print_r($args, true) . ' -->';
                        echo '<!-- Term ID: ' . $term->term_id . ' | Name: ' . $term->name . ' | Slug: ' . $term->slug . ' -->';
                        echo '<!-- Post Count: ' . $spa_query->post_count . ' -->';
                    }
                } else {
                    // Standard query for other neighborhoods
                    $args = array(
                        'post_type' => 'spa',
                        'posts_per_page' => -1, // Display all matching spas
                        'paged' => $paged_value,
                        'tax_query' => array(
                            array(
                                'taxonomy' => $term->taxonomy, // Should be 'neighborhoods'
                                'field'    => 'slug',
                                'terms'    => $term->slug,
                            ),
                        ),
                    );

                    // Debug info for administrators
                    if (current_user_can('administrator')) {
                        echo '<!-- Query Args: ' . print_r($args, true) . ' -->';
                        echo '<!-- Term ID: ' . $term->term_id . ' | Name: ' . $term->name . ' | Slug: ' . $term->slug . ' -->';
                    }

                    $spa_query = new WP_Query($args);
                }

                if ($spa_query->have_posts()) :
                ?>
                    <div class="spa-grid">
                        <?php
                        while ($spa_query->have_posts()) :
                            $spa_query->the_post();
                            get_template_part('template-parts/content', 'spa-card');
                        endwhile;
                        wp_reset_postdata();
                        ?>
                    </div>
                <?php else : ?>
                    <div class="no-results">
                        <p><?php esc_html_e('No spas found in this neighborhood.', 'spasinbarcelona'); ?></p>
                    </div>
                <?php endif;
            } else {
                // Fallback to the default query
                if (have_posts()) : ?>
                    <div class="spa-grid">
                        <?php
                        while (have_posts()) :
                            the_post();
                            get_template_part('template-parts/content', 'spa-card');
                        endwhile;
                        ?>
                    </div>
                <?php else : ?>
                    <div class="no-results">
                        <p><?php esc_html_e('No spas found in this neighborhood.', 'spasinbarcelona'); ?></p>
                    </div>
                <?php endif;
            }
            ?>

        <div class="spa-pagination">
            <?php
            $pagination_total_pages = 0;
            $pagination_current_page = $paged_value;

            if ( isset( $spa_query ) && $spa_query instanceof WP_Query && $spa_query->max_num_pages > 1 ) {
                // Used if the $spa_query from the 'else if ($term && !is_wp_error($term))' block was executed
                $pagination_total_pages = $spa_query->max_num_pages;
            } elseif ( isset( $neighborhood_query ) && $neighborhood_query instanceof WP_Query && $neighborhood_query->max_num_pages > 1 ) {
                // Used if $neighborhood_query (from query_var) was executed and is the main query
                $pagination_total_pages = $neighborhood_query->max_num_pages;
            } elseif ( !isset( $spa_query ) && !isset( $neighborhood_query ) && $wp_query->max_num_pages > 1 ) {
                // Fallback to global $wp_query if no custom query took over for pagination
                $pagination_total_pages = $wp_query->max_num_pages;
            }

            if ( $pagination_total_pages > 1 ) :
                $big = 999999999; // need an unlikely integer
                echo paginate_links( array(
                    'base'      => str_replace( $big, '%#%', esc_url( get_pagenum_link( $big ) ) ),
                    'format'    => '?paged=%#%',
                    'current'   => $pagination_current_page,
                    'total'     => $pagination_total_pages,
                    'prev_text' => '<i class="fas fa-chevron-left"></i> Previous',
                    'next_text' => 'Next <i class="fas fa-chevron-right"></i>',
                ) );
            endif;
            ?>
        </div>

        <!-- Navigation Buttons -->
        <div class="spa-navigation-buttons">
            <a href="<?php echo esc_url( get_post_type_archive_link( 'spa' ) ); ?>" class="spa-button">
                View All Spas <i class="fas fa-arrow-right"></i>
            </a>
            <a href="<?php echo esc_url( home_url() ); ?>" class="spa-button">
                <i class="fas fa-home"></i> Back To Homepage
            </a>
        </div>

        <?php
        // Display child neighborhoods if this is a district (parent term)
        $term_obj_children = get_queried_object(); // Use a different var name
        if ($term_obj_children && !is_wp_error($term_obj_children) && isset($term_obj_children->term_id) && !$term_obj_children->parent) {
            $child_terms = get_terms(array(
                'taxonomy' => 'neighborhoods', // Use 'neighborhoods'
                'parent' => $term_obj_children->term_id,
                'hide_empty' => true,
            ));

            if (!empty($child_terms) && !is_wp_error($child_terms)) {
                echo '<div class="spa-section">';
                echo '<h2 class="spa-section-title">Neighborhoods in ' . esc_html($term_obj_children->name) . '</h2>';
                echo '<div class="neighborhood-grid">';

                foreach ($child_terms as $child) {
                    $child_link = get_term_link($child);
                    echo '<a href="' . esc_url($child_link) . '" class="neighborhood-card">';
                    echo '<h3 class="neighborhood-card-title">' . esc_html($child->name) . '</h3>';
                    echo '<span class="neighborhood-card-count">' . $child->count . ' ' . _n('Spa', 'Spas', $child->count, 'spasinbarcelona') . '</span>';
                    echo '</a>';
                }

                echo '</div>';
                echo '</div>';
            }
        }
        ?>
    </main>
</div>

<?php
get_footer();