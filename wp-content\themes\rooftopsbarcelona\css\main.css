/**
 * Main stylesheet for Spas in Barcelona theme
 *
 * Imports:
 * - spa-card.css
 * - spa-single.css
 * - spa-archive.css
 * - spa-services.css
 * - spa-popular.css
 * - spa-neighborhood.css
 */

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Variables */
:root {
    /* Relaxing spa color palette */
    --primary-color: #68a0a0; /* Soft teal */
    --secondary-color: #d2b48c; /* Warm sand/beige */
    --accent-color: #8fb9aa; /* Sage green */
    --dark-color: #3a4a4a; /* Deep teal */
    --light-color: #f5f5f0; /* Soft off-white */
    --text-color: #3a4a4a; /* Deep teal for text */
    --light-text-color: #ffffff; /* White */
    --border-color: #e8e8e0; /* Soft beige border */
    --success-color: #8fb9aa; /* Sage green */
    --warning-color: #e6c89c; /* Light sand */
    --error-color: #c98276; /* Soft terracotta */
    --font-primary: 'Poppins', sans-serif;
    --font-secondary: 'Playfair Display', serif;
    --border-radius: 8px; /* Slightly more rounded corners */
    --box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05); /* Softer shadow */
    --transition: all 0.4s ease; /* Slightly slower transitions for a more relaxed feel */
}

/* Base Styles */
body {
    font-family: var(--font-primary);
    color: var(--text-color);
    line-height: 1.8; /* Increased line height for better readability */
    background-color: var(--light-color); /* Using our soft off-white background */
    letter-spacing: 0.01em; /* Slightly increased letter spacing for a more relaxed feel */
}

h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-secondary);
    font-weight: 600; /* Slightly lighter weight for a softer look */
    margin-bottom: 1.2rem;
    color: var(--dark-color);
    letter-spacing: 0.02em; /* Slightly increased letter spacing */
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

a:hover {
    color: var(--secondary-color);
}

img {
    max-width: 100%;
    height: auto;
}

/* Buttons */
.spa-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 14px 28px; /* Slightly larger padding */
    background-color: var(--primary-color);
    color: var(--light-text-color);
    border-radius: 30px; /* Rounded buttons for a softer look */
    font-weight: 500; /* Lighter font weight */
    text-align: center;
    transition: var(--transition);
    border: none;
    cursor: pointer;
    letter-spacing: 0.03em; /* Slightly increased letter spacing */
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05); /* Subtle shadow */
}

.spa-button:hover {
    background-color: #f5a623;
    color: var(--light-text-color);
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.08); /* Enhanced shadow on hover */
}

.spa-button.secondary {
    background-color: var(--secondary-color);
}

.spa-button.secondary:hover {
    background-color: var(--primary-color);
}

.spa-button.outline {
    background-color: transparent;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
}

.spa-button.outline:hover {
    background-color: var(--primary-color);
    color: var(--light-text-color);
}

/* Container */
.spa-container:not(.site-header .spa-container),
.content-area {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
    width: 100%;
}

/* Grid System */
.spa-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;
    margin: 30px 0;
}

@media (max-width: 768px) {
    .spa-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 20px;
    }
}

@media (max-width: 480px) {
    .spa-grid {
        grid-template-columns: 1fr;
    }
}

/* Section Styles */
.spa-section-header {
    text-align: center;
    margin-bottom: 25px;
}

.spa-section-title {
    font-size: 2.5rem;
    margin-bottom: 10px;
    color: var(--dark-color);
}

.spa-section-subtitle {
    font-size: 1.1rem;
    color: #666;
    max-width: 700px;
    margin: 0 auto;
}

.spa-section-footer {
    text-align: center;
    margin-top: 25px;
}

/* Utility Classes */
.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

.text-left {
    text-align: left;
}

.mb-0 {
    margin-bottom: 0;
}

.mb-1 {
    margin-bottom: 0.5rem;
}

.mb-2 {
    margin-bottom: 1rem;
}

.mb-3 {
    margin-bottom: 1.5rem;
}

.mb-4 {
    margin-bottom: 2rem;
}

.mt-0 {
    margin-top: 0;
}

.mt-1 {
    margin-top: 0.5rem;
}

.mt-2 {
    margin-top: 1rem;
}

.mt-3 {
    margin-top: 1.5rem;
}

.mt-4 {
    margin-top: 2rem;
}

.p-0 {
    padding: 0;
}

.p-1 {
    padding: 0.5rem;
}

.p-2 {
    padding: 1rem;
}

.p-3 {
    padding: 1.5rem;
}

.p-4 {
    padding: 2rem;
}

/* Override GeneratePress styles */
.site-header {
    background-color: var(--light-color);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    padding: 18px 0;
    border-bottom: 1px solid rgba(210, 180, 140, 0.2); /* Subtle border with secondary color */
}

.main-navigation {
    background-color: transparent;
}

.main-navigation .main-nav ul li a {
    color: var(--dark-color);
    font-weight: 500;
    padding: 10px 15px;
}

.main-navigation .main-nav ul li:hover > a,
.main-navigation .main-nav ul li.current-menu-item > a {
    color: var(--primary-color);
    background-color: transparent;
}

.main-navigation .main-nav ul ul {
    background-color: #fff;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.main-navigation .main-nav ul ul li a {
    padding: 10px 15px;
}

.site-footer {
    background-color: var(--dark-color);
    color: #f5f5f0;
    padding: 60px 0 30px;
    border-top: 5px solid var(--primary-color); /* Add a subtle border top */
}

.site-info {
    background-color: rgba(58, 74, 74, 0.95); /* Slightly lighter than dark color */
    color: #f5f5f0;
    padding: 20px 0;
    text-align: center;
    border-top: 1px solid rgba(255, 255, 255, 0.1); /* Subtle separator */
}

/* Spa Card Styles */
.spa-card {
    background-color: #fff;
    border-radius: 12px; /* More rounded corners */
    box-shadow: var(--box-shadow);
    overflow: hidden;
    transition: var(--transition);
    height: 100%;
    display: flex;
    flex-direction: column;
    margin-bottom: 30px;
    border: 1px solid rgba(210, 180, 140, 0.2); /* Very subtle border */
}

.spa-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(104, 160, 160, 0.15); /* Shadow with primary color */
    border-color: var(--primary-color);
}

.spa-card-image {
    position: relative;
    height: 200px;
    overflow: hidden;
    margin: 0;
    padding: 0;
    border-radius: 12px 12px 0 0;
    background-color: #000;
}

.spa-card-image a {
    display: block;
    height: 100%;
    width: 100%;
    margin: 0;
    padding: 0;
    line-height: 0;
    font-size: 0;
}

.spa-card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
    margin: 0;
    padding: 0;
    object-position: center;
    line-height: 0;
    font-size: 0;
    border-radius: 12px 12px 0 0;
}

.spa-card-content {
    padding: 20px;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
}

.spa-card-title {
    font-size: 1.3rem;
    margin-bottom: 10px;
    line-height: 1.3;
}

.spa-card-title a {
    color: var(--dark-color);
    text-decoration: none;
}

.spa-card-title a:hover {
    color: var(--primary-color);
}

/* Responsive Fixes */
@media (max-width: 768px) {
    .site-header {
        padding: 10px 0;
    }

    .spa-section-title {
        font-size: 2rem;
    }

    .spa-card-image {
        height: 180px;
    }
}

@media (max-width: 480px) {
    .spa-section-title {
        font-size: 1.6rem;
    }

    .spa-card-image {
        height: 160px;
    }
}
