<?php
/**
 * Customizer settings for this theme
 */

/**
 * Add customizer settings
 */
function spasinbarcelona_customize_register( $wp_customize ) {
    // Add section for spa directory settings
    $wp_customize->add_section( 'spasinbarcelona_directory_settings', array(
        'title'    => __( 'Spa Directory Settings', 'spasinbarcelona' ),
        'priority' => 130,
    ) );

    // Add setting for number of spas per page
    $wp_customize->add_setting( 'spasinbarcelona_spas_per_page', array(
        'default'           => 12,
        'sanitize_callback' => 'absint',
    ) );

    $wp_customize->add_control( 'spasinbarcelona_spas_per_page', array(
        'label'       => __( 'Spas Per Page', 'spasinbarcelona' ),
        'description' => __( 'Number of spas to display per page in the directory.', 'spasinbarcelona' ),
        'section'     => 'spasinbarcelona_directory_settings',
        'type'        => 'number',
        'input_attrs' => array(
            'min'  => 4,
            'max'  => 48,
            'step' => 4,
        ),
    ) );

    // Add setting for featured spas
    $wp_customize->add_setting( 'spasinbarcelona_featured_spas', array(
        'default'           => '',
        'sanitize_callback' => 'spasinbarcelona_sanitize_multiple_checkboxes',
    ) );

    // Get all spas for the control
    $spas = get_posts( array(
        'post_type'      => 'spa',
        'posts_per_page' => -1,
        'orderby'        => 'title',
        'order'          => 'ASC',
    ) );

    $spa_choices = array();

    if ( $spas ) {
        foreach ( $spas as $spa ) {
            $spa_choices[ $spa->ID ] = $spa->post_title;
        }
    }

    $wp_customize->add_control( new WP_Customize_Control( $wp_customize, 'spasinbarcelona_featured_spas', array(
        'label'       => __( 'Featured Spas', 'spasinbarcelona' ),
        'description' => __( 'Select spas to feature on the homepage.', 'spasinbarcelona' ),
        'section'     => 'spasinbarcelona_directory_settings',
        'type'        => 'select',
        'multiple'    => true,
        'choices'     => $spa_choices,
    ) ) );

    // Add setting for primary color
    $wp_customize->add_setting( 'spasinbarcelona_primary_color', array(
        'default'           => '#68a0a0', // Soft teal - relaxing spa color
        'sanitize_callback' => 'sanitize_hex_color',
    ) );

    $wp_customize->add_control( new WP_Customize_Color_Control( $wp_customize, 'spasinbarcelona_primary_color', array(
        'label'    => __( 'Primary Color', 'spasinbarcelona' ),
        'section'  => 'spasinbarcelona_directory_settings',
        'settings' => 'spasinbarcelona_primary_color',
    ) ) );

    // Add setting for secondary color
    $wp_customize->add_setting( 'spasinbarcelona_secondary_color', array(
        'default'           => '#d2b48c', // Warm sand/beige - relaxing spa color
        'sanitize_callback' => 'sanitize_hex_color',
    ) );

    $wp_customize->add_control( new WP_Customize_Color_Control( $wp_customize, 'spasinbarcelona_secondary_color', array(
        'label'    => __( 'Secondary Color', 'spasinbarcelona' ),
        'section'  => 'spasinbarcelona_directory_settings',
        'settings' => 'spasinbarcelona_secondary_color',
    ) ) );

    // Add setting for homepage intro text
    $wp_customize->add_setting( 'spasinbarcelona_homepage_intro', array(
        'default'           => __( 'Discover the best spas in Barcelona with our comprehensive directory. Find luxury spas, wellness centers, and beauty treatments across Barcelona.', 'spasinbarcelona' ),
        'sanitize_callback' => 'wp_kses_post',
    ) );

    $wp_customize->add_control( 'spasinbarcelona_homepage_intro', array(
        'label'       => __( 'Homepage Intro Text', 'spasinbarcelona' ),
        'description' => __( 'Introduction text displayed on the homepage.', 'spasinbarcelona' ),
        'section'     => 'spasinbarcelona_directory_settings',
        'type'        => 'textarea',
    ) );

    // Add setting for footer text
    $wp_customize->add_setting( 'spasinbarcelona_footer_text', array(
        'default'           => __( '© 2025 Spas in Barcelona. All rights reserved.', 'spasinbarcelona' ),
        'sanitize_callback' => 'wp_kses_post',
    ) );

    $wp_customize->add_control( 'spasinbarcelona_footer_text', array(
        'label'       => __( 'Footer Text', 'spasinbarcelona' ),
        'description' => __( 'Text displayed in the footer.', 'spasinbarcelona' ),
        'section'     => 'spasinbarcelona_directory_settings',
        'type'        => 'textarea',
    ) );

    // Add setting for Google Maps API key
    $wp_customize->add_setting( 'spasinbarcelona_google_maps_api_key', array(
        'default'           => '',
        'sanitize_callback' => 'sanitize_text_field',
    ) );

    $wp_customize->add_control( 'spasinbarcelona_google_maps_api_key', array(
        'label'       => __( 'Google Maps API Key', 'spasinbarcelona' ),
        'description' => __( 'Enter your Google Maps API key to enable maps on spa pages.', 'spasinbarcelona' ),
        'section'     => 'spasinbarcelona_directory_settings',
        'type'        => 'text',
    ) );
}
add_action( 'customize_register', 'spasinbarcelona_customize_register' );

/**
 * Sanitize multiple checkboxes
 */
function spasinbarcelona_sanitize_multiple_checkboxes( $values ) {
    $multi_values = ! is_array( $values ) ? explode( ',', $values ) : $values;

    return ! empty( $multi_values ) ? array_map( 'sanitize_text_field', $multi_values ) : array();
}

/**
 * Output customizer CSS
 */
function spasinbarcelona_customizer_css() {
    $primary_color = get_theme_mod( 'spasinbarcelona_primary_color', '#68a0a0' );
    $secondary_color = get_theme_mod( 'spasinbarcelona_secondary_color', '#d2b48c' );

    ?>
    <style type="text/css">
        :root {
            --primary-color: <?php echo esc_attr( $primary_color ); ?>;
            --secondary-color: <?php echo esc_attr( $secondary_color ); ?>;
            --accent-color: #8fb9aa; /* Sage green */
            --dark-color: #3a4a4a; /* Deep teal */
            --light-color: #f5f5f0; /* Soft off-white */
            --text-color: #3a4a4a; /* Deep teal for text */
            --border-color: #e8e8e0; /* Soft beige border */
        }

        .spa-card .spa-category,
        .spa-filter-form button,
        .spa-pagination .current,
        .spa-features-list i,
        .spa-social-links a:hover,
        .spa-cta-button {
            background-color: var(--primary-color);
        }

        .spa-card:hover,
        .spa-filter-form select:focus,
        .spa-filter-form input:focus,
        .spa-tabs-nav .active {
            border-color: var(--primary-color);
        }

        .spa-rating .fas,
        .spa-price-range,
        .spa-breadcrumbs a,
        .spa-tabs-nav .active,
        .spa-amenities-list i {
            color: var(--primary-color);
        }

        .spa-featured-badge,
        .spa-pagination a:hover,
        .spa-cta-button:hover,
        .spa-tag {
            background-color: var(--secondary-color);
        }

        .spa-price,
        .spa-contact-info i,
        .spa-review-count {
            color: var(--secondary-color);
        }

        /* Google Maps error styling */
        .spa-map-error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 20px;
            border-radius: 12px;
            margin-top: 20px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .spa-map-error p {
            margin: 0;
            font-size: 16px;
        }

        .spa-map-error i {
            margin-right: 8px;
            color: #dc3545;
        }
    </style>
    <?php
}
add_action( 'wp_head', 'spasinbarcelona_customizer_css' );
