/**
 * Spa filter styles for Spas in Barcelona theme
 */

/* Filter Container */
.spa-filter-container {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: var(--box-shadow);
    padding: 20px;
    margin-bottom: 30px;
}

/* Filter Form */
.spa-filter-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.spa-filter-row {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

.spa-filter-field {
    flex: 1;
    min-width: 200px;
}

.spa-filter-field label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--dark-color);
}

.spa-filter-field select,
.spa-filter-field input[type="text"] {
    width: 100%;
    padding: 10px 15px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background-color: #f8f9fa;
    transition: var(--transition);
}

.spa-filter-field select:focus,
.spa-filter-field input[type="text"]:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
}

.spa-search-field {
    flex: 2;
}

.spa-submit-field {
    display: flex;
    align-items: flex-end;
}

.spa-filter-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12px 24px;
    background-color: var(--primary-color);
    color: #fff;
    border: none;
    border-radius: 4px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
}

.spa-filter-button:hover {
    background-color: var(--secondary-color);
    transform: translateY(-2px);
}

.spa-filter-button i {
    margin-right: 8px;
}

/* Active Filters */
.spa-active-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 20px;
}

.spa-active-filter {
    display: inline-flex;
    align-items: center;
    background-color: #f8f9fa;
    border: 1px solid var(--border-color);
    border-radius: 50px;
    padding: 5px 15px;
    font-size: 0.9rem;
    transition: var(--transition);
}

.spa-active-filter:hover {
    background-color: #f1f1f1;
}

.spa-active-filter-label {
    font-weight: 600;
    margin-right: 5px;
}

.spa-active-filter-remove {
    margin-left: 8px;
    width: 18px;
    height: 18px;
    background-color: #ddd;
    color: #666;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    transition: var(--transition);
}

.spa-active-filter:hover .spa-active-filter-remove {
    background-color: var(--primary-color);
    color: #fff;
}

.spa-clear-filters {
    display: inline-flex;
    align-items: center;
    color: var(--primary-color);
    font-size: 0.9rem;
    font-weight: 500;
    transition: var(--transition);
}

.spa-clear-filters:hover {
    color: var(--secondary-color);
}

.spa-clear-filters i {
    margin-right: 5px;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .spa-filter-container {
        padding: 15px;
    }
    
    .spa-filter-row {
        flex-direction: column;
        gap: 10px;
    }
    
    .spa-filter-field {
        width: 100%;
    }
    
    .spa-filter-button {
        width: 100%;
    }
}
