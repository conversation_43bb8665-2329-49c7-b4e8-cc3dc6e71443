<?php
/**
 * The template for displaying spa archive pages
 */

get_header();
?>

<div id="primary" class="content-area">
    <main id="main" class="site-main">
        <header class="page-header">
            <div class="page-title-wrapper">
                <h1 class="page-title"><?php echo wp_kses_post( get_the_archive_title() ); ?></h1>
            </div>
            <div class="archive-description">
                <?php
                // Check if we have a custom archive description
                $archive_description = get_the_archive_description();

                // If no custom description, provide a default one for SEO
                if (empty($archive_description)) {
                    if (is_post_type_archive('spa')) {
                        echo '<p>Welcome to our comprehensive directory of spas in Barcelona. Discover luxury spa experiences, wellness centers, and beauty treatments across the city. Use our advanced filters to find the perfect spa based on services, amenities, location, and price range.</p>';
                        echo '<p>Whether you\'re looking for a relaxing massage, facial treatment, thermal baths, or a complete wellness experience, our directory helps you find the best spas in Barcelona to meet your needs. Each spa listing includes detailed information, photos, services offered, pricing, and authentic reviews.</p>';
                    } elseif (is_tax('specialties')) {
                        $term = get_queried_object();
                        $term_name = esc_html($term->name);
                        echo "<p>Discover Barcelona's finest spas specializing in {$term_name}. Whether you're seeking unique {$term_name} treatments or expert therapists, find your perfect wellness sanctuary here. Explore detailed listings, compare options, and book your specialized spa day.</p>";
                    } elseif (is_tax('services')) {
                        $term = get_queried_object();
                        $term_name = esc_html(strtolower($term->name));
                        echo "<p>Indulge in premier {$term_name} services at Barcelona's top spas. From rejuvenating massages to advanced skincare, our curated selection offers the best {$term_name}. Find detailed service menus, read reviews, and book your ultimate pampering session today.</p>";
                    } elseif (is_tax('amenities')) {
                        $term = get_queried_object();
                        $term_name = esc_html(strtolower($term->name));
                        echo "<p>Experience luxury and comfort with spas in Barcelona offering {$term_name}. Enhance your visit with exceptional facilities and enjoy a truly memorable spa day. Browse spas equipped with {$term_name} and other premium features.</p>";
                    } elseif (is_tax('neighborhoods')) {
                        $term = get_queried_object();
                        $term_name = esc_html($term->name);
                        if ($term && $term->parent == 0) {
                            echo "<p>Explore a diverse range of spas in the {$term_name} district of Barcelona. From tranquil retreats to urban wellness centers, {$term_name} offers something for everyone. Discover detailed spa profiles, services, and book your escape in this vibrant Barcelona district.</p>";
                        } elseif ($term) {
                            $parent_term_name = '';
                            if ($term->parent) {
                                $parent_term = get_term($term->parent, 'neighborhoods');
                                if ($parent_term && !is_wp_error($parent_term)) {
                                    $parent_term_name = ' in the ' . esc_html($parent_term->name) . ' district';
                                }
                            }
                            echo "<p>Find serene spas and wellness havens in the charming {$term_name} neighborhood{$parent_term_name}, Barcelona. Unwind with tailored treatments and enjoy a local spa experience. Browse our selection of spas in {$term_name} for your next relaxation day.</p>";
                        }
                    } elseif (is_tax('popular')) {
                        $term = get_queried_object();
                        $term_name = esc_html(strtolower($term->name));
                        echo "<p>Discover why {$term_name} is a popular choice for spa-goers in Barcelona. Explore our top-rated spas renowned for their exceptional {$term_name} offerings, ambiance, and guest satisfaction. Find your perfect spa experience based on this sought-after characteristic.</p>";
                    }
                } else {
                    echo wp_kses_post($archive_description); // Keep this for manually set descriptions
                }
                ?>
            </div>
        </header>

        <div class="spa-archive-additional-info spa-container">
            <h2>Discover Barcelona's Diverse Spa Scene</h2>
            <p>From traditional hammams nestled in historic districts to cutting-edge wellness centers boasting panoramic city views, Barcelona offers an incredibly diverse range of spa experiences. Whether you're a local resident seeking regular rejuvenation or a visitor looking for a special treat, you'll find facilities catering to every preference and budget. Our directory helps you navigate this vibrant scene to find your perfect sanctuary.</p>
            <h2>Tips for Choosing Your Ideal Spa in Barcelona</h2>
            <p>When selecting a spa, consider what you're hoping to achieve. Are you looking for a quick, invigorating treatment, a full day of luxurious pampering, or specific therapeutic services like hydrotherapy or ancient healing rituals? Think about the ambiance you prefer – a tranquil, meditative space or a more social, vibrant environment? Also, consider location convenience and whether the spa offers packages or amenities that align with your needs, such as couples' rooms, private suites, or post-treatment relaxation lounges. Using our filters for services, amenities, and neighborhoods can greatly simplify your search for the perfect spa in Barcelona.</p>
        </div>

        <div class="spa-archive-layout">
            <!-- Left Column - Filters -->
            <div class="spa-filter-sidebar">
                <div class="spa-filter-container">
                    <form class="spa-filter-form" method="get">
                        <div class="spa-filter-field">
                            <label for="popular_filter">Most Popular</label> <!-- Changed id and name -->
                            <select name="popular_filter" id="popular_filter" class="spa-filter-select"> <!-- Changed id and name -->
                                <option value="">All Popular</option>
                                <?php
                                $popular_terms = get_terms( array(
                                    'taxonomy' => 'popular', // Use the new 'popular' taxonomy
                                    'hide_empty' => true, // Only show terms with associated spas
                                    'orderby' => 'name',
                                    'order' => 'ASC'
                                ) );

                                if ( ! empty( $popular_terms ) && ! is_wp_error( $popular_terms ) ) {
                                    foreach ( $popular_terms as $term ) {
                                        echo '<option value="' . esc_attr( $term->slug ) . '" ' . selected( get_query_var('popular_filter'), $term->slug, false ) . '>' . esc_html( $term->name ) . '</option>';
                                    }
                                }
                                ?>
                            </select>
                        </div>

                        <div class="spa-filter-field">
                            <label for="service_filter">Service</label> <!-- Changed id and name -->
                            <select name="service_filter" id="service_filter" class="spa-filter-select"> <!-- Changed id and name -->
                                <option value="">All Services</option>
                                <?php
                                $services_terms = get_terms( array(
                                    'taxonomy' => 'services', // Use the new 'services' taxonomy
                                    'hide_empty' => true,
                                    'orderby' => 'name',
                                    'order' => 'ASC'
                                ) );

                                if ( ! empty( $services_terms ) && ! is_wp_error( $services_terms ) ) {
                                    foreach ( $services_terms as $term ) {
                                        echo '<option value="' . esc_attr( $term->slug ) . '" ' . selected( get_query_var('service_filter'), $term->slug, false ) . '>' . esc_html( $term->name ) . '</option>';
                                    }
                                }
                                ?>
                            </select>
                        </div>

                        <div class="spa-filter-field">
                            <label for="amenities_filter">Amenities</label> <!-- Changed id and name -->
                            <select name="amenities_filter" id="amenities_filter" class="spa-filter-select"> <!-- Changed id and name -->
                                <option value="">All Amenities</option>
                                <?php
                                $amenities_terms = get_terms( array(
                                    'taxonomy' => 'amenities', // Use the new 'amenities' taxonomy
                                    'hide_empty' => true,
                                    'orderby' => 'name',
                                    'order' => 'ASC'
                                ) );

                                if ( ! empty( $amenities_terms ) && ! is_wp_error( $amenities_terms ) ) {
                                    foreach ( $amenities_terms as $term ) {
                                        echo '<option value="' . esc_attr( $term->slug ) . '" ' . selected( get_query_var('amenities_filter'), $term->slug, false ) . '>' . esc_html( $term->name ) . '</option>';
                                    }
                                }
                                ?>
                            </select>
                        </div>

                        <div class="spa-filter-field">
                            <label for="neighborhood_filter">Neighborhood</label> <!-- Changed id and name -->
                            <select name="neighborhood_filter" id="neighborhood_filter" class="spa-filter-select"> <!-- Changed id and name -->
                                <option value="">All Neighborhoods</option>
                                <?php
                                $neighborhood_terms = get_terms( array(
                                    'taxonomy' => 'neighborhoods', // Use the new 'neighborhoods' taxonomy
                                    'hide_empty' => true,
                                    'orderby' => 'name',
                                    'order' => 'ASC'
                                ) );

                                if ( ! empty( $neighborhood_terms ) && ! is_wp_error( $neighborhood_terms ) ) {
                                    foreach ( $neighborhood_terms as $term ) {
                                        echo '<option value="' . esc_attr( $term->slug ) . '" ' . selected( get_query_var('neighborhood_filter'), $term->slug, false ) . '>' . esc_html( $term->name ) . '</option>';
                                    }
                                }
                                ?>
                            </select>
                        </div>

                        <!-- Price Range filter removed -->

                        <div class="spa-filter-field">
                            <label for="sort_by">Sort by</label>
                            <select name="sort_by" id="sort_by" class="spa-filter-select">
                                <option value="">Default</option>
                                <option value="alphabetical">Alphabetical (A-Z)</option>
                                <option value="rating">Rating (High to Low)</option>
                            </select>
                        </div>

                        <div class="spa-filter-field spa-search-field">
                            <label for="search">Search</label>
                            <input type="text" name="search" id="search" placeholder="Search spas...">
                        </div>

                        <div class="spa-filter-field spa-submit-field">
                            <button type="submit" class="spa-filter-button">
                                <i class="fas fa-filter"></i> Filter Results
                            </button>
                            <button type="button" class="spa-clear-filter-button">
                                <i class="fas fa-times"></i> Clear Filters
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Right Column - Spa Results -->
            <div class="spa-content-area">
                <div class="spa-loading">
                    <i class="fas fa-spinner fa-spin"></i> Loading...
                </div>

                <div class="spa-results">
                    <?php if ( have_posts() ) : ?>
                        <div class="spa-grid">
                            <?php
                            while ( have_posts() ) :
                                the_post();
                                get_template_part( 'template-parts/content', 'spa-card' );
                            endwhile;
                            ?>
                        </div>
                    <?php else : ?>
                        <div class="no-results">
                            <p><?php esc_html_e( 'No spas found matching your criteria.', 'spasinbarcelona' ); ?></p>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="spa-pagination">
                    <?php
                    $big = 999999999;
                    echo paginate_links( array(
                        'base' => str_replace( $big, '%#%', esc_url( get_pagenum_link( $big ) ) ),
                        'format' => '?paged=%#%',
                        'current' => max( 1, get_query_var( 'paged' ) ),
                        'total' => $wp_query->max_num_pages,
                        'prev_text' => '<i class="fas fa-chevron-left"></i> Previous',
                        'next_text' => 'Next <i class="fas fa-chevron-right"></i>',
                    ) );
                    ?>
                </div>

                <!-- Navigation Buttons -->
                <div class="spa-navigation-buttons">
                    <a href="<?php echo esc_url( get_post_type_archive_link( 'spa' ) ); ?>" class="spa-button">
                        View All Spas <i class="fas fa-arrow-right"></i>
                    </a>
                    <a href="<?php echo esc_url( home_url() ); ?>" class="spa-button">
                        <i class="fas fa-home"></i> Back To Homepage
                    </a>
                </div>
            </div>
        </div>
    </main>
</div>

<?php
get_footer();
