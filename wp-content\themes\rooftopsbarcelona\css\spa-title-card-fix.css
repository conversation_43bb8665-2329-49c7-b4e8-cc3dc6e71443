/**
 * Custom CSS for page title cards
 */

/* Change page header background to white for all page types */
.spa-single .page-header,
.spa-directory .page-header,
.post-type-archive-spa .page-header,
.tax-spa_category .page-header,
.tax-spa_service .page-header,
.tax-spa_feature .page-header,
.tax-spa_neighborhood .page-header,
.tag .page-header,
.page .page-header {
    background-color: #ffffff !important;
    border: 1px solid rgba(210, 180, 140, 0.15);
}

/* Style the spa tags (badges) to match the site design */
.spa-single .spa-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 15px;
    justify-content: center;
}

.spa-single .spa-tag {
    /* Match the styling of the 'View All Spas' button */
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 8px 16px;
    background-color: var(--primary-color); /* Teal color from the main button */
    color: var(--light-text-color);
    border-radius: 30px;
    font-weight: 500;
    font-family: var(--font-primary);
    font-size: 0.9rem;
    letter-spacing: 0.03em;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
    border: none;
    margin: 3px;
    text-transform: none;
    transition: all 0.3s ease; /* Add smooth transition for hover effects */
}

/* Add hover effects to match the 'View All Spas' button */
.spa-single .spa-tag:hover {
    background-color: var(--secondary-color) !important; /* Change to secondary color (#d2b48c) on hover */
    transform: translateY(-3px) !important;
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.08) !important;
}
