<?php
/**
 * Template Name: Popular Features Page
 *
 * The template for displaying all popular spa features
 */

get_header();
?>

<div id="primary" class="content-area">
    <main id="main" class="site-main">
        <header class="page-header">
            <div class="page-title-wrapper">
                <h1 class="page-title"><?php the_title(); ?></h1>
            </div>
            <div class="archive-description">
                <?php the_content(); ?>
            </div>
        </header>

        <div class="spa-tags-container">
            <?php
            // Get all popular features used by spas
            global $wpdb;

            // Get popular features that are actually used by spa posts
            $popular_features = $wpdb->get_results(
                "SELECT DISTINCT t.term_id, t.name, t.slug, COUNT(tr.object_id) as count
                FROM {$wpdb->terms} t
                INNER JOIN {$wpdb->term_taxonomy} tt ON t.term_id = tt.term_id
                INNER JOIN {$wpdb->term_relationships} tr ON tr.term_taxonomy_id = tt.term_taxonomy_id
                INNER JOIN {$wpdb->posts} p ON p.ID = tr.object_id
                WHERE tt.taxonomy = 'post_tag'
                AND p.post_type = 'spa'
                AND p.post_status = 'publish'
                GROUP BY t.term_id
                ORDER BY t.name ASC"
            );

            $tags = $popular_features;

            if (!empty($tags) && !is_wp_error($tags)) :
            ?>
                <div class="spa-tags-cloud">
                    <?php foreach ($tags as $tag) : ?>
                        <a href="<?php echo esc_url(get_tag_link($tag->term_id)); ?>" class="spa-tag-link">
                            <?php echo esc_html($tag->name); ?>
                            <span class="spa-tag-count">(<?php echo esc_html($tag->count); ?>)</span>
                        </a>
                    <?php endforeach; ?>
                </div>
            <?php else : ?>
                <p><?php esc_html_e('No popular features found.', 'spasinbarcelona'); ?></p>
            <?php endif; ?>
        </div>
    </main>
</div>

<style>
    .spa-tags-container {
        padding: 30px 0;
    }

    .spa-tags-cloud {
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
        margin-top: 20px;
    }

    .spa-tag-link {
        display: inline-block;
        padding: 8px 16px;
        background-color: var(--light-color);
        color: var(--dark-color);
        border-radius: 30px;
        text-decoration: none;
        font-size: 0.95rem;
        transition: all 0.3s ease;
        border: 1px solid rgba(210, 180, 140, 0.2);
    }

    .spa-tag-link:hover {
        background-color: var(--primary-color);
        color: #fff;
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(104, 160, 160, 0.2);
    }

    .spa-tag-count {
        font-size: 0.85rem;
        opacity: 0.7;
        margin-left: 5px;
    }
</style>

<?php
get_footer();
