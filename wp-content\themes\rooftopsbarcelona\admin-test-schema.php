<?php
/**
 * Admin page for testing schema
 */

// Add admin menu
function spasinbarcelona_add_schema_test_page() {
    add_submenu_page(
        'edit.php?post_type=spa',
        'Test Schema',
        'Test Schema',
        'manage_options',
        'test-schema',
        'spasinbarcelona_schema_test_page'
    );
}
add_action('admin_menu', 'spasinbarcelona_add_schema_test_page');

// Render the admin page
function spasinbarcelona_schema_test_page() {
    // Get all spas
    $args = array(
        'post_type' => 'spa',
        'posts_per_page' => -1,
        'orderby' => 'title',
        'order' => 'ASC'
    );
    
    $spas = get_posts($args);
    
    // Get the selected spa ID
    $spa_id = isset($_GET['spa_id']) ? intval($_GET['spa_id']) : 0;
    
    ?>
    <div class="wrap">
        <h1>Test Schema</h1>
        
        <form method="get">
            <input type="hidden" name="post_type" value="spa">
            <input type="hidden" name="page" value="test-schema">
            
            <select name="spa_id">
                <option value="">Select a spa</option>
                <?php foreach ($spas as $spa) : ?>
                    <option value="<?php echo $spa->ID; ?>" <?php selected($spa_id, $spa->ID); ?>>
                        <?php echo esc_html($spa->post_title); ?>
                    </option>
                <?php endforeach; ?>
            </select>
            
            <input type="submit" class="button" value="Test Schema">
        </form>
        
        <?php if ($spa_id) : ?>
            <h2>Schema for <?php echo get_the_title($spa_id); ?></h2>
            
            <?php
            // Get the schema
            $schema_html = spasinbarcelona_get_spa_schema($spa_id);
            
            // Extract JSON from the HTML
            preg_match_all('/<script type="application\/ld\+json">(.*?)<\/script>/s', $schema_html, $matches);
            
            if (empty($matches[1])) {
                echo '<p>No schema found.</p>';
            } else {
                // Process each schema object
                foreach ($matches[1] as $index => $json) {
                    $schema = json_decode($json, true);
                    if (!$schema) {
                        echo '<p>Error: Invalid JSON in schema #' . ($index + 1) . '</p>';
                        continue;
                    }
                    
                    $schema_type = isset($schema['@type']) ? 
                        (is_array($schema['@type']) ? implode(', ', $schema['@type']) : $schema['@type']) : 
                        (isset($schema['@graph']) ? '@graph structure' : 'Unknown');
                    
                    echo '<h3>Schema #' . ($index + 1) . ' Type: ' . $schema_type . '</h3>';
                    
                    // If this is the main schema with @graph, check the offers
                    if ($index === 0 && isset($schema['@graph'])) {
                        // Find the business entity
                        $business_entity = null;
                        foreach ($schema['@graph'] as $entity) {
                            if (isset($entity['@type']) && 
                                ((is_array($entity['@type']) && in_array('HealthAndBeautyBusiness', $entity['@type'])) ||
                                 $entity['@type'] === 'HealthAndBeautyBusiness')) {
                                $business_entity = $entity;
                                break;
                            }
                        }
                        
                        if ($business_entity) {
                            // Check for makesOffer property
                            if (isset($business_entity['makesOffer']) && is_array($business_entity['makesOffer'])) {
                                echo '<h4>Found ' . count($business_entity['makesOffer']) . ' offers:</h4>';
                                
                                echo '<table class="widefat">';
                                echo '<thead>';
                                echo '<tr>';
                                echo '<th>Name</th>';
                                echo '<th>Price</th>';
                                echo '<th>Currency</th>';
                                echo '<th>Valid Through</th>';
                                echo '<th>Availability</th>';
                                echo '<th>Provider</th>';
                                echo '<th>Seller</th>';
                                echo '</tr>';
                                echo '</thead>';
                                echo '<tbody>';
                                
                                foreach ($business_entity['makesOffer'] as $offer) {
                                    echo '<tr>';
                                    
                                    // Name
                                    echo '<td>' . esc_html($offer['name'] ?? 'N/A') . '</td>';
                                    
                                    // Price
                                    echo '<td>';
                                    if (isset($offer['priceSpecification']) && isset($offer['priceSpecification']['price'])) {
                                        echo esc_html($offer['priceSpecification']['price']);
                                    } else {
                                        echo 'N/A';
                                    }
                                    echo '</td>';
                                    
                                    // Currency
                                    echo '<td>';
                                    if (isset($offer['priceSpecification']) && isset($offer['priceSpecification']['priceCurrency'])) {
                                        echo esc_html($offer['priceSpecification']['priceCurrency']);
                                    } else {
                                        echo 'N/A';
                                    }
                                    echo '</td>';
                                    
                                    // Valid Through
                                    echo '<td>';
                                    if (isset($offer['priceSpecification']) && isset($offer['priceSpecification']['validThrough'])) {
                                        echo esc_html($offer['priceSpecification']['validThrough']);
                                    } else {
                                        echo 'N/A';
                                    }
                                    echo '</td>';
                                    
                                    // Availability
                                    echo '<td>' . esc_html($offer['availability'] ?? 'N/A') . '</td>';
                                    
                                    // Provider
                                    echo '<td>';
                                    if (isset($offer['itemOffered']) && isset($offer['itemOffered']['provider'])) {
                                        if (isset($offer['itemOffered']['provider']['@id'])) {
                                            echo esc_html($offer['itemOffered']['provider']['@id']);
                                        } else {
                                            echo 'Invalid provider reference';
                                        }
                                    } else {
                                        echo 'No Provider';
                                    }
                                    echo '</td>';
                                    
                                    // Seller
                                    echo '<td>';
                                    if (isset($offer['seller'])) {
                                        if (isset($offer['seller']['@id'])) {
                                            echo esc_html($offer['seller']['@id']);
                                        } else {
                                            echo 'Invalid seller reference';
                                        }
                                    } else {
                                        echo 'No Seller';
                                    }
                                    echo '</td>';
                                    
                                    echo '</tr>';
                                }
                                
                                echo '</tbody>';
                                echo '</table>';
                            } else {
                                echo '<p>No offers found in the business entity.</p>';
                            }
                        } else {
                            echo '<p>No business entity found in the schema.</p>';
                        }
                    }
                    
                    // Display the raw JSON
                    echo '<h4>Raw JSON:</h4>';
                    echo '<pre style="max-height: 300px; overflow: auto;">' . esc_html(json_encode($schema, JSON_PRETTY_PRINT)) . '</pre>';
                }
            }
            ?>
        <?php endif; ?>
    </div>
    <?php
}
