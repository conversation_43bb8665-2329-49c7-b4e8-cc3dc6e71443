<?php
/**
 * The template for displaying spa-popular tag archives
 * This template is used for the /spa-popular/ URL structure
 */

get_header();
?>

<div id="primary" class="content-area">
    <main id="main" class="site-main">
        <header class="page-header">
            <div class="page-title-wrapper">
                <?php
                // Get the current tag from the URL
                $current_url = $_SERVER['REQUEST_URI'];
                $url_parts = explode('/', trim($current_url, '/'));
                $tag_slug = end($url_parts);

                // Get the tag object
                $tag = get_term_by('slug', $tag_slug, 'post_tag');

                if ($tag) {
                    echo '<h1 class="page-title">Spas in Barcelona tagged with ' . esc_html($tag->name) . '</h1>';
                } else {
                    echo '<h1 class="page-title">' . wp_kses_post(get_the_archive_title()) . '</h1>';
                }
                ?>
            </div>
            <div class="archive-description">
                <?php
                if ($tag && !empty($tag->description)) {
                    echo wp_kses_post($tag->description);
                } else if ($tag) {
                    echo '<p>Discover the best spas in Barcelona with this most-searched feature: ' . esc_html($tag->name) . '. Browse our selection of spas.</p>';
                } else {
                    echo wp_kses_post(get_the_archive_description());
                }
                ?>
            </div>
        </header>

        <div class="spa-results" style="width: 100%;">
            <?php
            // Get the term slug from the query var
            $tag_slug = get_query_var('spa_tag_term_slug');
            if (empty($tag_slug)) {
                // Fallback to URL parsing if query var is not set
                $current_url = $_SERVER['REQUEST_URI'];
                $url_parts = explode('/', trim($current_url, '/'));
                $tag_slug = end($url_parts);
            }

            // Get the query args from the query var or create new ones
            $args = get_query_var('spa_tag_query_args');
            if (empty($args)) {
                // Fallback to creating our own args
                $args = array(
                    'post_type' => 'spa',
                    'posts_per_page' => -1,
                    'tag' => $tag_slug,
                );
            }

            // Debug output removed

            $spa_query = new WP_Query($args);

            if ($spa_query->have_posts()) :
            ?>
                <div class="spa-grid">
                    <?php
                    while ($spa_query->have_posts()) :
                        $spa_query->the_post();
                        get_template_part('template-parts/content', 'spa-card');
                    endwhile;
                    wp_reset_postdata();
                    ?>
                </div>
            <?php else : ?>
                <div class="no-results">
                    <p><?php esc_html_e('No spas found matching your criteria.', 'spasinbarcelona'); ?></p>
                </div>
            <?php endif; ?>
        </div>

        <?php if ($spa_query->max_num_pages > 1) : ?>
        <div class="spa-pagination">
            <?php
            $big = 999999999;
            echo paginate_links( array(
                'base' => str_replace( $big, '%#%', esc_url( get_pagenum_link( $big ) ) ),
                'format' => '?paged=%#%',
                'current' => max( 1, get_query_var( 'paged' ) ),
                'total' => $spa_query->max_num_pages,
                'prev_text' => '<i class="fas fa-chevron-left"></i> Previous',
                'next_text' => 'Next <i class="fas fa-chevron-right"></i>',
            ) );
            ?>
        </div>
        <?php endif; ?>
    </main>
</div>

<?php
get_footer();
