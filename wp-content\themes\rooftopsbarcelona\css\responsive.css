/**
 * Responsive styles for Spas in Barcelona theme
 */

/* Large Devices (Desktops) */
@media (max-width: 1200px) {
    .spa-container:not(.site-header .spa-container) {
        max-width: 960px;
    }

    .spa-section-title {
        font-size: 2.2rem;
    }

    .spa-hero-title {
        font-size: 2.8rem;
    }

    .spa-cta-title {
        font-size: 2.2rem;
    }
}

/* Medium Devices (Tablets) */
@media (max-width: 992px) {
    .spa-container:not(.site-header .spa-container) {
        max-width: 720px;
    }

    .spa-section-title {
        font-size: 2rem;
    }

    .spa-hero-title {
        font-size: 2.5rem;
    }

    .spa-hero {
        padding: 80px 0;
    }

    .spa-featured,
    .spa-categories,
    .spa-services,
    .spa-features {
        padding: 60px 0;
    }

    .spa-cta {
        padding: 80px 0;
    }

    .spa-cta-title {
        font-size: 2rem;
    }

    .spa-content-wrapper {
        flex-direction: column;
    }

    .spa-sidebar {
        width: 100%;
    }

    .footer-widgets {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Small Devices (Landscape Phones) */
@media (max-width: 768px) {
    .spa-container:not(.site-header .spa-container) {
        max-width: 540px;
    }

    .spa-section-title {
        font-size: 1.8rem;
    }

    .spa-hero-title {
        font-size: 2.2rem;
    }

    .spa-hero {
        padding: 60px 0;
    }

    .spa-hero-subtitle {
        font-size: 1.1rem;
    }

    .spa-featured,
    .spa-categories,
    .spa-services,
    .spa-features {
        padding: 50px 0;
    }

    .spa-cta {
        padding: 60px 0;
    }

    .spa-cta-title {
        font-size: 1.8rem;
    }

    .spa-cta-text {
        font-size: 1rem;
        margin-bottom: 25px;
    }

    .spa-cta-button {
        padding: 15px 30px;
    }

    .spa-tabs-nav {
        overflow-x: auto;
    }

    .spa-tabs-nav a {
        padding: 12px 15px;
        font-size: 0.9rem;
    }

    .spa-tabs-content {
        padding: 20px;
    }

    .spa-tab-content h2 {
        font-size: 1.6rem;
    }

    .spa-tab-content h3 {
        font-size: 1.3rem;
    }

    .spa-services-amenities {
        flex-direction: column;
    }

    .spa-packages,
    .spa-review-sources-grid,
    .spa-featured-reviews-grid,
    .spa-info-grid {
        grid-template-columns: 1fr;
    }

    .footer-bottom {
        flex-direction: column;
        text-align: center;
    }

    .footer-social {
        margin-top: 15px;
        justify-content: center;
    }
}

/* Extra Small Devices (Portrait Phones) */
@media (max-width: 576px) {
    .spa-section-title {
        font-size: 1.6rem;
    }

    .spa-hero-title {
        font-size: 1.8rem;
    }

    .spa-hero {
        padding: 50px 0;
    }

    .spa-hero-subtitle {
        font-size: 1rem;
    }

    .spa-hero-search input {
        padding: 12px 15px;
    }

    .spa-hero-search button {
        width: 40px;
        height: 40px;
    }

    .spa-hero-categories {
        flex-direction: column;
        gap: 10px;
    }

    .spa-featured,
    .spa-categories,
    .spa-services,
    .spa-features {
        padding: 40px 0;
    }

    .spa-cta {
        padding: 50px 0;
    }

    .spa-cta-title {
        font-size: 1.6rem;
        margin-bottom: 15px;
    }

    .spa-cta-text {
        font-size: 0.95rem;
        margin-bottom: 20px;
    }

    .spa-cta-button {
        padding: 12px 25px;
        font-size: 0.9rem;
    }

    .spa-categories-grid,
    .spa-services-grid {
        grid-template-columns: 1fr;
    }

    .spa-title {
        font-size: 1.8rem;
    }

    .spa-gallery-grid {
        grid-template-columns: 1fr;
    }

    .spa-gallery-item {
        height: 200px;
    }

    .footer-widgets {
        grid-template-columns: 1fr;
    }
}
