<?php
/**
 * A script to flush rewrite rules
 */

// Load WordPress
require_once( dirname( __FILE__ ) . '/../../wp-load.php' );

// Delete any transients
delete_transient('spasinbarcelona_flushed_rewrites');

// Flush rewrite rules
flush_rewrite_rules(true);

// Output success message
echo "Rewrite rules flushed successfully!";

// Debug output
global $wp_rewrite;
echo "<h2>Current Rewrite Rules</h2>";
echo "<pre>";
print_r($wp_rewrite->rules);
echo "</pre>";

// Debug query vars
echo "<h2>Registered Query Vars</h2>";
echo "<pre>";
print_r($wp_rewrite->query_vars);
echo "</pre>";
