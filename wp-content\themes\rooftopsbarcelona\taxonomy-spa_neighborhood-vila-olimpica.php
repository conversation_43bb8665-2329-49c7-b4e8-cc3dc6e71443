<?php
/**
 * The template for displaying Vila Olímpica neighborhood archive
 * 
 * This is a specific template for the Vila Olímpica neighborhood
 */

get_header();

// Get the Vila Olímpica term
$vila_olimpica = get_term_by('slug', 'vila-olimpica', 'spa_neighborhood');

// If term doesn't exist, create it
if (!$vila_olimpica) {
    $result = wp_insert_term(
        'Vila Olímpica',
        'spa_neighborhood',
        array(
            'description' => 'Discover the best spas in the Vila Olímpica neighborhood of Barcelona. This modern area near the beach offers luxury spa experiences with sea views.',
            'slug' => 'vila-olimpica'
        )
    );
    
    if (!is_wp_error($result)) {
        $vila_olimpica = get_term($result['term_id'], 'spa_neighborhood');
    }
}

// Get the Mandarin Oriental spa
$mandarin = get_page_by_path('the-spa-at-mandarin-oriental-barcelona', OBJECT, 'spa');

// If we don't have the Mandarin spa, try to find any spa
if (!$mandarin) {
    $spas = get_posts(array(
        'post_type' => 'spa',
        'posts_per_page' => 1,
    ));
    
    if (!empty($spas)) {
        $mandarin = $spas[0];
    }
}

// If we have both the term and the spa, make sure they're connected
if ($vila_olimpica && $mandarin) {
    wp_set_object_terms($mandarin->ID, $vila_olimpica->term_id, 'spa_neighborhood', true);
    
    // Update the spa's location meta
    $location = get_post_meta($mandarin->ID, 'location', true);
    
    if (empty($location) || !is_array($location)) {
        $location = array();
    }
    
    $location['neighborhood'] = 'Vila Olímpica';
    $location['district'] = 'Sant Martí';
    
    update_post_meta($mandarin->ID, 'location', $location);
    
    // Clear caches
    clean_object_term_cache($mandarin->ID, 'spa');
    clean_term_cache($vila_olimpica->term_id, 'spa_neighborhood');
}

// Debug info for administrators
if (current_user_can('administrator')) {
    echo '<!-- Vila Olímpica Special Template -->';
    if ($vila_olimpica) {
        echo '<!-- Term ID: ' . $vila_olimpica->term_id . ' | Name: ' . $vila_olimpica->name . ' | Slug: ' . $vila_olimpica->slug . ' -->';
    }
    if ($mandarin) {
        echo '<!-- Mandarin Spa ID: ' . $mandarin->ID . ' | Title: ' . $mandarin->post_title . ' -->';
    }
}
?>

<div id="primary" class="content-area">
    <main id="main" class="site-main">
        <header class="page-header">
            <div class="page-title-wrapper">
                <h1 class="page-title">Spas in Vila Olímpica, Barcelona</h1>
            </div>
            <div class="archive-description">
                <p>Discover the best spas in the Vila Olímpica neighborhood of Barcelona. This modern area near the beach offers luxury spa experiences with sea views.</p>
            </div>
        </header>

        <div class="spa-results" style="width: 100%;">
            <?php
            // Get spas in Vila Olímpica
            $spas = array();
            
            if ($vila_olimpica) {
                $args = array(
                    'post_type' => 'spa',
                    'posts_per_page' => -1,
                    'tax_query' => array(
                        array(
                            'taxonomy' => 'spa_neighborhood',
                            'field' => 'term_id',
                            'terms' => $vila_olimpica->term_id,
                        ),
                    ),
                );
                
                $spa_query = new WP_Query($args);
                
                if ($spa_query->have_posts()) {
                    $spas = $spa_query->posts;
                }
            }
            
            // If no spas found but we have the Mandarin spa, add it manually
            if (empty($spas) && $mandarin) {
                $spas = array($mandarin);
            }
            
            if (!empty($spas)) :
            ?>
                <div class="spa-grid">
                    <?php
                    foreach ($spas as $post) :
                        setup_postdata($post);
                        get_template_part('template-parts/content', 'spa-card');
                    endforeach;
                    wp_reset_postdata();
                    ?>
                </div>
            <?php else : ?>
                <div class="no-results">
                    <p><?php esc_html_e('No spas found in this neighborhood.', 'spasinbarcelona'); ?></p>
                </div>
            <?php endif; ?>
        </div>
    </main>
</div>

<?php
get_footer();
?>
