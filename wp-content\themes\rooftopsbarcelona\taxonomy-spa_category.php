<?php
/**
 * The template for displaying spa category archives
 */

get_header();
?>

<div id="primary" class="content-area">
    <main id="main" class="site-main">
        <header class="page-header">
            <div class="page-title-wrapper">
                <h1 class="page-title"><?php echo wp_kses_post( get_the_archive_title() ); ?></h1>
            </div>
            <div class="archive-description">
                <?php echo wp_kses_post( get_the_archive_description() ); ?>
            </div>
        </header>



        <div class="spa-results" style="width: 100%;">
            <?php if ( have_posts() ) : ?>
                <div class="spa-grid">
                    <?php
                    while ( have_posts() ) :
                        the_post();
                        get_template_part( 'template-parts/content', 'spa-card' );
                    endwhile;
                    ?>
                </div>
            <?php else : ?>
                <div class="no-results">
                    <p><?php esc_html_e( 'No spas found matching your criteria.', 'spasinbarcelona' ); ?></p>
                </div>
            <?php endif; ?>
        </div>

        <div class="spa-pagination">
            <?php
            $big = 999999999;
            echo paginate_links( array(
                'base' => str_replace( $big, '%#%', esc_url( get_pagenum_link( $big ) ) ),
                'format' => '?paged=%#%',
                'current' => max( 1, get_query_var( 'paged' ) ),
                'total' => $wp_query->max_num_pages,
                'prev_text' => '<i class="fas fa-chevron-left"></i> Previous',
                'next_text' => 'Next <i class="fas fa-chevron-right"></i>',
            ) );
            ?>
        </div>

        <!-- Navigation Buttons -->
        <div class="spa-navigation-buttons">
            <a href="<?php echo esc_url( get_post_type_archive_link( 'spa' ) ); ?>" class="spa-button">
                View All Spas <i class="fas fa-arrow-right"></i>
            </a>
            <a href="<?php echo esc_url( home_url() ); ?>" class="spa-button">
                <i class="fas fa-home"></i> Back To Homepage
            </a>
        </div>
    </main>
</div>

<?php
get_footer();
