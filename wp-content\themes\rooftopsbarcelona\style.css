/*
Theme Name: Rooftops in Barcelona
Theme URI: https://rooftopsbarcelona.local
Description: A comprehensive WordPress directory website for rooftops in Barcelona
Author: Rooftops in Barcelona
Author URI: https://rooftopsbarcelona.local
Template: generatepress
Version: 1.0.0
Text Domain: rooftopsbarcelona
*/

/* Add your custom styles below this line */
html {
    height: 100%;
}
html, body, #page, .site {
    margin-bottom: 0 !important;
    padding-bottom: 0 !important;
}
/* Ensure footer has no bottom margin/padding */
.site-footer {
    margin-bottom: 0 !important;
    padding-bottom: 0 !important;
    flex-shrink: 0; /* Prevent footer from shrinking */
    margin-top: auto !important; /* Push footer to the bottom of its flex container */
}

/* Override GeneratePress white background */
.separate-containers .inside-article,
.separate-containers .comments-area,
.separate-containers .page-header,
.separate-containers .paging-navigation,
.one-container .site-content,
.inside-article,
.site-content,
#page,
.site,
.content-area,
#primary,
.separate-containers .site-main,
.separate-containers .inside-article > *,
body {
    background-color: #f5f5f0 !important;
}

/* CTA section styling */
.spa-cta-wrapper,
.cta-section {
    margin-top: 40px;
    margin-bottom: 0;
    position: relative;
    z-index: 10;
}

/* Basic styling to ensure proper formatting */
body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.8;
    color: #3a4a4a;
    letter-spacing: 0.01em;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}
.site {
    display: flex;
    flex-direction: column;
    flex-grow: 1; /* Make .site fill its parent flex container */
}
#page {
    display: flex;
    flex-direction: column;
    flex-grow: 1; /* Make #page fill the body's flex container */
}

h1, h2, h3, h4, h5, h6 {
    font-family: 'Playfair Display', serif;
    margin-bottom: 20px;
    color: #3a4a4a;
    letter-spacing: 0.02em;
    font-weight: 600;
    line-height: 1.3;
}

.site-content {
    padding: 50px 0;
    flex-grow: 1;
}
#primary,
.site-main {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
}

/* Fix for GeneratePress container */
.container, .spa-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Ensure consistent layout for main section content containers */
.spa-section-content > .spa-card-container {
    max-width: 1200px; /* Consistent max width, matching .spa-container */
    margin-left: auto;
    margin-right: auto;
    padding: 25px; /* Consistent padding on all sides */
    margin-bottom: 40px; /* Space below each section container */
    /* No background-color, border, or box-shadow is set here,
       so the container itself will be transparent, allowing the
       page background to show through its padding. This matches
       the appearance of the "Packages & Treatments" section container
       in the provided image. Individual items inside (like specific
       packages or testimonials) will retain their own card styling. */
}

/* Ensure images display properly */
.wp-post-image {
    margin-bottom: 25px;
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

/* Basic button styling */
button, input[type="button"], input[type="reset"], input[type="submit"], .button {
    background-color: #68a0a0;
    color: #ffffff;
    padding: 12px 24px;
    border: none;
    border-radius: 30px;
    cursor: pointer;
    transition: all 0.4s ease;
    letter-spacing: 0.02em;
    font-weight: 500;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
}

button:hover, input[type="button"]:hover, input[type="reset"]:hover, input[type="submit"]:hover, .button:hover {
    background-color: #d2b48c;
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.08);
}

/* Fix for category links */
.site-main a {
    color: #68a0a0;
    text-decoration: none;
    transition: all 0.4s ease;
}

.site-main a:hover {
    color: #d2b48c;
}

/* Fix for spa cards */
.site-main article {
    margin-bottom: 35px;
    padding: 25px;
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.4s ease;
    border: 1px solid rgba(210, 180, 140, 0.2);
}

.site-main article:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(104, 160, 160, 0.15);
    border-color: #68a0a0;
}

/* Override hover effects for single spa page */
.single-spa-page .site-main article {
    transition: none;
    box-shadow: none;
    border: none;
    padding: 0;
}

.single-spa-page .site-main article:hover {
    transform: none;
    box-shadow: none;
    border-color: transparent;
}

.site-main article img {
    width: 100%;
    height: auto;
    border-radius: 4px;
    margin-bottom: 15px;
}

.site-main article h3 {
    font-size: 1.5rem;
    margin-bottom: 10px;
}

.site-main article p {
    margin-bottom: 15px;
    color: #666;
}

/* Fix for ratings */
.star-rating {
    color: #d2b48c;
    margin-bottom: 12px;
}

/* Fix for featured spas section */
.featured-spas, #featured-spas {
    margin: 40px 0;
}

.featured-spas h2, #featured-spas h2 {
    font-size: 2rem;
    margin-bottom: 20px;
    text-align: center;
    color: #2c3e50;
}

/* Fix for spa categories in archive pages */
.archive .spa-categories {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 30px;
}

.archive .spa-category {
    background-color: #4a90e2;
    color: #fff;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 0.9rem;
}

/* Fix for specific elements in the screenshot */
.site-main .entry-content > ul {
    list-style: none;
    padding: 0;
    margin: 0 0 20px 0;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.site-main .entry-content > ul li {
    margin-bottom: 10px;
}

.site-main .entry-content > ul li a {
    display: inline-block;
    padding: 8px 15px;
    background-color: #f8f9fa;
    border: 1px solid #e1e1e1;
    border-radius: 4px;
    color: #333;
    text-decoration: none;
    transition: all 0.3s ease;
}

.site-main .entry-content > ul li a:hover {
    background-color: #4a90e2;
    color: #fff;
    border-color: #4a90e2;
}

/* Fix for search box */
input[type="search"] {
    padding: 10px 15px;
    border: 1px solid #e1e1e1;
    border-radius: 4px;
    width: 100%;
    max-width: 300px;
}

/* Fix for 1850 Urban Spa Barcelona card */
.spa-card h3 {
    font-size: 1.5rem;
    margin-bottom: 10px;
}

.spa-card .location {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    color: #666;
}

.spa-card .rating {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.spa-card .reviews {
    font-size: 0.9rem;
    color: #666;
    margin-left: 5px;
}

/* .spa-card .services rule removed as it was conflicting */

.spa-card .description {
    margin-bottom: 15px;
    font-size: 0.9rem;
    color: #666;
}

/* Enhanced Header Styling */
.site-header {
    padding: 20px 0;
    background-color: #f5f5f0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    position: relative;
    z-index: 100;
    transition: all 0.4s ease;
    border-bottom: 1px solid rgba(210, 180, 140, 0.2);
}

/* Sticky Header */
.sticky-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    padding: 10px 0;
    background-color: rgba(245, 245, 240, 0.95);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    animation: slideDown 0.5s ease;
    border-bottom: 1px solid rgba(210, 180, 140, 0.2);
}

/* Responsive styles for header */
@media (max-width: 768px) {
    .site-header .spa-container {
        flex-direction: column;
    }

    .site-branding {
        margin-bottom: 15px;
        text-align: center;
    }

    .main-navigation {
        margin-left: 0;
        text-align: center;
    }

    .main-navigation ul {
        justify-content: center;
    }
}

@keyframes slideDown {
    from {
        transform: translateY(-100%);
    }
    to {
        transform: translateY(0);
    }
}

.inside-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 40px;
}

/* Logo Styling */
.site-logo {
    max-width: 250px;
}

.site-branding {
    display: flex;
    align-items: center;
}

.site-branding a {
    text-decoration: none;
}

.main-title {
    font-family: 'Playfair Display', serif;
    font-size: 28px;
    font-weight: 600;
    margin: 0;
    color: #68a0a0;
    transition: all 0.4s ease;
    letter-spacing: 0.02em;
}

.main-title:hover {
    color: #d2b48c;
}

.site-description {
    font-size: 14px;
    margin: 5px 0 0;
    color: #666;
}

/* Navigation Styling */
.main-navigation {
    background-color: transparent;
    margin-left: auto; /* This pushes the navigation to the right */
    text-align: right;
}

.main-navigation ul {
    display: flex;
    justify-content: flex-end; /* Align menu items to the right */
}

.main-navigation li {
    margin: 0 5px;
}

.main-navigation a {
    color: #333;
    font-weight: 500;
    padding: 10px 15px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.main-navigation a:hover,
.main-navigation .current-menu-item > a {
    color: #68a0a0;
    background-color: rgba(104, 160, 160, 0.1);
}

/* Fix for the specific spa listing */
.spa-listing {
    margin-bottom: 30px;
}

.spa-listing h3 {
    font-size: 1.5rem;
    margin-bottom: 10px;
}

.spa-listing img {
    width: 100%;
    height: auto;
    border-radius: 8px;
    margin-bottom: 15px;
}

.spa-listing .rating {
    color: #f5a623;
    margin-bottom: 10px;
}

.spa-listing .location {
    color: #666;
    margin-bottom: 10px;
}

.spa-listing .services {
    color: #666;
    margin-bottom: 15px;
}

.spa-listing .description {
    color: #666;
    margin-bottom: 15px;
}

.homepage {
    color: #4a90e2;
    text-decoration: none;
}

.homepage:hover {
    color: #2c3e50;
}

/* Neighborhood Cards with Images */
.spa-neighborhoods-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    margin: 0 auto 30px;
    max-width: 1000px;
}

.spa-neighborhood-card-wrapper {
    position: relative;
    overflow: hidden;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.spa-neighborhood-card-wrapper:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0,0,0,0.15);
}

.spa-neighborhood-card {
    display: block;
    background-color: #fff;
    color: #333;
    text-decoration: none;
    padding: 20px;
    height: 100%;
    position: relative;
    overflow: hidden;
    border-radius: 12px;
}

/* Removed spa-neighborhood-image styles as we're now using the same style as spa-feature-card */

/* Removed spa-neighborhood styles as we're now using the same style as spa-feature-card */

/* Removed spa-neighborhoods-grid media queries as we're now using spa-features-grid */

/* Fix for the search form in the screenshot */
.search-form,
form[role="search"] {
    display: flex;
    margin-bottom: 20px;
    max-width: 500px;
}

.search-form input[type="search"],
form[role="search"] input[type="search"] {
    flex-grow: 1;
    padding: 10px 15px;
    border: 1px solid #e1e1e1;
    border-radius: 4px 0 0 4px;
    max-width: none;
}

.search-form button,
form[role="search"] button {
    padding: 10px 15px;
    background-color: #4a90e2;
    color: #fff;
    border: none;
    border-radius: 0 4px 4px 0;
    cursor: pointer;
}

/* Fix for the category links in the screenshot */
.category-links {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 30px;
}

.category-link {
    display: inline-block;
    padding: 8px 15px;
    background-color: #f8f9fa;
    border: 1px solid #e1e1e1;
    border-radius: 4px;
    color: #333;
    text-decoration: none;
    transition: all 0.3s ease;
}

.category-link:hover {
    background-color: #4a90e2;
    color: #fff;
    border-color: #4a90e2;
}

/* Hero Section Styling */
.hero-section {
    background-image: linear-gradient(rgba(58, 74, 74, 0.6), rgba(58, 74, 74, 0.7)), url('../images/barcelona-aerial-view.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    color: #fff;
    padding: 120px 0 100px;
    text-align: center;
    position: relative;
    margin-bottom: 70px;
    overflow: hidden;
}

/* Hero Section Overlay */
.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(104, 160, 160, 0.3) 0%, rgba(210, 180, 140, 0.3) 100%);
    z-index: 1;
    background-image: url('data:image/svg+xml;utf8,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><path d="M30 5 L55 30 L30 55 L5 30 Z" fill="none" stroke="rgba(255, 255, 255, 0.05)" stroke-width="1"/></svg>');
}

.hero-content {
    position: relative;
    z-index: 2;
}

/* Animation Classes */
.animated {
    animation-duration: 1s;
    animation-fill-mode: both;
}

.fadeIn {
    animation-name: fadeIn;
}

.fadeInDown {
    animation-name: fadeInDown;
}

.fadeInUp {
    animation-name: fadeInUp;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translate3d(0, -30px, 0);
    }
    to {
        opacity: 1;
        transform: translate3d(0, 0, 0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translate3d(0, 30px, 0);
    }
    to {
        opacity: 1;
        transform: translate3d(0, 0, 0);
    }
}

.hero-content {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 20px;
}

.hero-title {
    font-size: 3.5rem;
    margin-bottom: 20px;
    color: #fff;
    font-family: 'Playfair Display', serif;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.3rem;
    margin-bottom: 30px;
    color: rgba(255, 255, 255, 0.9);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    line-height: 1.5;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
}

/* Enhanced Search Form */
.hero-search {
    max-width: 600px;
    margin: 0 auto 40px;
    position: relative;
}

.hero-search input[type="search"] {
    width: 100%;
    padding: 18px 25px;
    border-radius: 50px;
    border: none;
    font-size: 1.1rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    background-color: rgba(255, 255, 255, 0.95);
    transition: all 0.3s ease;
}

.hero-search input[type="search"]:focus {
    background-color: #fff;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    outline: none;
}

.hero-search button {
    position: absolute;
    right: 5px;
    top: 5px;
    background-color: #4a90e2;
    color: #fff;
    border: none;
    border-radius: 50px;
    width: 50px;
    height: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1.2rem;
}

.hero-search button:hover {
    background-color: #FFD700;
    transform: scale(1.05);
}

/* Category Links in Hero */
.hero-categories {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 12px;
    margin-bottom: 20px;
}

.hero-category {
    background-color: rgba(255, 255, 255, 0.2);
    color: #fff;
    padding: 10px 20px;
    border-radius: 50px;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
    text-decoration: none;
    display: inline-block;
}

.hero-category:hover {
    background-color: #4a90e2;
    color: #fff;
    transform: translateY(-3px);
}

.hero-category.view-all {
    background-color: #4a90e2;
}

.hero-category.view-all:hover {
    background-color: #FFD700;
}

/* Regular Content Styling */
.site-main > h1 {
    font-size: 2.5rem;
    margin-bottom: 20px;
    color: #2c3e50;
    font-family: 'Playfair Display', serif;
}

.site-main > p {
    margin-bottom: 20px;
    color: #666;
    font-size: 1rem;
    line-height: 1.6;
}

/* Regular Search Form */
.site-main > form {
    margin-bottom: 30px;
}

.site-main > form input[type="search"] {
    padding: 12px 15px;
    border: 1px solid #e1e1e1;
    border-radius: 4px;
    width: 100%;
    max-width: 400px;
    font-size: 1rem;
}

/* Regular Category Links */
.site-main > ul {
    list-style: none;
    padding: 0;
    margin: 0 0 30px 0;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

/* Fix for Explore by Category section */
.spa-categories,
.spa-services {
    padding: 80px 0;
    background-color: #f5f5f0; /* Corrected background */
}

.spa-section-header {
    text-align: center;
    margin-bottom: 40px;
}

.spa-section-title {
    font-size: 2.5rem;
    margin-bottom: 10px;
    color: #2c3e50;
}

.spa-section-subtitle {
    font-size: 1.1rem;
    color: #666;
    max-width: 700px;
    margin: 0 auto;
}

.spa-categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.spa-category-card {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 30px 20px;
    text-align: center;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #333333;
    text-decoration: none;
}

.spa-category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    color: #333333;
    text-decoration: none;
}

.spa-category-icon {
    width: 70px;
    height: 70px;
    background-color: #4a90e2;
    color: #fff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    margin-bottom: 20px;
    transition: all 0.3s ease;
}

.spa-category-card:hover .spa-category-icon {
    background-color: #f5a623;
    transform: scale(1.1);
}

.spa-category-title {
    font-size: 1.2rem;
    margin-bottom: 10px;
    color: #333333;
}

.spa-category-count {
    color: #666;
    font-size: 0.9rem;
}

.spa-section-footer {
    text-align: center;
    margin-top: 40px;
}

.spa-button {
    display: inline-block;
    padding: 12px 24px;
    background-color: #4a90e2;
    color: #ffffff;
    border-radius: 4px;
    font-weight: 600;
    text-align: center;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    text-decoration: none;
}

.spa-button:hover {
    background-color: #f5a623;
    color: #ffffff;
    transform: translateY(-2px);
    text-decoration: none;
}

.site-main > ul > li {
    margin-bottom: 10px;
}

.site-main > ul > li > a {
    display: inline-block;
    padding: 10px 18px;
    background-color: #f8f9fa;
    border: 1px solid #e1e1e1;
    border-radius: 30px;
    color: #333;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 0.95rem;
}

.site-main > ul > li > a:hover {
    background-color: #4a90e2;
    color: #fff;
    border-color: #4a90e2;
    transform: translateY(-2px);
}

/* Fix for the featured spas section in the screenshot */
.site-main > h2 {
    font-size: 2rem;
    margin: 40px 0 20px;
    color: #2c3e50;
    font-family: 'Playfair Display', serif;
}

.site-main > p + h2 {
    margin-top: 0;
}

/* Fix for the spa card in the screenshot */
.site-main > article {
    margin-bottom: 30px;
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.site-main > article:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.site-main > article h3 {
    font-size: 1.5rem;
    margin-bottom: 10px;
    font-family: 'Playfair Display', serif;
}

.site-main > article img {
    width: 100%;
    height: auto;
    border-radius: 4px;
    margin-bottom: 15px;
}

/* Fix for the view details link in the screenshot */
.site-main > article a {
    display: inline-block;
    padding: 8px 15px;
    background-color: #4a90e2;
    color: #fff !important;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
    margin-top: 10px;
}

.site-main > article a:hover {
    background-color: #2c3e50;
    color: #fff !important;
    text-decoration: none;
}

/* Additional fixes for specific elements in the screenshot */
#WPBPGeneratePress {
    font-size: 1.8rem;
    margin-bottom: 20px;
    color: #2c3e50;
    font-family: 'Playfair Display', serif;
}

/* Fix for the star ratings */
.star-rating i, .fa-star, .fas.fa-star, .far.fa-star {
    color: #f5a623;
    font-size: 1.1rem;
    letter-spacing: 2px;
}
/* Style stars within "Most Popular" section icons */
.spa-tag-icon .fa-star,
.spa-tag-icon .fas.fa-star,
.spa-tag-icon .far.fa-star {
    color: #FFD700 !important; /* Match rating star color */
}

/* Ensure stars within "Most Popular" section icons change to white on hover */
.spa-tag-card:hover .spa-tag-icon .fa-star,
.spa-tag-card:hover .spa-tag-icon .fas.fa-star,
.spa-tag-card:hover .spa-tag-icon .far.fa-star {
    color: #fff !important; /* Change stars to white on hover */
}
/* Hover effect for "Most Popular" section icon container */
.spa-tag-card:hover .spa-tag-icon {
    background-color: #d2b48c; /* Match general hover color */
    transform: translateY(-3px) scale(1.05); /* Adjust transform for consistency */
}

/* Fix for the reviews count */
.reviews, .review-count, .spa-review-count {
    font-size: 0.9rem;
    color: #666;
    margin-left: 5px;
}

/* Fix for the location */
.location, .spa-location {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    color: #666;
    font-size: 0.9rem;
}

.location i, .spa-location i, .fa-map-marker-alt {
    margin-right: 5px;
    color: #4a90e2;
}

/* Fix for the services */
.services, .spa-services {
    margin-bottom: 15px;
    font-size: 0.9rem;
    color: #666;
}

/* Fix for the view details button */
.view-details, a.view-details, .view-details a, a.view-details-link, .view-details-link {
    display: inline-block;
    padding: 8px 15px;
    background-color: #4a90e2;
    color: #fff !important;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
    margin-top: 10px;
    letter-spacing: 0.02em; /* Added from earlier rule */
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05); /* Added from earlier rule */
}

.view-details:hover, a.view-details:hover, .view-details a:hover, a.view-details-link:hover, .view-details-link:hover {
    background-color: #2c3e50;
    color: #fff !important;
    text-decoration: none;
    transform: translateY(-3px); /* Added from earlier rule */
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.08); /* Added from earlier rule */
}

/* Fix for the arrow icon */
.view-details i, a.view-details i, .view-details a i, a.view-details-link i, .view-details-link i, .fa-arrow-right {
    margin-left: 5px;
}

/* Fix for the featured badge */
.featured-badge, .spa-featured-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: #f5a623;
    color: #fff;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
    z-index: 1;
}

/* Responsive Styles for Header and Hero */
@media (max-width: 1024px) {
    .inside-header {
        padding: 10px 20px;
    }

    .main-title {
        font-size: 24px;
    }

    .hero-title {
        font-size: 3rem;
    }

    .hero-subtitle {
        font-size: 1.2rem;
    }

    .hero-search input[type="search"] {
        padding: 15px 20px;
    }

    .hero-search button {
        width: 45px;
        height: 45px;
    }
}

@media (max-width: 768px) {
    .inside-header {
        flex-direction: column;
        text-align: center;
        padding: 15px;
    }

    .site-branding {
        margin-bottom: 15px;
    }

    .main-navigation {
        margin-left: 0;
    }

    .main-navigation ul {
        justify-content: center;
    }

    .hero-section {
        padding: 80px 0 60px;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .hero-categories {
        gap: 8px;
    }

    .spa-categories-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }

    .hero-category {
        padding: 8px 15px;
        font-size: 0.9rem;
    }
}

@media (max-width: 576px) {
    .main-title {
        font-size: 22px;
    }

    .site-description {
        font-size: 12px;
    }

    .main-navigation ul {
        flex-wrap: wrap;
    }

    .main-navigation li {
        margin: 3px;
    }

    .main-navigation a {
        padding: 8px 12px;
        font-size: 0.9rem;
    }

    .spa-categories-grid {
        grid-template-columns: 1fr 1fr;
        gap: 15px;
    }

    .hero-section {
        padding: 60px 0 50px;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .hero-search input[type="search"] {
        padding: 12px 15px;
        font-size: 1rem;
    }

    .hero-search button {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .hero-categories {
        flex-direction: column;
        align-items: center;
        gap: 8px;
    }

    .spa-categories-grid {
        grid-template-columns: 1fr;
    }

    .hero-category {
        width: 80%;
        padding: 8px 15px;
    }
}

/* Neighborhood section styling */
.spa-address-content,
.spa-neighborhood-content {
    display: flex;
    align-items: flex-start;
    margin-bottom: 15px;
}

.spa-address-content i,
.spa-neighborhood-content i {
    margin-right: 15px;
    font-size: 20px;
    color: #68a0a0;
    margin-top: 3px;
}

.spa-neighborhood {
    margin-top: 20px;
    margin-bottom: 20px;
}

.spa-neighborhood-text {
    font-size: 16px;
    line-height: 1.6;
}

/* Ensure page-header has a white background consistently */
.separate-containers .page-header, /* Specific override for GeneratePress theme structure */
.page-header { /* General override */
    background-color: #ffffff !important;
}
