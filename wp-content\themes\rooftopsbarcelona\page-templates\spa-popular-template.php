<?php
/**
 * Template Name: Spa Popular Features Template
 *
 * This template is used to display spas by popular feature (tag)
 */

get_header();

// Get the tag slug from query var
$tag_slug = get_query_var('tag_slug');

// If no query var, try to get from URL
if (empty($tag_slug)) {
    $current_url = $_SERVER['REQUEST_URI'];
    $url_parts = explode('/', trim($current_url, '/'));
    $tag_slug = end($url_parts);

    // If the last part is the page name, there's no slug
    if ($tag_slug === 'spa-popular') {
        $tag_slug = '';
    }
}

// Get the tag object if we have a slug
$tag = !empty($tag_slug) ? get_term_by('slug', $tag_slug, 'post_tag') : null;
?>

<div id="primary" class="content-area">
    <main id="main" class="site-main">
        <header class="page-header">
            <div class="page-title-wrapper">
                <?php
                if ($tag) {
                    echo '<h1 class="page-title">Spas in Barcelona with ' . esc_html($tag->name) . '</h1>';
                } else {
                    echo '<h1 class="page-title">Popular Spa Features</h1>';
                }
                ?>
            </div>
            <div class="archive-description">
                <?php
                if ($tag && !empty($tag->description)) {
                    echo wp_kses_post($tag->description);
                } else if ($tag) {
                    echo '<p>Discover the best spas in Barcelona with the popular feature: ' . esc_html($tag->name) . '. Browse our selection of spas with this feature.</p>';
                } else {
                    echo '<p>Browse our selection of spas in Barcelona by popular features.</p>';
                }
                ?>
            </div>
        </header>



        <div class="spa-results" style="width: 100%;">
            <?php
            if ($tag) {
                // Create a custom query to get spas with this tag
                $args = array(
                    'post_type' => 'spa',
                    'posts_per_page' => -1,
                    'tag' => $tag_slug,
                );

                $spa_query = new WP_Query($args);

                if ($spa_query->have_posts()) :
                ?>
                    <div class="spa-grid">
                        <?php
                        while ($spa_query->have_posts()) :
                            $spa_query->the_post();
                            get_template_part('template-parts/content', 'spa-card');
                        endwhile;
                        wp_reset_postdata();
                        ?>
                    </div>
                <?php else : ?>
                    <div class="no-results">
                        <p><?php esc_html_e('No spas found matching your criteria.', 'spasinbarcelona'); ?></p>
                    </div>
                <?php endif;
            } else {
                // Display all popular features (tags)
                global $wpdb;

                // Get popular features that are actually used by spa posts
                $popular_features = $wpdb->get_results(
                    "SELECT DISTINCT t.term_id, t.name, t.slug, COUNT(tr.object_id) as count
                    FROM {$wpdb->terms} t
                    INNER JOIN {$wpdb->term_taxonomy} tt ON t.term_id = tt.term_id
                    INNER JOIN {$wpdb->term_relationships} tr ON tr.term_taxonomy_id = tt.term_taxonomy_id
                    INNER JOIN {$wpdb->posts} p ON p.ID = tr.object_id
                    WHERE tt.taxonomy = 'post_tag'
                    AND p.post_type = 'spa'
                    AND p.post_status = 'publish'
                    GROUP BY t.term_id
                    ORDER BY t.name ASC"
                );

                if (!empty($popular_features) && !is_wp_error($popular_features)) :
                ?>
                    <div class="spa-tags-grid">
                        <?php foreach ($popular_features as $feature) : ?>
                            <a href="<?php echo esc_url(home_url('/spa-popular/' . $feature->slug . '/')); ?>" class="spa-tag-card">
                                <h3 class="spa-tag-title"><?php echo esc_html($feature->name); ?></h3>
                                <span class="spa-tag-count"><?php echo esc_html($feature->count); ?> <?php echo esc_html(_n('Spa', 'Spas', $feature->count, 'spasinbarcelona')); ?></span>
                            </a>
                        <?php endforeach; ?>
                    </div>
                <?php else : ?>
                    <div class="no-results">
                        <p><?php esc_html_e('No popular features found.', 'spasinbarcelona'); ?></p>
                    </div>
                <?php endif;
            }
            ?>
        </div>
    </main>
</div>

<style>
    .spa-tags-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 20px;
        margin-top: 30px;
    }

    .spa-tag-card {
        background-color: #f5f5f0;
        border-radius: 8px;
        padding: 20px;
        text-align: center;
        text-decoration: none;
        color: #333;
        transition: all 0.3s ease;
        border: 1px solid rgba(0,0,0,0.05);
    }

    .spa-tag-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }

    .spa-tag-title {
        margin-top: 0;
        margin-bottom: 10px;
        font-size: 1.2rem;
    }

    .spa-tag-count {
        font-size: 0.9rem;
        color: #666;
    }
</style>

<?php
get_footer();
