<?php
/**
 * Template Name: Homepage
 *
 * The template for displaying the homepage
 */

get_header();
?>

<div id="primary" class="content-area">
    <main id="main" class="site-main">
        <section class="spa-hero">
            <div class="spa-hero-content">
                <h1 class="spa-hero-title">Discover the Best Rooftops in Barcelona</h1>
                <p class="spa-hero-subtitle">Find stunning rooftop bars, terraces, and panoramic views across Barcelona</p>

                <div class="spa-hero-search">
                    <form action="<?php echo esc_url( get_post_type_archive_link( 'rooftop' ) ); ?>" method="get">
                        <input type="text" name="search" placeholder="Search for rooftops, views, or features...">
                        <button type="submit"><i class="fas fa-search"></i></button>
                    </form>
                </div>

                <div class="spa-hero-categories">
                    <?php
                    $categories = get_terms( array(
                        'taxonomy' => 'rooftop_popular',
                        'hide_empty' => true,
                        'number' => 5,
                    ) );

                    if ( ! empty( $categories ) && ! is_wp_error( $categories ) ) {
                        foreach ( $categories as $category ) {
                            echo '<a href="' . esc_url( get_term_link( $category ) ) . '" class="spa-hero-category">';
                            echo '<i class="fas fa-building"></i> ' . esc_html( $category->name );
                            echo '</a>';
                        }
                    }
                    ?>
                    <a href="<?php echo esc_url( get_post_type_archive_link( 'rooftop' ) ); ?>" class="spa-hero-category view-all">
                        <i class="fas fa-th-list"></i> View All Rooftops <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </section>

        <section class="spa-featured">
            <div class="spa-section-header">
                <h2 class="spa-section-title">Featured Rooftops in Barcelona</h2>
                <p class="spa-section-subtitle">Discover our handpicked selection of the best rooftops in Barcelona</p>
            </div>

            <div class="spa-grid">
                <?php
                // Get featured rooftops from customizer setting
                $featured_rooftops = get_theme_mod( 'rooftopsbarcelona_featured_rooftops', array() );

                if ( ! empty( $featured_rooftops ) ) {
                    $args = array(
                        'post_type' => 'rooftop',
                        'post__in' => $featured_rooftops,
                        'posts_per_page' => 6,
                    );
                } else {
                    // If no featured rooftops are set, get the most recent ones
                    $args = array(
                        'post_type' => 'rooftop',
                        'posts_per_page' => 6,
                    );
                }

                $featured_query = new WP_Query( $args );

                if ( $featured_query->have_posts() ) {
                    while ( $featured_query->have_posts() ) {
                        $featured_query->the_post();
                        get_template_part( 'template-parts/content', 'rooftop-card' );
                    }
                    wp_reset_postdata();
                } else {
                    echo '<div class="no-results"><p>No featured rooftops found.</p></div>';
                }
                ?>
            </div>

            <div class="spa-section-footer">
                <a href="<?php echo esc_url( get_post_type_archive_link( 'rooftop' ) ); ?>" class="spa-button">
                    View All Rooftops <i class="fas fa-arrow-right"></i>
                </a>
            </div>
        </section>

        <section class="spa-categories">
            <div class="spa-section-header">
                <h2 class="spa-section-title">Most Popular Rooftop Features in Barcelona</h2>
                <p class="spa-section-subtitle">Find the perfect rooftop experience for your needs</p>
            </div>

            <div class="spa-categories-grid">
                <?php
                $categories = get_terms( array(
                    'taxonomy' => 'rooftop_popular',
                    'hide_empty' => true,
                ) );

                if ( ! empty( $categories ) && ! is_wp_error( $categories ) ) {
                    foreach ( $categories as $category ) {
                        $icon_class = 'fa-building';

                        // Assign different icons based on category name
                        if ( stripos( $category->name, 'view' ) !== false || stripos( $category->name, 'panoramic' ) !== false ) {
                            $icon_class = 'fa-mountain';
                        } elseif ( stripos( $category->name, 'bar' ) !== false || stripos( $category->name, 'cocktail' ) !== false ) {
                            $icon_class = 'fa-cocktail';
                        } elseif ( stripos( $category->name, 'terrace' ) !== false ) {
                            $icon_class = 'fa-leaf';
                        } elseif ( stripos( $category->name, 'sunset' ) !== false ) {
                            $icon_class = 'fa-sun';
                        } elseif ( stripos( $category->name, 'pool' ) !== false ) {
                            $icon_class = 'fa-swimming-pool';
                        }

                        echo '<a href="' . esc_url( get_term_link( $category ) ) . '" class="spa-category-card">';
                        echo '<div class="spa-category-icon"><i class="fas ' . esc_attr( $icon_class ) . '"></i></div>';
                        echo '<h3 class="spa-category-title">' . esc_html( $category->name ) . '</h3>';

                        // Get count of rooftops in this category
                        $count = $category->count;
                        echo '<span class="spa-category-count">' . esc_html( $count ) . ' ' . esc_html( _n( 'Rooftop', 'Rooftops', $count, 'rooftopsbarcelona' ) ) . '</span>';

                        echo '</a>';
                    }
                } else {
                    echo '<div class="no-results"><p>No categories found.</p></div>';
                }
                ?>
            </div>
        </section>

        <section class="spa-specialties">
            <div class="spa-section-header">
                <h2 class="spa-section-title">Explore Rooftop Services in Barcelona</h2>
                <p class="spa-section-subtitle">Find rooftops offering your favorite amenities and services</p>
            </div>

            <div class="spa-specialties-grid">
                <?php
                $services = get_terms( array(
                    'taxonomy' => 'rooftop_services',
                    'hide_empty' => true,
                    'orderby' => 'count',
                    'order' => 'DESC',
                    'number' => 8,
                ) );

                if ( ! empty( $services ) && ! is_wp_error( $services ) ) {
                    foreach ( $services as $service ) {
                        $icon_class = 'fa-concierge-bell';

                        // Assign different icons based on service name
                        if ( stripos( $service->name, 'bar' ) !== false || stripos( $service->name, 'cocktail' ) !== false ) {
                            $icon_class = 'fa-cocktail';
                        } elseif ( stripos( $service->name, 'restaurant' ) !== false || stripos( $service->name, 'dining' ) !== false ) {
                            $icon_class = 'fa-utensils';
                        } elseif ( stripos( $service->name, 'pool' ) !== false ) {
                            $icon_class = 'fa-swimming-pool';
                        } elseif ( stripos( $service->name, 'music' ) !== false || stripos( $service->name, 'dj' ) !== false ) {
                            $icon_class = 'fa-music';
                        } elseif ( stripos( $service->name, 'event' ) !== false || stripos( $service->name, 'party' ) !== false ) {
                            $icon_class = 'fa-calendar-alt';
                        } elseif ( stripos( $service->name, 'lounge' ) !== false ) {
                            $icon_class = 'fa-couch';
                        } elseif ( stripos( $service->name, 'vip' ) !== false ) {
                            $icon_class = 'fa-crown';
                        } elseif ( stripos( $service->name, 'wifi' ) !== false ) {
                            $icon_class = 'fa-wifi';
                        }

                        // Create a custom URL for the rooftop service
                        $service_link = home_url( '/rooftop-services/' . $service->slug . '/' );
                        echo '<a href="' . esc_url( $service_link ) . '" class="spa-specialty-card">';
                        echo '<div class="spa-specialty-icon"><i class="fas ' . esc_attr( $icon_class ) . '"></i></div>';
                        echo '<h3 class="spa-specialty-title">' . esc_html( $service->name ) . '</h3>';

                        // Get count of rooftops with this service
                        $count = $service->count;
                        echo '<span class="spa-specialty-count">' . esc_html( $count ) . ' ' . esc_html( _n( 'Rooftop', 'Rooftops', $count, 'rooftopsbarcelona' ) ) . '</span>';

                        echo '</a>';
                    }
                } else {
                    echo '<div class="no-results"><p>No services found.</p></div>';
                }
                ?>
            </div>
        </section>

        <section class="spa-features">
            <div class="spa-section-header">
                <h2 class="spa-section-title">Find Barcelona Rooftops by Amenities</h2>
                <p class="spa-section-subtitle">Find rooftops with these popular amenities and features</p>
            </div>

            <div class="spa-features-grid">
                <?php
                $features = get_terms( array(
                    'taxonomy' => 'rooftop_amenities',
                    'hide_empty' => true,
                    'orderby' => 'count',
                    'order' => 'DESC',
                    'number' => 6,
                ) );

                if ( ! empty( $features ) && ! is_wp_error( $features ) ) {
                    foreach ( $features as $feature ) {
                        $icon_class = 'fa-check-circle';

                        // Assign different icons based on feature name
                        if ( stripos( $feature->name, 'pool' ) !== false ) {
                            $icon_class = 'fa-swimming-pool';
                        } elseif ( stripos( $feature->name, 'bar' ) !== false ) {
                            $icon_class = 'fa-cocktail';
                        } elseif ( stripos( $feature->name, 'terrace' ) !== false ) {
                            $icon_class = 'fa-leaf';
                        } elseif ( stripos( $feature->name, 'lounge' ) !== false ) {
                            $icon_class = 'fa-couch';
                        } elseif ( stripos( $feature->name, 'restaurant' ) !== false || stripos( $feature->name, 'food' ) !== false ) {
                            $icon_class = 'fa-utensils';
                        } elseif ( stripos( $feature->name, 'parking' ) !== false ) {
                            $icon_class = 'fa-parking';
                        } elseif ( stripos( $feature->name, 'wifi' ) !== false ) {
                            $icon_class = 'fa-wifi';
                        } elseif ( stripos( $feature->name, 'wheelchair' ) !== false || stripos( $feature->name, 'accessible' ) !== false ) {
                            $icon_class = 'fa-wheelchair';
                        }

                        echo '<a href="' . esc_url( get_term_link( $feature ) ) . '" class="spa-feature-card">';
                        echo '<div class="spa-feature-icon"><i class="fas ' . esc_attr( $icon_class ) . '"></i></div>';
                        echo '<h3 class="spa-feature-title">' . esc_html( $feature->name ) . '</h3>';

                        // Get count of rooftops with this feature
                        $count = $feature->count;
                        echo '<span class="spa-feature-count">' . esc_html( $count ) . ' ' . esc_html( _n( 'Rooftop', 'Rooftops', $count, 'rooftopsbarcelona' ) ) . '</span>';

                        echo '</a>';
                    }
                } else {
                    echo '<div class="no-results"><p>No features found.</p></div>';
                }
                ?>
            </div>
        </section>

        <section class="spa-tags">
            <div class="spa-section-header">
                <h2 class="spa-section-title">Explore Rooftops by Barcelona Neighborhoods</h2>
                <p class="spa-section-subtitle">Discover rooftops by commonly searched neighborhoods and areas</p>
            </div>

            <div class="spa-tags-grid">
                <?php
                // Get neighborhood terms for rooftops
                $neighborhoods = get_terms( array(
                    'taxonomy' => 'rooftop_neighborhood',
                    'hide_empty' => true,
                    'orderby' => 'count',
                    'order' => 'DESC',
                    'number' => 8,
                ) );

                if ( ! empty( $neighborhoods ) && ! is_wp_error( $neighborhoods ) ) {
                    foreach ( $neighborhoods as $neighborhood ) {
                        $icon_class = 'fa-map-marker-alt';

                        // Assign different icons based on neighborhood name
                        if ( stripos( $neighborhood->name, 'gothic' ) !== false || stripos( $neighborhood->name, 'barrio' ) !== false ) {
                            $icon_class = 'fa-landmark';
                        } elseif ( stripos( $neighborhood->name, 'eixample' ) !== false ) {
                            $icon_class = 'fa-city';
                        } elseif ( stripos( $neighborhood->name, 'gracia' ) !== false ) {
                            $icon_class = 'fa-heart';
                        } elseif ( stripos( $neighborhood->name, 'barceloneta' ) !== false ) {
                            $icon_class = 'fa-water';
                        } elseif ( stripos( $neighborhood->name, 'born' ) !== false ) {
                            $icon_class = 'fa-palette';
                        }

                        echo '<a href="' . esc_url( get_term_link( $neighborhood ) ) . '" class="spa-tag-card">';
                        echo '<div class="spa-tag-icon"><i class="fas ' . esc_attr( $icon_class ) . '"></i></div>';
                        echo '<h3 class="spa-tag-title">' . esc_html( $neighborhood->name ) . '</h3>';
                        echo '<span class="spa-tag-count">' . esc_html( $neighborhood->count ) . ' ' . esc_html( _n( 'Rooftop', 'Rooftops', $neighborhood->count, 'rooftopsbarcelona' ) ) . '</span>';
                        echo '</a>';
                    }
                } else {
                    echo '<div class="no-results"><p>No neighborhoods found.</p></div>';
                }
                ?>
            </div>

            <div class="spa-section-footer">
                <a href="<?php echo esc_url( home_url( '/rooftop-neighborhoods/' ) ); ?>" class="spa-button">
                    View All Neighborhoods <i class="fas fa-arrow-right"></i>
                </a>
            </div>
        </section>

        <section class="spa-cta">
            <div class="spa-container">
                <div class="spa-cta-content">
                    <h2 class="spa-cta-title">Find Your Perfect Rooftop Experience</h2>
                    <p class="spa-cta-text">Browse our comprehensive directory of rooftops in Barcelona and discover your next elevated destination.</p>
                    <a href="<?php echo esc_url( get_post_type_archive_link( 'rooftop' ) ); ?>" class="spa-cta-button">
                        Explore All Rooftops <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </section>
    </main>
</div>

<?php
get_footer();
