<?php
/**
 * Custom template hooks for this theme
 */

/**
 * Head<PERSON> hooks
 */
function rooftopsbarcelona_header_additions() {
    // Add Font Awesome
    echo '<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" />';
}
add_action( 'wp_head', 'rooftopsbarcelona_header_additions' );

/**
 * Add custom CSS to fix footer styling
 */
function rooftopsbarcelona_footer_fix_css() {
    // This function is no longer needed as we're using the footer.css file
    // The styles have been moved to wp-content/themes/rooftopsbarcelona/css/footer.css
}
// Removed the action hook since we're using the enqueued footer.css file instead
// add_action( 'wp_head', 'rooftopsbarcelona_footer_fix_css' );

/**
 * Add schema markup to single rooftop pages and directory pages
 */
function rooftopsbarcelona_add_rooftop_schema() {
    if ( is_singular( 'rooftop' ) ) {
        echo rooftopsbarcelona_get_rooftop_schema( get_the_ID() );
    } elseif ( is_post_type_archive( 'rooftop' ) || is_tax( array( 'rooftop_neighborhood', 'rooftop_services', 'rooftop_amenities', 'rooftop_popular' ) ) ) {
        echo rooftopsbarcelona_get_directory_schema();
    }
}
add_action( 'wp_head', 'rooftopsbarcelona_add_rooftop_schema' );

/**
 * Modify archive title for spa taxonomies and tags
 */
function spasinbarcelona_archive_title( $title ) {
    if ( is_tax( 'popular' ) ) {
        $term_name = single_term_title( '', false );
        $title = sprintf( 'Best Popular Spas for %s in Barcelona', $term_name );
    } elseif ( is_tax( 'services' ) ) {
        $term_name = single_term_title( '', false );
        $title = sprintf( 'Best %s Services in Barcelona Spas', $term_name );
    } elseif ( is_tax( 'amenities' ) ) {
        $term_name = single_term_title( '', false );
        $title = sprintf( 'Best Barcelona Spas Featuring %s', $term_name );
    } elseif ( is_tax( 'neighborhoods' ) ) {
        $term = get_queried_object();
        $term_name = single_term_title('', false);
        if ($term && $term->parent) {
            $parent = get_term($term->parent, 'neighborhoods');
            if ($parent && !is_wp_error($parent)) {
                $title = sprintf( 'Best Spas in %s, %s, Barcelona', $term_name, esc_html($parent->name) );
            } else {
                $title = sprintf( 'Best Spas in %s, Barcelona', $term_name );
            }
        } elseif ($term) {
            $title = sprintf( 'Best Spas in the %s District, Barcelona', $term_name );
        }
        // If $term is not set, $title remains unchanged
    } elseif ( is_post_type_archive( 'spa' ) ) {
        $title = 'Directory of All Spas in Barcelona';
    } elseif ( is_tag() ) {
        $title = single_tag_title( 'Spas in Barcelona tagged with ', false );
    }

    // Return the title without span wrapper for better SEO
    return $title;
}
add_filter( 'get_the_archive_title', 'spasinbarcelona_archive_title' );

/**
 * Enhance the entry title for regular pages and posts
 */
function spasinbarcelona_entry_title( $title ) {
    // Return the title without modification for better SEO
    return $title;
}
add_filter( 'the_title', 'spasinbarcelona_entry_title' );

/**
 * Add enhanced meta tags for SEO including Open Graph and Twitter Cards
 */
function spasinbarcelona_meta_description() {
    $description = '';
    $title = wp_get_document_title();
    $url = '';
    $image = '';
    $type = 'website';

    // Get the current URL
    $url = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";

    // Default image (site logo or fallback)
    $custom_logo_id = get_theme_mod('custom_logo');
    if ($custom_logo_id) {
        $logo_image = wp_get_attachment_image_src($custom_logo_id, 'full');
        if ($logo_image) {
            $image = $logo_image[0];
        }
    }

    // If no logo, use a default image
    if (empty($image)) {
        $image = get_stylesheet_directory_uri() . '/images/barcelona-aerial-view.jpg';
    }

    if (is_singular('spa')) {
        // For single spa pages
        $spa_id = get_the_ID();

        // First check if we have a custom meta description
        $custom_meta = get_post_meta($spa_id, 'meta_description', true);

        if (!empty($custom_meta)) {
            $description = $custom_meta;
        } else {
            // Fall back to excerpt
            $description = get_the_excerpt();

            // If no excerpt, use trimmed content
            if (empty($description)) {
                $description = wp_trim_words(get_the_content(), 30, '...');
            }
        }

        // Get spa image for Open Graph
        if (has_post_thumbnail($spa_id)) {
            $image = get_the_post_thumbnail_url($spa_id, 'large');
        } else {
            $images = get_post_meta($spa_id, 'images', true);
            if (!empty($images) && is_array($images) && !empty($images[0]['url'])) {
                $image = esc_url($images[0]['url']);
            }
        }

        // Set content type for single spa
        $type = 'article';

    } elseif (is_tax('popular')) { // Updated from spa_category
        // For 'popular' taxonomy pages
        $term = get_queried_object();
        $term_name = esc_html( $term->name );
        $description = sprintf( 'Discover top-rated spas in Barcelona known for %s. Find your ideal %s spa experience and book today for ultimate relaxation and rejuvenation.', $term_name, $term_name );

    } elseif (is_tax('services')) { // Updated from spa_service
        // For 'services' taxonomy pages
        $term = get_queried_object();
        $term_name = esc_html( $term->name );
        $description = sprintf( 'Looking for %s in Barcelona? Explore our curated list of spas offering professional %s services. Book your treatment and indulge.', $term_name, $term_name );

    } elseif (is_tax('amenities')) { // Updated from spa_feature
        // For 'amenities' taxonomy pages
        $term = get_queried_object();
        $term_name = esc_html( $term->name );
        $description = sprintf( 'Find spas in Barcelona equipped with %s. Enjoy premium spa amenities like %s for an enhanced and luxurious visit.', $term_name, $term_name );

    } elseif (is_tax('neighborhoods')) { // Kept 'neighborhoods' (was spa_neighborhood)
        // For neighborhood pages
        $term = get_queried_object();

        // Check if there's a custom description in term meta
        $seo_description = get_term_meta($term->term_id, 'seo_description', true);
        if (!empty($seo_description)) {
            $description = $seo_description;
        } else {
            // Get parent term (district) if it exists
            if ($term->parent) {
                $parent_term = get_term($term->parent, 'spa_neighborhood');
                if (!is_wp_error($parent_term)) {
                    $description = "Discover the best spas in the " . $term->name . " neighborhood of " . $parent_term->name . "in Barcelona. Find luxury spa experiences, wellness centers, and beauty treatments in this area.";
                } else {
                    $description = "Discover the best spas in " . $term->name . ", Barcelona. Find luxury spa experiences, wellness centers, and beauty treatments in this neighborhood.";
                }
            } else {
                $description = "Discover the best spas in the " . $term->name . " area of Barcelona. Find luxury spa experiences, wellness centers, and beauty treatments in this district.";
            }
        }

    } elseif (is_tag()) {
        // For tag pages
        $term = get_queried_object();
        $description = "Discover spas in Barcelona tagged with '" . $term->name . "'. Find the perfect spa experience based on this popular tag.";

    } elseif (is_post_type_archive('spa')) {
        // For main archive
        $description = "Explore our complete directory of spas in Barcelona. Filter by services, features, price range, and more to find your perfect spa experience.";

    } elseif (is_home() || is_front_page()) {
        // For homepage
        $description = "Discover the best spas in Barcelona with our comprehensive directory. Find luxury spas, wellness centers, and beauty treatments across Barcelona.";
    }

    // Output meta tags if we have a description
    if (!empty($description)) {
        // Basic meta description
        echo '<meta name="description" content="' . esc_attr($description) . '" />' . "\n";

        // Canonical URL
        echo '<link rel="canonical" href="' . esc_url($url) . '" />' . "\n";

        // Open Graph tags
        echo '<meta property="og:locale" content="' . esc_attr(get_locale()) . '" />' . "\n";
        echo '<meta property="og:type" content="' . esc_attr($type) . '" />' . "\n";
        echo '<meta property="og:title" content="' . esc_attr($title) . '" />' . "\n";
        echo '<meta property="og:description" content="' . esc_attr($description) . '" />' . "\n";
        echo '<meta property="og:url" content="' . esc_url($url) . '" />' . "\n";
        echo '<meta property="og:site_name" content="' . esc_attr(get_bloginfo('name')) . '" />' . "\n";

        if (!empty($image)) {
            echo '<meta property="og:image" content="' . esc_url($image) . '" />' . "\n";
            echo '<meta property="og:image:secure_url" content="' . esc_url(str_replace('http://', 'https://', $image)) . '" />' . "\n";
            echo '<meta property="og:image:width" content="1200" />' . "\n";
            echo '<meta property="og:image:height" content="630" />' . "\n";
        }

        // Twitter Card tags
        echo '<meta name="twitter:card" content="summary_large_image" />' . "\n";
        echo '<meta name="twitter:description" content="' . esc_attr($description) . '" />' . "\n";
        echo '<meta name="twitter:title" content="' . esc_attr($title) . '" />' . "\n";

        if (!empty($image)) {
            echo '<meta name="twitter:image" content="' . esc_url($image) . '" />' . "\n";
        }
    }
}
add_action('wp_head', 'spasinbarcelona_meta_description', 1);

/**
 * Add canonical URL for spa directory pages with filters
 * This helps prevent duplicate content issues with filtered results
 */
function spasinbarcelona_canonical_url() {
    // Only run on spa archive or taxonomy pages
    if (!is_post_type_archive('spa') && !is_tax(array('spa_category', 'spa_service', 'spa_feature', 'spa_neighborhood'))) {
        return;
    }

    // Get the base URL without query parameters
    $canonical_url = '';

    if (is_post_type_archive('spa')) {
        $canonical_url = get_post_type_archive_link('spa');
    } elseif (is_tax()) {
        $canonical_url = get_term_link(get_queried_object());
    }

    // If we have query parameters that aren't pagination, use the base URL as canonical
    if (!empty($_GET) && (count($_GET) > 1 || !isset($_GET['paged']))) {
        // We already output canonical URL in meta_description function, so remove the default one
        remove_action('wp_head', 'rel_canonical');
    }
}
add_action('wp_head', 'spasinbarcelona_canonical_url', 0);

/**
 * Add enhanced breadcrumbs with schema markup to spa pages
 */
function spasinbarcelona_breadcrumbs() {
    // Only show on spa related pages
    if (!is_singular('spa') && !is_post_type_archive('spa') && !is_tax(array('spa_category', 'spa_service', 'spa_feature', 'spa_neighborhood')) && !is_tag()) {
        return;
    }

    $home_url = home_url();
    $home_label = __('Home', 'spasinbarcelona');
    $position = 1;

    // Start breadcrumbs container with schema markup
    $html = '<div class="spa-breadcrumbs" itemscope itemtype="https://schema.org/BreadcrumbList">';

    // Home link
    $html .= '<span itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">';
    $html .= '<a href="' . esc_url($home_url) . '" itemprop="item"><span itemprop="name">' . esc_html($home_label) . '</span></a>';
    $html .= '<meta itemprop="position" content="' . $position++ . '" />';
    $html .= '</span>';

    if (is_post_type_archive('spa')) {
        // All Spas page
        $html .= ' &raquo; <span itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">';
        $html .= '<span itemprop="name">' . __('All Spas', 'spasinbarcelona') . '</span>';
        $html .= '<meta itemprop="position" content="' . $position++ . '" />';
        $html .= '<meta itemprop="item" content="' . esc_url(get_post_type_archive_link('spa')) . '" />';
        $html .= '</span>';
    } elseif (is_tax()) {
        // Taxonomy page (category, service, feature, neighborhood)
        $html .= ' &raquo; <span itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">';
        $html .= '<a href="' . esc_url(get_post_type_archive_link('spa')) . '" itemprop="item"><span itemprop="name">' . __('All Spas', 'spasinbarcelona') . '</span></a>';
        $html .= '<meta itemprop="position" content="' . $position++ . '" />';
        $html .= '</span>';

        // Current taxonomy term
        $html .= ' &raquo; <span itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">';
        $html .= '<span itemprop="name">' . single_term_title('', false) . '</span>';
        $html .= '<meta itemprop="position" content="' . $position++ . '" />';
        $html .= '<meta itemprop="item" content="' . esc_url(get_term_link(get_queried_object())) . '" />';
        $html .= '</span>';
    } elseif (is_tag()) {
        // Tag page
        $html .= ' &raquo; <span itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">';
        $html .= '<a href="' . esc_url(get_post_type_archive_link('spa')) . '" itemprop="item"><span itemprop="name">' . __('All Spas', 'spasinbarcelona') . '</span></a>';
        $html .= '<meta itemprop="position" content="' . $position++ . '" />';
        $html .= '</span>';

        // Current tag
        $html .= ' &raquo; <span itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">';
        $html .= '<span itemprop="name">' . __('Tag:', 'spasinbarcelona') . ' ' . single_tag_title('', false) . '</span>';
        $html .= '<meta itemprop="position" content="' . $position++ . '" />';
        $html .= '<meta itemprop="item" content="' . esc_url(get_term_link(get_queried_object())) . '" />';
        $html .= '</span>';
    } elseif (is_singular('spa')) {
        // Single spa page
        $html .= ' &raquo; <span itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">';
        $html .= '<a href="' . esc_url(get_post_type_archive_link('spa')) . '" itemprop="item"><span itemprop="name">' . __('All Spas', 'spasinbarcelona') . '</span></a>';
        $html .= '<meta itemprop="position" content="' . $position++ . '" />';
        $html .= '</span>';

        // Get primary category if available
        $terms = get_the_terms(get_the_ID(), 'spa_category');
        if ($terms && !is_wp_error($terms)) {
            $term = reset($terms);
            $html .= ' &raquo; <span itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">';
            $html .= '<a href="' . esc_url(get_term_link($term)) . '" itemprop="item"><span itemprop="name">' . esc_html($term->name) . '</span></a>';
            $html .= '<meta itemprop="position" content="' . $position++ . '" />';
            $html .= '</span>';
        }

        // Neighborhood level removed from breadcrumbs as per requirements

        // Current spa
        $html .= ' &raquo; <span itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">';
        $html .= '<span itemprop="name">' . get_the_title() . '</span>';
        $html .= '<meta itemprop="position" content="' . $position++ . '" />';
        $html .= '<meta itemprop="item" content="' . esc_url(get_permalink()) . '" />';
        $html .= '</span>';
    }

    $html .= '</div>';

    echo $html;
}
add_action('generate_before_content', 'spasinbarcelona_breadcrumbs');

/**
 * AJAX handler for spa filtering
 */
function spasinbarcelona_filter_spas() {
    // Check nonce
    if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'spasinbarcelona_nonce' ) ) {
        wp_send_json_error( 'Invalid nonce' );
    }

    $category = isset( $_POST['category'] ) ? sanitize_text_field( $_POST['category'] ) : '';
    $service = isset( $_POST['service'] ) ? sanitize_text_field( $_POST['service'] ) : '';
    $feature = isset( $_POST['feature'] ) ? sanitize_text_field( $_POST['feature'] ) : '';
    $neighborhood = isset( $_POST['neighborhood'] ) ? sanitize_text_field( $_POST['neighborhood'] ) : '';
    $sort_by = isset( $_POST['sort_by'] ) ? sanitize_text_field( $_POST['sort_by'] ) : '';
    $search = isset( $_POST['search'] ) ? sanitize_text_field( $_POST['search'] ) : '';

    $args = array(
        'post_type' => 'spa',
        'posts_per_page' => 10, // Changed from 12 to 10
        'paged' => isset( $_POST['paged'] ) ? absint( $_POST['paged'] ) : 1, // Changed from 'page' to 'paged'
    );

    // Build tax query
    $tax_query = array(
        'relation' => 'AND',
    );

    $has_tax_filter = false;

    // Neighborhood filter
    if ( ! empty( $neighborhood ) ) {
        $tax_query[] = array(
            'taxonomy' => 'neighborhoods',
            'field'    => 'slug',
            'terms'    => $neighborhood,
        );
        $has_tax_filter = true;
    }

    // Category filter (Most Popular)
    if ( ! empty( $category ) ) {
        $tax_query[] = array(
            'taxonomy' => 'popular', // Updated taxonomy slug (popular for category)
            'field' => 'slug',
            'terms' => $category,
        );
        $has_tax_filter = true;
    }

    // Service filter (Services)
    if ( ! empty( $service ) ) {
        $tax_query[] = array(
            'taxonomy' => 'services', // Updated taxonomy slug
            'field' => 'slug',
            'terms' => $service,
        );
        $has_tax_filter = true;
    }

    // Feature filter (Amenities)
    if ( ! empty( $feature ) ) {
        $tax_query[] = array(
            'taxonomy' => 'amenities', // Updated taxonomy slug (amenities for feature)
            'field' => 'slug',
            'terms' => $feature,
        );
        $has_tax_filter = true;
    }

    // Add tax_query if any taxonomy filter is active
    if ( $has_tax_filter ) {
        $args['tax_query'] = $tax_query;
    }

    // Add search query
    if ( ! empty( $search ) ) {
        $args['s'] = $search;
    }

    // Price Range filter removed

    // Add sorting
    $manual_max_pages = null; // Variable to store manually calculated max_pages for rating sort

    if (!empty($sort_by)) {
        switch ($sort_by) {
            case 'alphabetical':
                $args['orderby'] = 'title';
                $args['order'] = 'ASC';
                break;
            case 'rating':
                // For rating sorting, we need to calculate the average rating for each post
                // and then sort them manually since the rating is calculated, not stored

                // When sorting by rating, we apply the tax_query and search directly to the initial get_posts
                // to ensure we only rate and sort relevant spas.
                $rating_query_args = array(
                    'post_type' => 'spa',
                    'posts_per_page' => -1, // Get all matching posts for rating
                    'fields' => 'ids', // We only need IDs
                );

                // Apply existing taxonomy filters to the rating query
                if (isset($args['tax_query'])) {
                    $rating_query_args['tax_query'] = $args['tax_query'];
                }

                // Apply existing search filter to the rating query
                if (isset($args['s'])) {
                    $rating_query_args['s'] = $args['s'];
                }

                $all_posts_for_rating = get_posts($rating_query_args);

                if (!empty($all_posts_for_rating)) {
                    $post_ratings = array();
                    foreach ($all_posts_for_rating as $post_id) {
                        $reviews = get_post_meta($post_id, 'reviews', true);
                        $rating_data = spasinbarcelona_calculate_rating(!empty($reviews['review_sources']) ? $reviews['review_sources'] : array());
                        $post_ratings[$post_id] = $rating_data['rating'];
                    }
                    arsort($post_ratings); // Sort by rating, high to low
                    $sorted_post_ids = array_keys($post_ratings);
                    $total_sorted_posts = count($sorted_post_ids);

                    if ($total_sorted_posts > 0) {
                        $items_per_page = $args['posts_per_page'];
                        $manual_max_pages = ceil($total_sorted_posts / $items_per_page); // Calculate max_pages manually

                        // Apply pagination to the sorted IDs
                        $current_page = $args['paged'];
                        $offset = ($current_page - 1) * $items_per_page;

                        $paginated_ids = array_slice($sorted_post_ids, $offset, $items_per_page);

                        if (!empty($paginated_ids)) {
                            $args['post__in'] = $paginated_ids;
                            $args['orderby'] = 'post__in'; // Order by the paginated, sorted IDs
                            // When using post__in with pre-paginated IDs, WP_Query's 'paged' is not needed
                            // and 'posts_per_page' should be -1 or match the count of $paginated_ids
                            // to fetch all specified IDs.
                            unset($args['paged']);
                            $args['posts_per_page'] = -1;
                        } else {
                            // No posts for this page after sorting and pagination
                            $args['post__in'] = array(0); // Ensure no results if paginated_ids is empty
                        }
                    } else {
                        // No posts after rating sort (e.g., all had 0 rating or no matching posts)
                        $args['post__in'] = array(0); // Ensure no results
                    }
                } else {
                    // No posts matched the initial criteria for rating
                    $args['post__in'] = array(0); // Ensure no results
                }
                // Remove default ordering if sorting by rating and post__in is set
                if (isset($args['post__in'])) {
                    unset($args['order']); // order is not applicable with post__in & orderby=post__in
                }
                break;
            default:
                // Default sorting (by date or whatever is set if not 'rating' or 'alphabetical')
                if (empty($args['orderby'])) { // Ensure default if nothing else set it
                    $args['orderby'] = 'date';
                    $args['order'] = 'DESC';
                }
                break;
        }
    } else {
        // Default sorting if $sort_by is empty
        $args['orderby'] = 'date';
        $args['order'] = 'DESC';
    }

    $query = new WP_Query( $args );

    ob_start();

    if ( $query->have_posts() ) {
        echo '<div class="spa-grid">';
        while ( $query->have_posts() ) {
            $query->the_post();
            get_template_part( 'template-parts/content', 'spa-card' );
        }
        echo '</div>';
    } else {
        echo '<div class="no-results"><p>' . __( 'No spas found matching your criteria.', 'spasinbarcelona' ) . '</p></div>';
    }

    $html = ob_get_clean();

    // Add debug info for administrators
    if (current_user_can('administrator')) {
        $debug_info = '<div class="debug-info" style="margin-top: 20px; padding: 15px; background: #ffe; border: 2px solid #f00; font-size: 14px; color: #000;">';
        $debug_info .= '<p><strong>Debug Info (only visible to admins):</strong></p>';

        // Show all POST data for debugging
        $debug_info .= '<p><strong>Raw POST Data:</strong></p>';
        $debug_info .= '<pre>' . print_r($_POST, true) . '</pre>';

        if (!empty($neighborhood)) {
            // Get the term to verify it exists
            $term = get_term_by('slug', $neighborhood, 'spa_neighborhood');

            if ($term) {
                $debug_info .= '<p>Neighborhood: ' . esc_html($term->name) . ' (ID: ' . $term->term_id . ', Count: ' . $term->count . ')</p>';

                // No special cases - all neighborhoods are treated the same

                // Show term taxonomy information for better debugging
                global $wpdb;
                $term_taxonomy_id = $wpdb->get_var($wpdb->prepare(
                    "SELECT term_taxonomy_id FROM $wpdb->term_taxonomy
                    WHERE term_id = %d AND taxonomy = 'spa_neighborhood'",
                    $term->term_id
                ));
                $debug_info .= '<p>Term Taxonomy ID: ' . $term_taxonomy_id . '</p>';

                // Show the spa IDs we found for this neighborhood
                if (!empty($spa_ids)) {
                    $debug_info .= '<p>Found ' . count($spa_ids) . ' spas with this neighborhood</p>';
                    $debug_info .= '<p>Spa IDs: ' . implode(', ', $spa_ids) . '</p>';
                } else {
                    $debug_info .= '<p>No spas found with this neighborhood</p>';
                }
            } else {
                $debug_info .= '<p>Neighborhood term not found: ' . esc_html($neighborhood) . '</p>';
            }
        }

        // Show all active filters
        $debug_info .= '<p><strong>Active Filters:</strong></p>';
        $debug_info .= '<ul>';
        if (!empty($category)) $debug_info .= '<li>Category: ' . esc_html($category) . '</li>';
        if (!empty($service)) $debug_info .= '<li>Service: ' . esc_html($service) . '</li>';
        if (!empty($feature)) $debug_info .= '<li>Feature: ' . esc_html($feature) . '</li>';
        if (!empty($neighborhood)) $debug_info .= '<li>Neighborhood: ' . esc_html($neighborhood) . '</li>';
        if (!empty($sort_by)) $debug_info .= '<li>Sort by: ' . esc_html($sort_by) . '</li>';
        if (!empty($search)) $debug_info .= '<li>Search: ' . esc_html($search) . '</li>';
        $debug_info .= '</ul>';

        $debug_info .= '<p>Total posts found in current query: ' . $query->found_posts . '</p>';

        // Show the query arguments
        $debug_info .= '<p><strong>Query Arguments:</strong></p>';
        $debug_info .= '<pre>' . print_r($args, true) . '</pre>';

        // Show the posts found in the current query
        if ($query->have_posts()) {
            $debug_info .= '<p><strong>Posts in Current Query:</strong></p><ul>';

            // Get the ratings for debugging if available
            global $spa_ratings_debug;

            foreach ($query->posts as $post) {
                $rating_info = '';

                // If we're sorting by rating, show the calculated rating
                if (!empty($sort_by) && $sort_by === 'rating' && !empty($spa_ratings_debug) && isset($spa_ratings_debug[$post->ID])) {
                    $rating_info = ' - Rating: ' . $spa_ratings_debug[$post->ID];
                } else {
                    // Calculate rating for this post using the dedicated function
                    $reviews = get_post_meta($post->ID, 'reviews', true);
                    $rating_data = spasinbarcelona_calculate_rating(!empty($reviews['review_sources']) ? $reviews['review_sources'] : array());
                    $avg_rating = $rating_data['rating'];

                    if ($avg_rating > 0) {
                        $rating_info = ' - Rating: ' . $avg_rating . ' (' . $rating_data['count'] . ' reviews)';
                    }
                }

                $debug_info .= '<li>' . esc_html($post->post_title) . ' (ID: ' . $post->ID . $rating_info . ')</li>';
            }

            $debug_info .= '</ul>';
            wp_reset_postdata();
        } else {
            $debug_info .= '<p>No posts found in current query</p>';
        }

        $debug_info .= '</div>';

        $html .= $debug_info;
    }

    wp_send_json_success( array(
        'html' => $html,
        'max_pages' => ($manual_max_pages !== null) ? $manual_max_pages : $query->max_num_pages,
    ) );
}
add_action( 'wp_ajax_spasinbarcelona_filter_spas', 'spasinbarcelona_filter_spas' );
add_action( 'wp_ajax_nopriv_spasinbarcelona_filter_spas', 'spasinbarcelona_filter_spas' );
