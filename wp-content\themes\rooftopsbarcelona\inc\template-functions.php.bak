<?php
/**
 * Custom template functions for this theme
 */

/**
 * Add custom classes to the body
 */
function spasinbarcelona_body_classes( $classes ) {
    // Add a class if we're on the spa directory
    if ( is_post_type_archive( 'spa' ) || is_tax( 'spa_category' ) || is_tax( 'spa_feature' ) || is_tax( 'spa_service' ) || is_tax( 'spa_neighborhood' ) ) {
        $classes[] = 'spa-directory';
    }

    // Add a class for single spa pages
    if ( is_singular( 'spa' ) ) {
        $classes[] = 'single-spa-page';
    }

    return $classes;
}
add_filter( 'body_class', 'spasinbarcelona_body_classes' );

/**
 * Calculate the weighted average rating from review sources
 *
 * @param array $review_sources Array of review sources with rating and count
 * @return array Contains 'rating' (weighted average) and 'count' (total number of reviews)
 */
function spasinbarcelona_calculate_rating($review_sources) {
    // Initialize variables
    $total_rating = 0;
    $total_count = 0;

    // Check if we have valid review sources
    if (!empty($review_sources) && is_array($review_sources)) {
        // Loop through each review source
        foreach ($review_sources as $source) {
            // Make sure the source has both rating and count
            if (!empty($source['rating']) && !empty($source['count'])) {
                // Add weighted rating (rating * count) to total
                $total_rating += $source['rating'] * $source['count'];
                // Add count to total count
                $total_count += $source['count'];
            }
        }
    }

    // Calculate weighted average if we have reviews
    $avg_rating = 0;
    if ($total_count > 0) {
        $avg_rating = round($total_rating / $total_count, 1);
    }

    // Return both the average rating and the total count
    return array(
        'rating' => $avg_rating,
        'count' => $total_count
    );
}

/**
 * Generate star rating HTML using the star symbol (★)
 */
function spasinbarcelona_star_rating( $rating, $max = 5 ) {
    // For review sources on the spa page, use the badge style
    if (is_singular('spa')) {
        // Check if we're in the featured reviews section
        $backtrace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 10);
        $in_featured_review = false;
        $in_review_source = false;
        $source_name = '';

        foreach ($backtrace as $trace) {
            if (isset($trace['file']) && strpos($trace['file'], 'content-spa-single.php') !== false) {
                // If we're in the featured reviews section of the template
                if (isset($trace['line']) && $trace['line'] > 1200) {
                    $in_featured_review = true;
                    break;
                }

                // Check if we're in the review sources section
                if (isset($trace['line']) && $trace['line'] > 800 && $trace['line'] < 1200) {
                    $in_review_source = true;

                    // Try to get the source name from the current context
                    if (isset($source) && isset($source['source'])) {
                        $source_name = $source['source'];
                    }
                    break;
                }
            }
        }

        if ($in_featured_review) {
            // For featured reviews, use a simple rating display with number and star
            $html = '<div class="spa-rating-badge-stars">';
            $html .= '<strong>' . number_format($rating, 1) . '</strong>';
            $html .= ' <span class="star-icon">★</span>';
            $html .= '</div>';
            return $html;
        }

        if ($in_review_source) {
            // For review sources, use the new badge style with rounded rectangle and yellow star
            $badge_color = '';

            // Set badge color based on source name
            switch (strtolower($source_name)) {
                case 'google':
                case 'google reviews':
                    $badge_color = 'google-badge';
                    break;
                case 'tripadvisor':
                    $badge_color = 'tripadvisor-badge';
                    break;
                case 'yelp':
                    $badge_color = 'yelp-badge';
                    break;
                case 'booking':
                case 'booking.com':
                    $badge_color = 'booking-badge';
                    break;
                case 'trustpilot':
                    $badge_color = 'trustpilot-badge';
                    break;
                default:
                    $badge_color = 'default-badge';
                    break;
            }

            $html = '<div class="spa-rating-badge-new ' . $badge_color . '">';
            $html .= '<span class="rating-number-new">' . number_format($rating, 1) . '</span>';
            $html .= ' <span class="star-icon-new">★</span>';
            $html .= '</div>';
            return $html;
        }

        // For header rating in spa page, use the badge style with white text and yellow star
        $html = '<div class="spa-rating-badge-header">';
        $html .= '<span class="rating-number-header">' . number_format($rating, 1) . '</span>';
        $html .= ' <span class="star-icon-header">★</span>';
        $html .= '</div>';
        return $html;
    }

    // For other places, use the FontAwesome stars
    $html = '<div class="star-rating">';

    // Full stars
    $full_stars = floor($rating);

    // Half stars
    $half_stars = ceil($rating - $full_stars);

    // Empty stars
    $empty_stars = $max - $full_stars - $half_stars;

    // Add full stars
    for ($i = 0; $i < $full_stars; $i++) {
        $html .= '<i class="fas fa-star"></i>';
    }

    // Add half star if needed
    if ($half_stars) {
        $html .= '<i class="fas fa-star-half-alt"></i>';
    }

    // Add empty stars
    for ($i = 0; $i < $empty_stars; $i++) {
        $html .= '<i class="far fa-star"></i>';
    }

    $html .= '</div>';

    return $html;
}

/**
 * Format price display
 */
function spasinbarcelona_format_price( $price ) {
    // Remove any non-numeric characters except decimal point
    $price = preg_replace('/[^0-9.]/', '', $price);

    if ( empty( $price ) ) {
        return '';
    }

    return '€' . number_format( (float) $price, 2 );
}

/**
 * Get spa features as a formatted list
 */
function spasinbarcelona_get_spa_features( $post_id, $limit = 5 ) {
    $features = get_the_terms( $post_id, 'spa_feature' );

    if ( ! $features || is_wp_error( $features ) ) {
        return '';
    }

    $html = '<ul class="spa-features-list">';

    $count = 0;
    foreach ( $features as $feature ) {
        if ( $count >= $limit ) {
            break;
        }

        $html .= '<li><i class="fas fa-check-circle"></i> ' . esc_html( $feature->name ) . '</li>';
        $count++;
    }

    $html .= '</ul>';

    return $html;
}

/**
 * Get related spas based on taxonomy terms
 */
function spasinbarcelona_get_related_spas( $post_id, $limit = 3 ) {
    $terms = get_the_terms( $post_id, 'spa_category' );

    if ( ! $terms || is_wp_error( $terms ) ) {
        return array();
    }

    $term_ids = wp_list_pluck( $terms, 'term_id' );

    $args = array(
        'post_type' => 'spa',
        'posts_per_page' => $limit,
        'post__not_in' => array( $post_id ),
        'tax_query' => array(
            array(
                'taxonomy' => 'spa_category',
                'field' => 'term_id',
                'terms' => $term_ids,
            ),
        ),
    );

    return get_posts( $args );
}

/**
 * Generate schema.org structured data for the spa directory page
 */
function spasinbarcelona_get_directory_schema() {
    // Only generate schema for the spa directory page
    if (!is_post_type_archive('spa') && !is_tax(array('spa_category', 'spa_service', 'spa_feature', 'spa_neighborhood'))) {
        return '';
    }

    global $wp_query;

    // Get page title
    $title = wp_get_document_title();

    // Get page description
    $description = '';
    if (is_post_type_archive('spa')) {
        $description = "Explore our complete directory of spas in Barcelona. Filter by services, features, price range, and more to find your perfect spa experience.";
    } elseif (is_tax()) {
        $term = get_queried_object();
        if (is_tax('spa_category')) {
            $description = "Discover the best " . $term->name . " spas in Barcelona. Find detailed information, services, prices, and reviews.";
        } elseif (is_tax('spa_service')) {
            $description = "Find spas in Barcelona offering " . $term->name . ". Compare prices, read reviews, and book your perfect spa experience.";
        } elseif (is_tax('spa_feature')) {
            $description = "Looking for spas in Barcelona with " . $term->name . "? Browse our comprehensive directory of spas featuring " . $term->name . ".";
        } elseif (is_tax('spa_neighborhood')) {
            // Check if there's a custom description in term meta
            $seo_description = get_term_meta($term->term_id, 'seo_description', true);
            if (!empty($seo_description)) {
                $description = $seo_description;
            } else {
                // Simple neighborhood description
                $description = "Discover the best spas in " . $term->name . ", Barcelona. Find luxury spa experiences, wellness centers, and beauty treatments in this neighborhood.";
            }
        }
    }

    // Get current URL
    $url = get_pagenum_link(1, false);

    // Build the schema
    $schema = array(
        '@context' => 'https://schema.org',
        '@type' => 'CollectionPage',
        'name' => $title,
        'description' => $description,
        'url' => $url,
        'isPartOf' => array(
            '@type' => 'WebSite',
            'name' => get_bloginfo('name'),
            'url' => home_url()
        )
    );

    // Add breadcrumb list schema
    $breadcrumb_items = array();

    // Home page
    $breadcrumb_items[] = array(
        '@type' => 'ListItem',
        'position' => 1,
        'name' => 'Home',
        'item' => home_url()
    );

    // All Spas (if we're on a taxonomy page)
    if (is_tax()) {
        $breadcrumb_items[] = array(
            '@type' => 'ListItem',
            'position' => 2,
            'name' => 'All Spas',
            'item' => get_post_type_archive_link('spa')
        );

        // Current taxonomy term
        $breadcrumb_items[] = array(
            '@type' => 'ListItem',
            'position' => 3,
            'name' => single_term_title('', false),
            'item' => get_term_link(get_queried_object())
        );
    } else {
        // We're on the main spa archive
        $breadcrumb_items[] = array(
            '@type' => 'ListItem',
            'position' => 2,
            'name' => 'All Spas',
            'item' => get_post_type_archive_link('spa')
        );
    }

    // Add breadcrumb schema
    $breadcrumb_schema = array(
        '@type' => 'BreadcrumbList',
        'itemListElement' => $breadcrumb_items
    );

    // Add itemList schema for the spas
    $item_list_elements = array();
    $position = 1;

    if ($wp_query->have_posts()) {
        while ($wp_query->have_posts()) {
            $wp_query->the_post();
            $spa_id = get_the_ID();
            $spa_title = get_the_title();
            $spa_url = get_permalink();

            // Get spa image
            $image_url = '';
            if (has_post_thumbnail()) {
                $image_url = get_the_post_thumbnail_url($spa_id, 'medium');
            } else {
                $images = get_post_meta($spa_id, 'images', true);
                if (!empty($images) && is_array($images) && !empty($images[0]['url'])) {
                    $image_url = esc_url($images[0]['url']);
                }
            }

            // Get spa description
            $spa_description = get_the_excerpt();
            if (empty($spa_description)) {
                $spa_description = wp_trim_words(get_the_content(), 30, '...');
            }

            // Add spa to item list
            $item_list_elements[] = array(
                '@type' => 'ListItem',
                'position' => $position,
                'item' => array(
                    '@type' => 'DaySpa',
                    'name' => $spa_title,
                    'url' => $spa_url,
                    'description' => $spa_description,
                    'image' => $image_url
                )
            );

            $position++;
        }
        wp_reset_postdata();
    }

    // Add itemList schema if we have items
    if (!empty($item_list_elements)) {
        $item_list_schema = array(
            '@context' => 'https://schema.org',
            '@type' => 'ItemList',
            'itemListElement' => $item_list_elements,
            'numberOfItems' => count($item_list_elements)
        );
    }

    // Add pagination schema if needed
    if ($wp_query->max_num_pages > 1) {
        $current_page = max(1, get_query_var('paged'));

        if ($current_page > 1) {
            $schema['pagination'] = array(
                '@type' => 'SiteNavigationElement',
                'hasPart' => array(
                    array(
                        '@type' => 'SiteNavigationElement',
                        'name' => 'Previous',
                        'url' => get_pagenum_link($current_page - 1)
                    )
                )
            );
        }

        if ($current_page < $wp_query->max_num_pages) {
            if (!isset($schema['pagination'])) {
                $schema['pagination'] = array(
                    '@type' => 'SiteNavigationElement',
                    'hasPart' => array()
                );
            }

            $schema['pagination']['hasPart'][] = array(
                '@type' => 'SiteNavigationElement',
                'name' => 'Next',
                'url' => get_pagenum_link($current_page + 1)
            );
        }
    }

    // Prepare the output
    $output = '<script type="application/ld+json">' . wp_json_encode($schema) . '</script>';

    // Add breadcrumb schema
    $output .= '<script type="application/ld+json">' . wp_json_encode($breadcrumb_schema) . '</script>';

    // Add itemList schema if we have items
    if (!empty($item_list_elements)) {
        $output .= '<script type="application/ld+json">' . wp_json_encode($item_list_schema) . '</script>';
    }

    return $output;
}

/**
 * Generate schema.org structured data for spas
 */
function spasinbarcelona_get_spa_schema( $post_id ) {
    $post = get_post( $post_id );

    if ( ! $post || 'spa' !== $post->post_type ) {
        return '';
    }

    $name = get_the_title( $post_id );

    // Check for custom meta description first
    $meta_description = get_post_meta( $post_id, 'meta_description', true );

    // If no custom meta description, fall back to regular description
    $description = empty( $meta_description ) ? get_post_meta( $post_id, 'description', true ) : $meta_description;

    // If still empty, use excerpt
    if ( empty( $description ) ) {
        $description = get_the_excerpt( $post_id );
    }

    // Get neighborhood information for enhancing the description
    $neighborhood_name = '';
    $neighborhood_terms = get_the_terms($post_id, 'spa_neighborhood');

    if (!empty($neighborhood_terms) && !is_wp_error($neighborhood_terms)) {
        foreach ($neighborhood_terms as $term) {
            // Use the first neighborhood term we find
            if (empty($neighborhood_name)) {
                $neighborhood_name = $term->name;
            }
        }
    }

    // Fallback to meta values if taxonomy terms aren't available
    if (empty($neighborhood_name) && !empty($location['neighborhood'])) {
        $neighborhood_name = $location['neighborhood'];
    }

    // Enhance description with neighborhood and "near me" context if not already present
    if (!empty($neighborhood_name) && strpos($description, 'near me') === false && strpos($description, $neighborhood_name) === false) {
        // Only add if the description isn't too long already (keep under ~160 chars for meta description)
        if (strlen($description) < 120) {
            $description .= ' Located in the ' . $neighborhood_name . ' area, this is one of the best spas near me in Barcelona for relaxation and wellness.';
        }
    }

    $url = get_permalink( $post_id );

    // Get location data
    $location = get_post_meta( $post_id, 'location', true );
    $address = '';

    if ( ! empty( $location['address'] ) ) {
        $address = $location['address'];
    }

    $city = ! empty( $location['city'] ) ? $location['city'] : 'Barcelona';
    $country = ! empty( $location['country'] ) ? $location['country'] : 'Spain';
    $neighborhood = ! empty( $location['neighborhood'] ) ? $location['neighborhood'] : '';

    // Get rating data with enhanced processing
    $reviews = get_post_meta( $post_id, 'reviews', true );
    $rating = '';
    $review_count = '';
    $review_schema_items = array();
    $review_sources_data = array();

    if ( ! empty( $reviews['review_sources'] ) && is_array( $reviews['review_sources'] ) ) {
        $total_rating = 0;
        $total_count = 0;
        $source_ratings = array();

        foreach ( $reviews['review_sources'] as $source ) {
            if ( ! empty( $source['rating'] ) && ! empty( $source['count'] ) ) {
                // Store source data for individual source ratings
                $source_name = !empty($source['name']) ? $source['name'] : 'Review Site';
                $source_url = !empty($source['url']) ? $source['url'] : '';

                // Calculate weighted contribution to overall rating
                $total_rating += $source['rating'] * $source['count'];
                $total_count += $source['count'];

                // Store source data for schema
                $review_sources_data[] = array(
                    'name' => $source_name,
                    'url' => $source_url,
                    'rating' => floatval($source['rating']),
                    'count' => intval($source['count'])
                );
            }
        }

        if ( $total_count > 0 ) {
            $rating = round( $total_rating / $total_count, 1 );
            $review_count = $total_count;
        }
    }

    
    // Get featured reviews for schema markup with enhanced details
    if ( ! empty( $reviews['featured_reviews'] ) && is_array( $reviews['featured_reviews'] ) ) {
        $processed_reviews = array(); // Track processed reviews to avoid duplicates
        
        foreach ( $reviews['featured_reviews'] as $review ) {
            if ( ! empty( $review['text'] ) && ! empty( $review['author'] ) ) {
                // Create a unique key for this review to detect duplicates
                $review_key = md5($review['author'] . '-' . $review['text']);
                
                // Skip if we've already processed this review
                if (isset($processed_reviews[$review_key])) {
                    continue;
                }
                
                // Mark this review as processed
                $processed_reviews[$review_key] = true;
                
                // Create enhanced review schema
                $review_schema = array(
                    '@type' => 'Review',
                    'reviewBody' => $review['text'],
                    'author' => array(
                        '@type' => 'Person',
                        'name' => $review['author']
                    ),
                    'itemReviewed' => array(
                        '@type' => 'HealthAndBeautyBusiness',
                        'name' => $name,
                        'url' => $url
                    )
                );
                    '@type' => 'Review',
                    'reviewBody' => $review['text'],
                    'author' => array(
                        '@type' => 'Person',
                        'name' => $review['author']
                    ),
                    'itemReviewed' => array(
                        '@type' => 'HealthAndBeautyBusiness',
                        'name' => $name,
                        'url' => $url
                    )
                );

                // Add rating with enhanced details
                if ( ! empty( $review['rating'] ) ) {
                    $review_schema['reviewRating'] = array(
                        '@type' => 'Rating',
                        'ratingValue' => floatval($review['rating']),
                        'bestRating' => 5,
                        'worstRating' => 1
                    );
                }

                // Add date with enhanced format handling
                if ( ! empty( $review['date'] ) ) {
                    // Try multiple date formats
                    $date_formats = array('d/m/Y', 'Y-m-d', 'm/d/Y', 'd-m-Y', 'Y/m/d');
                    $date_parsed = false;

                    foreach ($date_formats as $format) {
                        $date_obj = DateTime::createFromFormat($format, $review['date']);
                        if ($date_obj) {
                            $review_schema['datePublished'] = $date_obj->format('Y-m-d');
                            $date_parsed = true;
                            break;
                        }
                    }

                    // If no format matched, try strtotime as a fallback
                    if (!$date_parsed) {
                        $timestamp = strtotime($review['date']);
                        if ($timestamp) {
                            $review_schema['datePublished'] = date('Y-m-d', $timestamp);
                        } else {
                            // If all else fails, use as-is but note it might not be valid
                            $review_schema['datePublished'] = $review['date'];
                        }
                    }
                } else {
                    // If no date provided, use current date
                    $review_schema['datePublished'] = date('Y-m-d');
                }

                // Add source information if available
                if (!empty($review['source'])) {
                    $review_schema['publisher'] = array(
                        '@type' => 'Organization',
                        'name' => $review['source']
                    );

                    // Add source URL if available
                    if (!empty($review['sourceUrl'])) {
                        $review_schema['publisher']['url'] = $review['sourceUrl'];
                        $review_schema['url'] = $review['sourceUrl'];
                    }
                }

                // Add language if available
                if (!empty($review['language'])) {
                    $review_schema['inLanguage'] = $review['language'];
                } else {
                    // Default to Spanish for Barcelona
                    $review_schema['inLanguage'] = 'es';
                }

                $review_schema_items[] = $review_schema;
            }
        }
    }

    // Create individual review source ratings
    if (!empty($review_sources_data)) {
        foreach ($review_sources_data as $source) {
            // Only add sources with meaningful data
            if (!empty($source['name']) && !empty($source['rating']) && !empty($source['count'])) {
                $source_review = array(
                    '@type' => 'Review',
                    'author' => array(
                        '@type' => 'Organization',
                        'name' => $source['name']
                    ),
                    'reviewRating' => array(
                        '@type' => 'AggregateRating',
                        'ratingValue' => $source['rating'],
                        'reviewCount' => $source['count'],
                        'bestRating' => 5,
                        'worstRating' => 1
                    ),
                    'itemReviewed' => array(
                        '@type' => 'HealthAndBeautyBusiness',
                        'name' => $name,
                        'url' => $url
                    )
                );

                // Add source URL if available
                if (!empty($source['url'])) {
                    $source_review['author']['url'] = $source['url'];
                    $source_review['url'] = $source['url'];
                }

                $review_schema_items[] = $source_review;
            }
        }
    }

    // Get contact information
    $contact = get_post_meta( $post_id, 'contact', true );
    $phone = ! empty( $contact['phone'] ) ? $contact['phone'] : '';
    $email = ! empty( $contact['email'] ) ? $contact['email'] : '';
    $website = ! empty( $contact['website'] ) ? $contact['website'] : '';

    // Get opening hours with enhanced handling for different formats
    $hoursSchema = get_post_meta( $post_id, 'hoursSchema', true );
    $hours = get_post_meta( $post_id, 'hours', true );
    $hoursDisplay = get_post_meta( $post_id, 'hoursDisplay', true );
    $opening_hours_spec = array();

    // Define the days of the week in schema.org format
    $day_mapping = array(
        'monday' => 'Monday',
        'tuesday' => 'Tuesday',
        'wednesday' => 'Wednesday',
        'thursday' => 'Thursday',
        'friday' => 'Friday',
        'saturday' => 'Saturday',
        'sunday' => 'Sunday'
    );

    // Track which days have been processed to ensure all days are represented
    $processed_days = array();

    // 1. If hoursSchema is available, use it directly (most accurate format)
    if ( ! empty( $hoursSchema ) && is_array( $hoursSchema ) ) {
        foreach ($hoursSchema as $spec) {
            // Ensure the schema is properly formatted
            if (isset($spec['dayOfWeek']) && isset($spec['opens']) && isset($spec['closes'])) {
                // Add to processed days
                if (is_array($spec['dayOfWeek'])) {
                    foreach ($spec['dayOfWeek'] as $day) {
                        $processed_days[$day] = true;
                    }
                } else {
                    $processed_days[$spec['dayOfWeek']] = true;
                }

                // Add to opening hours specification
                $opening_hours_spec[] = $spec;
            }
        }
    }
    // 2. Otherwise, try to generate from hours data (structured format)
    elseif ( ! empty( $hours ) && is_array( $hours ) ) {
        foreach ( $hours as $day => $time ) {
            if ( isset( $day_mapping[$day] ) ) {
                $day_name = $day_mapping[$day];
                $processed_days[$day_name] = true;

                // Handle new format with open/close/isClosed
                if ( is_array( $time ) ) {
                    // Create a special entry for closed days
                    if ( !empty( $time['isClosed'] ) && $time['isClosed'] ) {
                        $opening_hours_spec[] = array(
                            '@type' => 'OpeningHoursSpecification',
                            'dayOfWeek' => $day_name,
                            'validFrom' => date('Y-m-d'), // Current date
                            'validThrough' => date('Y-m-d', strtotime('+1 year')), // Valid for a year
                            'closes' => '00:00', // Required by schema
                            'opens' => '00:00'   // Required by schema
                        );
                        continue;
                    }

                    // Check if we have both open and close times
                    if ( !empty( $time['open'] ) && !empty( $time['close'] ) ) {
                        // Convert to 24-hour format for schema
                        $opening_time_24h = date( 'H:i', strtotime( $time['open'] ) );
                        $closing_time_24h = date( 'H:i', strtotime( $time['close'] ) );

                        $opening_hours_spec[] = array(
                            '@type' => 'OpeningHoursSpecification',
                            'dayOfWeek' => $day_name,
                            'opens' => $opening_time_24h,
                            'closes' => $closing_time_24h
                        );
                    }
                }
                // Handle old format (string)
                else if ( !empty( $time ) ) {
                    // Handle closed days
                    if ( strtolower( $time ) === 'closed' ) {
                        $opening_hours_spec[] = array(
                            '@type' => 'OpeningHoursSpecification',
                            'dayOfWeek' => $day_name,
                            'validFrom' => date('Y-m-d'), // Current date
                            'validThrough' => date('Y-m-d', strtotime('+1 year')), // Valid for a year
                            'closes' => '00:00', // Required by schema
                            'opens' => '00:00'   // Required by schema
                        );
                        continue;
                    }

                    // Parse opening hours (assuming format like "10:00 AM - 9:00 PM")
                    $time_parts = explode( ' - ', $time );

                    if ( count( $time_parts ) === 2 ) {
                        $opening_time = trim( $time_parts[0] );
                        $closing_time = trim( $time_parts[1] );

                        // Convert to 24-hour format for schema
                        $opening_time_24h = date( 'H:i', strtotime( $opening_time ) );
                        $closing_time_24h = date( 'H:i', strtotime( $closing_time ) );

                        $opening_hours_spec[] = array(
                            '@type' => 'OpeningHoursSpecification',
                            'dayOfWeek' => $day_name,
                            'opens' => $opening_time_24h,
                            'closes' => $closing_time_24h
                        );
                    }
                }
            }
        }
    }
    // 3. Try to parse from hoursDisplay as a last resort
    elseif ( ! empty( $hoursDisplay ) ) {
        // Common patterns for day ranges
        $day_patterns = array(
            'monday' => '/\b(mon|monday)\b/i',
            'tuesday' => '/\b(tue|tues|tuesday)\b/i',
            'wednesday' => '/\b(wed|wednesday)\b/i',
            'thursday' => '/\b(thu|thur|thurs|thursday)\b/i',
            'friday' => '/\b(fri|friday)\b/i',
            'saturday' => '/\b(sat|saturday)\b/i',
            'sunday' => '/\b(sun|sunday)\b/i'
        );

        // Pattern for time ranges like "10:00 AM - 9:00 PM"
        $hours_pattern = '/(\d{1,2}:\d{2}\s*(?:AM|PM))\s*[-–]\s*(\d{1,2}:\d{2}\s*(?:AM|PM))/i';

        // Pattern for day ranges like "Mon-Fri"
        $day_range_pattern = '/\b(mon|monday|tue|tues|tuesday|wed|wednesday|thu|thur|thurs|thursday|fri|friday|sat|saturday|sun|sunday)\s*[-–]\s*(mon|monday|tue|tues|tuesday|wed|wednesday|thu|thur|thurs|thursday|fri|friday|sat|saturday|sun|sunday)\b/i';

        // Split by semicolons if multiple day ranges
        $hours_parts = explode(';', $hoursDisplay);

        foreach ($hours_parts as $part) {
            $part = trim($part);
            if (empty($part)) continue;

            // Check for day range pattern
            if (preg_match($day_range_pattern, $part, $range_matches)) {
                $start_day_abbr = strtolower($range_matches[1]);
                $end_day_abbr = strtolower($range_matches[2]);

                // Map abbreviated days to full day keys
                $day_map = array(
                    'mon' => 'monday', 'monday' => 'monday',
                    'tue' => 'tuesday', 'tues' => 'tuesday', 'tuesday' => 'tuesday',
                    'wed' => 'wednesday', 'wednesday' => 'wednesday',
                    'thu' => 'thursday', 'thur' => 'thursday', 'thurs' => 'thursday', 'thursday' => 'thursday',
                    'fri' => 'friday', 'friday' => 'friday',
                    'sat' => 'saturday', 'saturday' => 'saturday',
                    'sun' => 'sunday', 'sunday' => 'sunday'
                );

                if (isset($day_map[$start_day_abbr]) && isset($day_map[$end_day_abbr])) {
                    $start_day = $day_map[$start_day_abbr];
                    $end_day = $day_map[$end_day_abbr];

                    // Extract hours from this part
                    $hours_found = false;
                    if (preg_match($hours_pattern, $part, $time_matches)) {
                        $opening_time = $time_matches[1];
                        $closing_time = $time_matches[2];
                        $hours_found = true;
                    }

                    if ($hours_found) {
                        // Convert to 24-hour format
                        $opening_time_24h = date('H:i', strtotime($opening_time));
                        $closing_time_24h = date('H:i', strtotime($closing_time));

                        // Get all days in the range
                        $days_in_range = array();
                        $day_order = array_keys($day_mapping);
                        $start_index = array_search($start_day, $day_order);
                        $end_index = array_search($end_day, $day_order);

                        // Handle wrap-around ranges (e.g., Sun-Wed)
                        if ($start_index > $end_index) {
                            $end_index += 7;
                        }

                        for ($i = $start_index; $i <= $end_index; $i++) {
                            $day_index = $i % 7;
                            $current_day = $day_order[$day_index];
                            $days_in_range[] = $day_mapping[$current_day];
                            $processed_days[$day_mapping[$current_day]] = true;
                        }

                        // Add specification for this range
                        $opening_hours_spec[] = array(
                            '@type' => 'OpeningHoursSpecification',
                            'dayOfWeek' => $days_in_range,
                            'opens' => $opening_time_24h,
                            'closes' => $closing_time_24h
                        );
                    }
                }
            }
            // Check for individual days
            else {
                foreach ($day_patterns as $day_key => $pattern) {
                    if (preg_match($pattern, $part)) {
                        $day_name = $day_mapping[$day_key];
                        $processed_days[$day_name] = true;

                        // Extract hours for this day
                        if (preg_match($hours_pattern, $part, $time_matches)) {
                            $opening_time = $time_matches[1];
                            $closing_time = $time_matches[2];

                            // Convert to 24-hour format
                            $opening_time_24h = date('H:i', strtotime($opening_time));
                            $closing_time_24h = date('H:i', strtotime($closing_time));

                            $opening_hours_spec[] = array(
                                '@type' => 'OpeningHoursSpecification',
                                'dayOfWeek' => $day_name,
                                'opens' => $opening_time_24h,
                                'closes' => $closing_time_24h
                            );
                        }
                        // Check if closed
                        elseif (preg_match('/\bclosed\b/i', $part)) {
                            $opening_hours_spec[] = array(
                                '@type' => 'OpeningHoursSpecification',
                                'dayOfWeek' => $day_name,
                                'validFrom' => date('Y-m-d'), // Current date
                                'validThrough' => date('Y-m-d', strtotime('+1 year')), // Valid for a year
                                'closes' => '00:00', // Required by schema
                                'opens' => '00:00'   // Required by schema
                            );
                        }
                    }
                }
            }
        }
    }

    // Add default hours for any days not specified (assume standard business hours)
    foreach ($day_mapping as $day_key => $day_name) {
        if (!isset($processed_days[$day_name])) {
            // For missing days, assume standard business hours (10 AM - 6 PM)
            // This ensures complete schema data for all days of the week
            $opening_hours_spec[] = array(
                '@type' => 'OpeningHoursSpecification',
                'dayOfWeek' => $day_name,
                'opens' => '10:00',
                'closes' => '18:00'
            );
        }
    }

    // Consolidate hours specifications for days with the same hours
    $consolidated_hours = array();
    $hours_by_pattern = array();

    foreach ($opening_hours_spec as $spec) {
        $days = is_array($spec['dayOfWeek']) ? $spec['dayOfWeek'] : array($spec['dayOfWeek']);
        $hours_pattern = isset($spec['opens']) && isset($spec['closes']) ?
            $spec['opens'] . '-' . $spec['closes'] : 'closed';

        foreach ($days as $day) {
            $hours_by_pattern[$hours_pattern][] = $day;
        }
    }

    // Create consolidated specifications
    foreach ($hours_by_pattern as $pattern => $days) {
        if ($pattern === 'closed' || $pattern === '00:00-00:00') {
            $consolidated_hours[] = array(
                '@type' => 'OpeningHoursSpecification',
                'dayOfWeek' => array_unique($days),
                'validFrom' => date('Y-m-d'),
                'validThrough' => date('Y-m-d', strtotime('+1 year')),
                'closes' => '00:00',
                'opens' => '00:00'
            );
        } else {
            list($opens, $closes) = explode('-', $pattern);
            $consolidated_hours[] = array(
                '@type' => 'OpeningHoursSpecification',
                'dayOfWeek' => array_unique($days),
                'opens' => $opens,
                'closes' => $closes
            );
        }
    }

    // Use consolidated hours if available, otherwise use the original specs
    $opening_hours_spec = !empty($consolidated_hours) ? $consolidated_hours : $opening_hours_spec;

    // Get images
    $images = get_post_meta( $post_id, 'images', true );
    $image_urls = array();

    if ( ! empty( $images ) && is_array( $images ) ) {
        foreach ( $images as $image ) {
            if ( ! empty( $image['url'] ) ) {
                $image_urls[] = $image['url'];
            }
        }
    } elseif ( has_post_thumbnail( $post_id ) ) {
        $image_urls[] = get_the_post_thumbnail_url( $post_id, 'large' );
    }

    // Get FAQ data
    $faq = get_post_meta( $post_id, 'faq', true );
    $faq_items = array();

    if ( ! empty( $faq ) && is_array( $faq ) ) {
        foreach ( $faq as $item ) {
            if ( ! empty( $item['question'] ) && ! empty( $item['answer'] ) ) {
                $faq_items[] = array(
                    '@type' => 'Question',
                    'name' => $item['question'],
                    'acceptedAnswer' => array(
                        '@type' => 'Answer',
                        'text' => $item['answer']
                    )
                );
            }
        }
    }

    // Build schema with improved type and core information for local business
    $schema = array(
        '@context' => 'https://schema.org',
        '@type' => 'HealthAndBeautyBusiness',
        'additionalType' => array(
            'https://schema.org/DaySpa',
            'https://schema.org/LocalBusiness'
        ),
        'name' => $name,
        'description' => $description,
        'url' => $url,
        'address' => array(
            '@type' => 'PostalAddress',
            'streetAddress' => $address,
            'addressLocality' => $city,
            'addressCountry' => $country
        ),
        // Add local business specific properties
        'slogan' => 'Luxury spa treatments in Barcelona',
        'paymentAccepted' => 'Cash, Credit Card',
        'currenciesAccepted' => 'EUR'
    );

    // Add postal code if available
    if (!empty($location['zip'])) {
        $schema['address']['postalCode'] = $location['zip'];
    }

    // Add state/region if available
    if (!empty($location['state'])) {
        $schema['address']['addressRegion'] = $location['state'];
    }

    // Add business-specific information
    $schema['isAccessibleForFree'] = false; // Spas typically charge for services

    // Add accessibility information
    $accessibility = get_post_meta( $post_id, 'accessibility', true );
    if (!empty($accessibility) && is_array($accessibility)) {
        // Store accessibility information in a more schema-compliant way

        // Add physical accessibility features as amenities
        if (!isset($schema['amenityFeature'])) {
            $schema['amenityFeature'] = array();
        }

        // Process physical accessibility features
        if (!empty($accessibility['wheelchair'])) {
            $schema['amenityFeature'][] = array(
                '@type' => 'LocationFeatureSpecification',
                'name' => 'Wheelchair Accessible',
                'value' => 'True'
            );
        }

        if (!empty($accessibility['parking'])) {
            $schema['amenityFeature'][] = array(
                '@type' => 'LocationFeatureSpecification',
                'name' => 'Accessible Parking',
                'value' => 'True'
            );
        }

        if (!empty($accessibility['elevator'])) {
            $schema['amenityFeature'][] = array(
                '@type' => 'LocationFeatureSpecification',
                'name' => 'Elevator Access',
                'value' => 'True'
            );
        }

        // Add accessibility advice as separate amenity features
        foreach ($accessibility as $key => $value) {
            if ($value && strpos($key, 'piece of advice') !== false) {
                $schema['amenityFeature'][] = array(
                    '@type' => 'LocationFeatureSpecification',
                    'name' => 'Visitor Information',
                    'value' => 'True',
                    'description' => $value
                );
            }
        }
    }

    // Add business category and type information
    $schema['@id'] = $url . '#spa';
    $schema['mainEntityOfPage'] = $url;

    // Add business status
    $schema['publicAccess'] = true;

    // Add hasMap with proper structured data
    if (!empty($location['coordinates']['latitude']) && !empty($location['coordinates']['longitude'])) {
        $schema['hasMap'] = array(
            '@type' => 'Map',
            'url' => 'https://www.google.com/maps?q=' . $location['coordinates']['latitude'] . ',' . $location['coordinates']['longitude']
        );
    }

    // Remove acceptsReservations as it's not valid for HealthAndBeautyBusiness
    // Instead, add a valid property to indicate reservation capability
    $schema['potentialAction'] = array(
        '@type' => 'ReserveAction',
        'target' => array(
            '@type' => 'EntryPoint',
            'urlTemplate' => $url . '#contact',
            'inLanguage' => 'en',
            'actionPlatform' => array(
                'http://schema.org/DesktopWebPlatform'
            )
        ),
        'result' => array(
            '@type' => 'Reservation',
            'name' => 'Spa treatment reservation'
        )
    );

    // Add local business hours
    if (!empty($opening_hours_spec)) {
        $schema['openingHoursSpecification'] = $opening_hours_spec;
    }

    // Add language information as additionalProperty since availableLanguage is not valid
    if (!isset($schema['additionalProperty'])) {
        $schema['additionalProperty'] = array();
    }

    

        // Add areaServed property for local SEO
        $schema['areaServed'] = $neighborhood;
    }

    // Get neighborhood terms and add them to schema as additionalProperty
    $neighborhood_terms = get_the_terms($post_id, 'spa_neighborhood');
    if (!empty($neighborhood_terms) && !is_wp_error($neighborhood_terms)) {
        if (!isset($schema['additionalProperty'])) {
            $schema['additionalProperty'] = array();
        }

        foreach ($neighborhood_terms as $term) {
            // Add neighborhood as additionalProperty
            $neighborhood_property = array(
                '@type' => 'PropertyValue',
                'name' => 'Located in',
                'value' => $term->name
            );

            // Add description if available
            if (!empty($term->description)) {
                $neighborhood_property['description'] = $term->description;
            }

            $schema['additionalProperty'][] = $neighborhood_property;
        }
    }

    // Add comprehensive contact information
    if ( ! empty( $phone ) ) {
        $schema['telephone'] = $phone;

        // Add structured telephone data for better machine readability
        // Format international number for better recognition
        $formatted_phone = $phone;
        if (strpos($phone, '+') === false) {
            // If no international prefix, assume Spain (+34)
            $formatted_phone = '+34 ' . preg_replace('/^0+/', '', $phone);
        }
        $schema['contactPoint'] = array(
            '@type' => 'ContactPoint',
            'telephone' => $formatted_phone,
            'contactType' => 'customer service',
            'areaServed' => 'ES',
            'availableLanguage' => array('Spanish', 'English')
        );
    }

    if ( ! empty( $email ) ) {
        $schema['email'] = $email;
    }

    if ( ! empty( $website ) ) {
        // Add website to sameAs array
        if (!isset($schema['sameAs'])) {
            $schema['sameAs'] = array();
        } else if (!is_array($schema['sameAs'])) {
            // Convert single value to array
            $schema['sameAs'] = array($schema['sameAs']);
        }

        // Add website to sameAs array if not already there
        if (!in_array($website, $schema['sameAs'])) {
            $schema['sameAs'][] = $website;
        }
    }

    // Add social media profiles
    $social_media = get_post_meta( $post_id, 'social_media', true );
    if (!empty($social_media) && is_array($social_media)) {
        if (!isset($schema['sameAs'])) {
            $schema['sameAs'] = array();
        } else if (!is_array($schema['sameAs'])) {
            // Convert single value to array
            $schema['sameAs'] = array($schema['sameAs']);
        }

        // Add each social media profile
        foreach ($social_media as $platform => $url) {
            if (!empty($url)) {
                $schema['sameAs'][] = $url;
            }
        }
    }

    // Add opening hours specification
    if ( ! empty( $opening_hours_spec ) ) {
        $schema['openingHoursSpecification'] = $opening_hours_spec;
    }

    // Add images
    if ( ! empty( $image_urls ) ) {
        $schema['image'] = $image_urls;
    }

    // Add services as offered services
    $services = get_post_meta( $post_id, 'services', true );
    if ( ! empty( $services ) && is_array( $services ) ) {
        // Create an array to hold all services
        $offered_services = array();

        foreach ( $services as $service_name ) {
            // Create a Service object for each service
            $service = array(
                '@type' => 'Service',
                'name' => $service_name,
                'provider' => array(
                    '@type' => 'HealthAndBeautyBusiness',
                    'name' => $name
                ),
                'serviceType' => 'Spa Service'
            );

            // Add service category based on common keywords
            $service_lower = strtolower($service_name);

            if (strpos($service_lower, 'massage') !== false) {
                $service['category'] = 'Massage Therapy';
            } elseif (strpos($service_lower, 'facial') !== false) {
                $service['category'] = 'Facial Treatment';
            } elseif (strpos($service_lower, 'body') !== false) {
                $service['category'] = 'Body Treatment';
            } elseif (strpos($service_lower, 'nail') !== false || strpos($service_lower, 'manicure') !== false || strpos($service_lower, 'pedicure') !== false) {
                $service['category'] = 'Nail Care';
            } elseif (strpos($service_lower, 'hair') !== false) {
                $service['category'] = 'Hair Care';
            } elseif (strpos($service_lower, 'wax') !== false || strpos($service_lower, 'depil') !== false) {
                $service['category'] = 'Hair Removal';
            } else {
                $service['category'] = 'Wellness Service';
            }

            $offered_services[] = $service;
        }

        if (!empty($offered_services)) {
            $schema['hasOfferCatalog'] = array(
                '@type' => 'OfferCatalog',
                'name' => 'Spa Services',
                'itemListElement' => $offered_services
            );
        }
    }

    // Add specialties as additional services with more detailed information
    $specialties = get_post_meta( $post_id, 'specialties', true );
    if ( ! empty( $specialties ) && is_array( $specialties ) ) {
        // Create or extend the offer catalog
        if (!isset($schema['hasOfferCatalog'])) {
            $schema['hasOfferCatalog'] = array(
                '@type' => 'OfferCatalog',
                'name' => 'Spa Services',
                'itemListElement' => array()
            );
        } elseif (!isset($schema['hasOfferCatalog']['itemListElement'])) {
            $schema['hasOfferCatalog']['itemListElement'] = array();
        }

        foreach ( $specialties as $specialty ) {
            // Create a Service object for each specialty
            $specialty_service = array(
                '@type' => 'Service',
                'name' => $specialty,
                'provider' => array(
                    '@type' => 'HealthAndBeautyBusiness',
                    'name' => $name
                ),
                'serviceType' => 'Spa Specialty',
                'serviceOutput' => 'Enhanced wellness and relaxation experience'
            );

            // Add to the offer catalog
            $schema['hasOfferCatalog']['itemListElement'][] = $specialty_service;
        }
    }

    // Add amenities as proper features
    $amenities = get_post_meta( $post_id, 'amenities', true );
    if ( ! empty( $amenities ) && is_array( $amenities ) ) {
        if (!isset($schema['amenityFeature'])) {
            $schema['amenityFeature'] = array();
        }

        foreach ( $amenities as $amenity ) {
            // Create a LocationFeatureSpecification for each amenity
            $feature = array(
                '@type' => 'LocationFeatureSpecification',
                'name' => $amenity,
                'value' => 'True'
            );

            // Add description based on common keywords instead of category
            $amenity_lower = strtolower($amenity);

            if (strpos($amenity_lower, 'pool') !== false || strpos($amenity_lower, 'jacuzzi') !== false || strpos($amenity_lower, 'tub') !== false) {
                $feature['description'] = 'Water facility: ' . $amenity;
            } elseif (strpos($amenity_lower, 'sauna') !== false || strpos($amenity_lower, 'steam') !== false) {
                $feature['description'] = 'Heat experience: ' . $amenity;
            } elseif (strpos($amenity_lower, 'locker') !== false || strpos($amenity_lower, 'changing') !== false || strpos($amenity_lower, 'shower') !== false) {
                $feature['description'] = 'Changing facility: ' . $amenity;
            } elseif (strpos($amenity_lower, 'relax') !== false || strpos($amenity_lower, 'lounge') !== false) {
                $feature['description'] = 'Relaxation area: ' . $amenity;
            } elseif (strpos($amenity_lower, 'gym') !== false || strpos($amenity_lower, 'fitness') !== false) {
                $feature['description'] = 'Fitness facility: ' . $amenity;
            } else {
                $feature['description'] = 'Spa amenity: ' . $amenity;
            }

            $schema['amenityFeature'][] = $feature;
        }
    }

    // Add enhanced geo coordinates and map information
    if ( ! empty( $location['coordinates']['latitude'] ) && ! empty( $location['coordinates']['longitude'] ) ) {
        // Add geo coordinates with proper typing
        $schema['geo'] = array(
            '@type' => 'GeoCoordinates',
            'latitude' => floatval($location['coordinates']['latitude']),
            'longitude' => floatval($location['coordinates']['longitude'])
        );

        // Add hasMap property with enhanced Google Maps URL
        $map_query = urlencode($name . ', ' . $address . ', ' . $city);
        $schema['hasMap'] = array(
            '@type' => 'Map',
            'url' => sprintf(
                'https://www.google.com/maps/place/%s/@%s,%s,15z',
                $map_query,
                $location['coordinates']['latitude'],
                $location['coordinates']['longitude']
            )
        );

        // Add Google Maps directions URL
        $directions_url = sprintf(
            'https://www.google.com/maps/dir/?api=1&destination=%s,%s&destination_place_id=%s',
            $location['coordinates']['latitude'],
            $location['coordinates']['longitude'],
            urlencode($name)
        );

        // Add directions URL as an additional property
        if (!isset($schema['additionalProperty'])) {
            $schema['additionalProperty'] = array();
        }

        // Service area is already defined in serviceArea property with GeoCircle
    }

    // Add nearby attractions if available
    $nearby_attractions = get_post_meta( $post_id, 'nearby_attractions', true );
    if ( ! empty( $nearby_attractions ) && is_array( $nearby_attractions ) ) {
        // Keep the existing amenityFeature implementation for backward compatibility
        if (!isset($schema['amenityFeature'])) {
            $schema['amenityFeature'] = array();
        }

        // Use only LocationFeatureSpecification for nearby attractions to avoid redundancy
            }
        }
    }

    // Add transportation information if available
    $transportation = get_post_meta( $post_id, 'transportation', true );
    if ( ! empty( $transportation ) && is_array( $transportation ) ) {
        // Set publicAccess to true if public transportation is available
        $schema['publicAccess'] = true;

        // Add transportation details as amenityFeature
        if (!isset($schema['amenityFeature'])) {
            $schema['amenityFeature'] = array();
        }

        if ( ! empty( $transportation['metro'] ) ) {
            $schema['amenityFeature'][] = array(
                '@type' => 'LocationFeatureSpecification',
                'name' => 'Metro Access',
                'value' => 'True',
                'description' => 'Metro: ' . $transportation['metro']
            );
        }

        if ( ! empty( $transportation['bus'] ) ) {
            $schema['amenityFeature'][] = array(
                '@type' => 'LocationFeatureSpecification',
                'name' => 'Bus Access',
                'value' => 'True',
                'description' => 'Bus: ' . $transportation['bus']
            );
        }

        if ( ! empty( $transportation['train'] ) ) {
            $schema['amenityFeature'][] = array(
                '@type' => 'LocationFeatureSpecification',
                'name' => 'Train Access',
                'value' => 'True',
                'description' => 'Train: ' . $transportation['train']
            );
        }

        // Add public transit information to the description
        $public_transit = array();
        if ( ! empty( $transportation['metro'] ) ) {
            $public_transit[] = 'Metro: ' . $transportation['metro'];
        }
        if ( ! empty( $transportation['bus'] ) ) {
            $public_transit[] = 'Bus: ' . $transportation['bus'];
        }
        if ( ! empty( $transportation['train'] ) ) {
            $public_transit[] = 'Train: ' . $transportation['train'];
        }

        if ( ! empty( $public_transit ) ) {
            if (!isset($schema['additionalProperty'])) {
                $schema['additionalProperty'] = array();
            }

            $schema['additionalProperty'][] = array(
                '@type' => 'PropertyValue',
                'name' => 'Public Transportation',
                'value' => implode('. ', $public_transit)
            );
        }
    }

    // Add "near me" keywords to the description for local SEO
    if ( ! empty( $neighborhood ) && strpos( $description, 'near me' ) === false ) {
        $schema['alternateName'] = $name . ' in ' . $neighborhood . ' Barcelona';

        // Add keywords for local SEO
        $schema['keywords'] = $name . ', spa in ' . $neighborhood . ', Barcelona spa, spa near me in ' . $neighborhood . ', ' . $neighborhood . ' spa';
    }

    // Add popular features as additional services
    $popular_features = get_post_meta( $post_id, 'popular', true );
    if ( ! empty( $popular_features ) && is_array( $popular_features ) ) {
        // Create or extend the offer catalog
        if (!isset($schema['hasOfferCatalog'])) {
            $schema['hasOfferCatalog'] = array(
                '@type' => 'OfferCatalog',
                'name' => 'Spa Services',
                'itemListElement' => array()
            );
        } elseif (!isset($schema['hasOfferCatalog']['itemListElement'])) {
            $schema['hasOfferCatalog']['itemListElement'] = array();
        }

        foreach ( $popular_features as $feature ) {
            // Create a Service object for each popular feature
            $feature_service = array(
                '@type' => 'Service',
                'name' => $feature,
                'provider' => array(
                    '@type' => 'HealthAndBeautyBusiness',
                    'name' => $name
                ),
                'serviceType' => 'Popular Feature',
                'serviceOutput' => 'Enhanced spa experience'
            );

            // Add to the offer catalog
            $schema['hasOfferCatalog']['itemListElement'][] = $feature_service;
        }

        // Popular features are already in hasOfferCatalog, no need for redundant amenityFeature entries
    }

    // Add enhanced aggregate rating if available
    if ( $rating && $review_count ) {
        $schema['aggregateRating'] = array(
            '@type' => 'AggregateRating',
            'ratingValue' => floatval($rating),
            'reviewCount' => intval($review_count),
            'bestRating' => 5,
            'worstRating' => 1,
            'ratingExplanation' => 'Weighted average of ' . intval($review_count) . ' reviews from multiple sources'
        );

        // Add rating count as a separate property for better visibility
        if (!isset($schema['additionalProperty'])) {
            $schema['additionalProperty'] = array();
        }

        $schema['additionalProperty'][] = array(
            '@type' => 'PropertyValue',
            'name' => 'Total Reviews',
            'value' => intval($review_count)
        );
    }

    // Add review schema items with enhanced structure
    if ( ! empty( $review_schema_items ) ) {
        // Add reviews directly to the schema
        $schema['review'] = $review_schema_items;

        // Also create a separate WebPage schema with review content for better visibility in search results
        if (count($review_schema_items) >= 3) {  // Only create if we have enough reviews
            $review_page_schema = array(
                '@type' => 'WebPage',
                'speakable' => array(
                    '@type' => 'SpeakableSpecification',
                    'cssSelector' => ['.reviews-section']
                ),
                'mainEntity' => array(
                    '@type' => 'HealthAndBeautyBusiness',
                    'name' => $name,
                    'url' => $url,
                    'review' => $review_schema_items
                ),
                'url' => $url . '#reviews',
                'name' => $name . ' - Reviews',
                'description' => 'Customer reviews and ratings for ' . $name . ' in Barcelona',
                'about' => array(
                    '@type' => 'HealthAndBeautyBusiness',
                    'name' => $name
                )
            );

            // Add this as a separate script tag later
            $review_page_json = wp_json_encode($review_page_schema);
        }
    }

    // Add pros and cons if available
    $pros_cons = get_post_meta( $post_id, 'pros_cons', true );
    if ( ! empty( $pros_cons ) && is_array( $pros_cons ) ) {
        if ( ! empty( $pros_cons['pros'] ) && is_array( $pros_cons['pros'] ) ) {
            if ( ! isset( $schema['additionalProperty'] ) ) {
                $schema['additionalProperty'] = array();
            }

            $schema['additionalProperty'][] = array(
                '@type' => 'PropertyValue',
                'name' => 'Pros',
                'value' => implode(', ', $pros_cons['pros'])
            );
        }

        if ( ! empty( $pros_cons['cons'] ) && is_array( $pros_cons['cons'] ) ) {
            if ( ! isset( $schema['additionalProperty'] ) ) {
                $schema['additionalProperty'] = array();
            }

            $schema['additionalProperty'][] = array(
                '@type' => 'PropertyValue',
                'name' => 'Cons',
                'value' => implode(', ', $pros_cons['cons'])
            );
        }
    }

    // Add services with specific treatments and their prices using makesOffer with enhanced structure
    $packages = get_post_meta( $post_id, 'packages', true );
    if ( ! empty( $packages ) && is_array( $packages ) ) {
        $offers = array();
        $package_categories = array();

        foreach ( $packages as $package ) {
            if ( is_array( $package ) && ! empty( $package['name'] ) ) {
                // Create a Service object for each package with enhanced details
                $service = array(
                    '@type' => 'Service',
                    'name' => $package['name'],
                    'description' => ! empty( $package['description'] ) ? $package['description'] : '',
                    'provider' => array(
                        '@type' => 'HealthAndBeautyBusiness',
                        'name' => $name,
                        'url' => $url
                    )
                );

                // Add service category based on name keywords
                $package_name_lower = strtolower($package['name']);

                if (strpos($package_name_lower, 'massage') !== false) {
                    $service['category'] = 'Massage Therapy';
                    $package_categories['Massage Therapy'] = true;
                } elseif (strpos($package_name_lower, 'facial') !== false) {
                    $service['category'] = 'Facial Treatment';
                    $package_categories['Facial Treatment'] = true;
                } elseif (strpos($package_name_lower, 'body') !== false) {
                    $service['category'] = 'Body Treatment';
                    $package_categories['Body Treatment'] = true;
                } elseif (strpos($package_name_lower, 'couple') !== false || strpos($package_name_lower, 'couples') !== false) {
                    $service['category'] = 'Couples Treatment';
                    $package_categories['Couples Treatment'] = true;
                } elseif (strpos($package_name_lower, 'package') !== false || strpos($package_name_lower, 'ritual') !== false) {
                    $service['category'] = 'Spa Package';
                    $package_categories['Spa Package'] = true;
                } else {
                    $service['category'] = 'Wellness Treatment';
                    $package_categories['Wellness Treatment'] = true;
                }

                // Add audience if specified
                if (!empty($package['audience'])) {
                    $service['audience'] = array(
                        '@type' => 'Audience',
                        'audienceType' => $package['audience']
                    );
                }

                // Add service area
                $service['areaServed'] = array(
                    '@type' => 'City',
                    'name' => 'Barcelona'
                );

                // Add service URL if available
                if (!empty($package['url'])) {
                    $service['url'] = $package['url'];
                } else {
                    $service['url'] = $url . '#packages';
                }

                // Create an enhanced Offer object for the service
                $offer = array(
                    '@type' => 'Offer',
                    'itemOffered' => $service,
                    'seller' => array(
                        '@type' => 'HealthAndBeautyBusiness',
                        'name' => $name,
                        'url' => $url
                    )
                );

                // Add price with enhanced handling
                if (!empty($package['price'])) {
                    // Check if price is already a number (new format)
                    if (is_numeric($package['price'])) {
                        $offer['price'] = floatval($package['price']);
                        $offer['priceCurrency'] = !empty($package['currency']) ? $package['currency'] : 'EUR';
                    }
                    // Handle string price (old format)
                    else if ($package['price'] !== 'Varies') {
                        // Extract numeric value and currency
                        preg_match('/([A-Z]{3})\s*(\d+(?:\.\d+)?)|\$?(\d+(?:\.\d+)?)/', $package['price'], $matches);

                        if (!empty($matches[2])) {
                            // Format: "EUR 275" or similar
                            $offer['price'] = floatval($matches[2]);
                            $offer['priceCurrency'] = $matches[1];
                        } elseif (!empty($matches[3])) {
                            // Format: "$275" or "275"
                            $offer['price'] = floatval($matches[3]);
                            $offer['priceCurrency'] = !empty($matches[1]) ? $matches[1] : 'EUR'; // Default to EUR if no currency specified
                        }
                    } else {
                        // Handle "Varies" price with a price range if min/max are available
                        if (!empty($package['priceMin']) && !empty($package['priceMax'])) {
                            $offer['priceSpecification'] = array(
                                '@type' => 'PriceSpecification',
                                'minPrice' => floatval($package['priceMin']),
                                'maxPrice' => floatval($package['priceMax']),
                                'priceCurrency' => !empty($package['currency']) ? $package['currency'] : 'EUR'
                            );
                        } else {
                            // Just note that price varies
                            $offer['priceSpecification'] = array(
                                '@type' => 'PriceSpecification',
                                'description' => 'Price varies based on options selected'
                            );
                        }
                    }
                }

                // Add price validity period if available
                if (!empty($package['priceValidUntil'])) {
                    $offer['priceValidUntil'] = $package['priceValidUntil'];
                } else {
                    // Default to 1 year from now
                    $offer['priceValidUntil'] = date('Y-m-d', strtotime('+1 year'));
                }

                // Add availability with enhanced status
                if (!empty($package['availability'])) {
                    switch (strtolower($package['availability'])) {
                        case 'in stock':
                        case 'available':
                            $offer['availability'] = 'https://schema.org/InStock';
                            break;
                        case 'out of stock':
                        case 'unavailable':
                            $offer['availability'] = 'https://schema.org/OutOfStock';
                            break;
                        case 'pre-order':
                            $offer['availability'] = 'https://schema.org/PreOrder';
                            break;
                        default:
                            $offer['availability'] = 'https://schema.org/InStock'; // Default
                    }
                } else {
                    $offer['availability'] = 'https://schema.org/InStock'; // Default
                }

                // Add eligibility requirements if available
                if (!empty($package['eligibleCustomerType'])) {
                    $offer['eligibleCustomerType'] = $package['eligibleCustomerType'];
                }

                // Add warranty information if available
                if (!empty($package['warranty'])) {
                    $offer['warranty'] = array(
                        '@type' => 'WarrantyPromise',
                        'description' => $package['warranty']
                    );
                }

                // Add duration with enhanced ISO 8601 formatting
                if (!empty($package['durationMinutes']) && is_numeric($package['durationMinutes'])) {
                    // Use ISO 8601 duration format
                    $hours = floor(intval($package['durationMinutes']) / 60);
                    $minutes = intval($package['durationMinutes']) % 60;
                    $duration = 'PT';
                    if ($hours > 0) {
                        $duration .= $hours . 'H';
                    }
                    if ($minutes > 0) {
                        $duration .= $minutes . 'M';
                    }
                    $service['timeRequired'] = $duration;

                    // Also add as a human-readable description
                    $time_description = '';
                    if ($hours > 0) {
                        $time_description .= $hours . ' hour' . ($hours > 1 ? 's' : '');
                    }
                    if ($minutes > 0) {
                        if ($hours > 0) {
                            $time_description .= ' and ';
                        }
                        $time_description .= $minutes . ' minute' . ($minutes > 1 ? 's' : '');
                    }

                    if (!empty($time_description)) {
                        if (!isset($service['additionalProperty'])) {
                            $service['additionalProperty'] = array();
                        }

                        $service['additionalProperty'][] = array(
                            '@type' => 'PropertyValue',
                            'name' => 'Duration',
                            'value' => $time_description
                        );
                    }
                } elseif (!empty($package['duration'])) {
                    // Try to parse duration from string format with enhanced pattern matching
                    if (preg_match('/(\d+)\s*(?:hour|hr)s?\s*(?:and)?\s*(\d+)?\s*(?:min|minute)s?/i', $package['duration'], $matches)) {
                        // Format: "2 hours and 30 minutes" or "2 hours 30 minutes"
                        $hours = (int)$matches[1];
                        $minutes = !empty($matches[2]) ? (int)$matches[2] : 0;
                        $service['timeRequired'] = 'PT' . $hours . 'H' . ($minutes > 0 ? $minutes . 'M' : '');
                    } elseif (preg_match('/(\d+)\s*(?:hour|hr)s?/i', $package['duration'], $matches)) {
                        // Format: "2 hours"
                        $hours = (int)$matches[1];
                        $service['timeRequired'] = 'PT' . $hours . 'H';
                    } elseif (preg_match('/(\d+)\s*(?:min|minute)s?/i', $package['duration'], $matches)) {
                        // Format: "30 minutes"
                        $minutes = (int)$matches[1];
                        $service['timeRequired'] = 'PT' . $minutes . 'M';
                    } elseif (preg_match('/(\d+)(?::(\d+))?/i', $package['duration'], $matches)) {
                        // Format: "2:30" or "2"
                        $hours = (int)$matches[1];
                        $minutes = !empty($matches[2]) ? (int)$matches[2] : 0;
                        $service['timeRequired'] = 'PT' . $hours . 'H' . ($minutes > 0 ? $minutes . 'M' : '');
                    }

                    // Also add the original duration string as a property
                    if (!isset($service['additionalProperty'])) {
                        $service['additionalProperty'] = array();
                    }

                    $service['additionalProperty'][] = array(
                        '@type' => 'PropertyValue',
                        'name' => 'Duration',
                        'value' => $package['duration']
                    );
                }

                // Add included items if available
                if (!empty($package['includes']) && is_array($package['includes'])) {
                    if (!isset($service['additionalProperty'])) {
                        $service['additionalProperty'] = array();
                    }

                    $service['additionalProperty'][] = array(
                        '@type' => 'PropertyValue',
                        'name' => 'Includes',
                        'value' => implode(', ', $package['includes'])
                    );
                }

                // Add benefits if available
                if (!empty($package['benefits']) && is_array($package['benefits'])) {
                    if (!isset($service['additionalProperty'])) {
                        $service['additionalProperty'] = array();
                    }

                    $service['additionalProperty'][] = array(
                        '@type' => 'PropertyValue',
                        'name' => 'Benefits',
                        'value' => implode(', ', $package['benefits'])
                    );
                }

                $offers[] = $offer;
            }
        }

        if (!empty($offers)) {
            $schema['makesOffer'] = $offers;

            // Add package categories as business types for better visibility
            if (!empty($package_categories)) {
                if (!isset($schema['additionalType'])) {
                    $schema['additionalType'] = array();
                } else if (!is_array($schema['additionalType'])) {
                    // Convert single value to array
                    $schema['additionalType'] = array($schema['additionalType']);
                }

                foreach (array_keys($package_categories) as $category) {
                    $schema['additionalType'][] = 'https://schema.org/' . str_replace(' ', '', $category) . 'Provider';
                }
            }
        }
    }

    // Validate schema before output
    $schema = spa_validate_schema($schema);

    // Prepare the output with all schema types
    $output = '<script type="application/ld+json">' . wp_json_encode( $schema ) . '</script>';

    // Add FAQ items if available
    if ( ! empty( $faq_items ) ) {
        // Create a separate FAQPage schema
        $faq_schema = array(
            '@type' => 'FAQPage',
            'mainEntity' => $faq_items
        );

        // Validate FAQ schema
        $faq_schema = spa_validate_schema($faq_schema);

        // Add FAQ schema to output
        $output .= '<script type="application/ld+json">' . wp_json_encode( $faq_schema ) . '</script>';
    }

    // Add ReviewWebPage schema if available
    if ( isset( $review_page_schema ) ) {
        // Validate review page schema
        $review_page_schema = spa_validate_schema($review_page_schema);
        $review_page_json = wp_json_encode($review_page_schema);
        $output .= '<script type="application/ld+json">' . $review_page_json . '</script>';
    }

    // Add BreadcrumbList schema for better navigation structure
    $breadcrumb_schema = array(
        '@type' => 'BreadcrumbList',
        'itemListElement' => array(
            array(
                '@type' => 'ListItem',
                'position' => 1,
                'name' => 'Home',
                'item' => home_url()
            ),
            array(
                '@type' => 'ListItem',
                'position' => 2,
                'name' => 'Spas',
                'item' => get_post_type_archive_link('spa')
            ),
            array(
                '@type' => 'ListItem',
                'position' => 3,
                'name' => $name,
                'item' => $url
            )
        )
    );

    // Add neighborhood to breadcrumb if available
    if (!empty($neighborhood)) {
        // Shift the spa position to 4
        $breadcrumb_schema['itemListElement'][2]['position'] = 3;
        $breadcrumb_schema['itemListElement'][3]['position'] = 4;

        // Insert neighborhood at position 3
        array_splice($breadcrumb_schema['itemListElement'], 2, 0, array(
            array(
                '@type' => 'ListItem',
                'position' => 2,
                'name' => $neighborhood,
                'item' => add_query_arg(array('neighborhood' => sanitize_title($neighborhood)), get_post_type_archive_link('spa'))
            )
        ));
    }

    // Validate breadcrumb schema
    $breadcrumb_schema = spa_validate_schema($breadcrumb_schema);

    // Add breadcrumb schema to output
    $output .= '<script type="application/ld+json">' . wp_json_encode( $breadcrumb_schema ) . '</script>';

    // Add schema.org testing comment for developers
    $output .= '<!-- Schema.org markup validated and generated by SpasinBarcelona. Test using:
    - Google Rich Results Test: https://search.google.com/test/rich-results
    - Schema.org Validator: https://validator.schema.org/
    - Google Structured Data Testing Tool (Legacy): https://search.google.com/structured-data/testing-tool
    -->';

    return $output;
}

/**
 * Validate schema.org markup to ensure it meets requirements
 *
 * @param array $schema The schema to validate
 * @return array The validated schema
 */
function spa_validate_schema($schema) {
    // Skip validation if schema is not an array
    if (!is_array($schema)) {
        return $schema;
    }

    // Ensure required properties for all schema types
    if (isset($schema['@type'])) {
        // Validate common properties for all types
        $schema = spa_validate_common_properties($schema);

        // Validate specific schema types
        switch ($schema['@type']) {
            case 'HealthAndBeautyBusiness':
            case 'LocalBusiness':
            case 'DaySpa':
                $schema = spa_validate_local_business($schema);
                break;

            case 'FAQPage':
                $schema = spa_validate_faq_page($schema);
                break;

            case 'WebPage':
                // If this is a review page (has reviews in mainEntity), validate it as a review page
                if (isset($schema['mainEntity']) && isset($schema['mainEntity']['review'])) {
                    $schema = spa_validate_review_page($schema);
                }
                break;

            case 'BreadcrumbList':
                $schema = spa_validate_breadcrumb_list($schema);
                break;
        }
    }

    // Recursively validate nested objects
    foreach ($schema as $key => $value) {
        if (is_array($value)) {
            if (isset($value['@type'])) {
                // This is a nested schema object
                $schema[$key] = spa_validate_schema($value);
            } else if (is_numeric(key($value)) || empty($value)) {
                // This is an array of items
                foreach ($value as $index => $item) {
                    if (is_array($item) && isset($item['@type'])) {
                        // Validate each schema object in the array
                        $schema[$key][$index] = spa_validate_schema($item);
                    }
                }
            }
        }
    }

    return $schema;
}

/**
 * Validate common properties for all schema types
 *
 * @param array $schema The schema to validate
 * @return array The validated schema
 */
function spa_validate_common_properties($schema) {
    // Ensure @context is present
    if (!isset($schema['@context'])) {
        $schema['@context'] = 'https://schema.org';
    }

    // Ensure all URLs are valid
    foreach ($schema as $key => $value) {
        if (in_array($key, array('url', 'image', 'logo', 'sameAs')) && !empty($value)) {
            if (is_array($value)) {
                foreach ($value as $index => $url) {
                    if (!filter_var($url, FILTER_VALIDATE_URL)) {
                        // Remove invalid URLs
                        unset($schema[$key][$index]);
                    }
                }
                // Re-index array if items were removed
                if (is_array($schema[$key])) {
                    $schema[$key] = array_values($schema[$key]);
                }
            } else if (!filter_var($value, FILTER_VALIDATE_URL)) {
                // Remove invalid URL
                unset($schema[$key]);
            }
        }
    }

    // Ensure numeric values are properly formatted
    foreach ($schema as $key => $value) {
        if (in_array($key, array('ratingValue', 'reviewCount', 'price', 'latitude', 'longitude'))) {
            if (is_numeric($value)) {
                $schema[$key] = floatval($value);
            }
        }
    }

    return $schema;
}

/**
 * Validate LocalBusiness schema
 *
 * @param array $schema The schema to validate
 * @return array The validated schema
 */
function spa_validate_local_business($schema) {
    // Required properties for LocalBusiness
    $required_props = array('name', 'address');

    foreach ($required_props as $prop) {
        if (!isset($schema[$prop]) || empty($schema[$prop])) {
            // Add default values for missing required properties
            switch ($prop) {
                case 'name':
                    $schema[$prop] = 'Spa in Barcelona';
                    break;
                case 'address':
                    $schema[$prop] = array(
                        '@type' => 'PostalAddress',
                        'addressLocality' => 'Barcelona',
                        'addressCountry' => 'Spain'
                    );
                    break;
            }
        }
    }

    // Validate address
    if (isset($schema['address']) && is_array($schema['address'])) {
        if (!isset($schema['address']['@type'])) {
            $schema['address']['@type'] = 'PostalAddress';
        }
    }

    // Validate geo coordinates
    if (isset($schema['geo']) && is_array($schema['geo'])) {
        if (!isset($schema['geo']['@type'])) {
            $schema['geo']['@type'] = 'GeoCoordinates';
        }

        // Ensure latitude and longitude are numeric
        if (isset($schema['geo']['latitude'])) {
            $schema['geo']['latitude'] = floatval($schema['geo']['latitude']);
        }

        if (isset($schema['geo']['longitude'])) {
            $schema['geo']['longitude'] = floatval($schema['geo']['longitude']);
        }
    }

    return $schema;
}

/**
 * Validate FAQPage schema
 *
 * @param array $schema The schema to validate
 * @return array The validated schema
 */
function spa_validate_faq_page($schema) {
    // Ensure mainEntity is present
    if (!isset($schema['mainEntity']) || !is_array($schema['mainEntity'])) {
        $schema['mainEntity'] = array();
    }

    // Validate each question
    foreach ($schema['mainEntity'] as $index => $question) {
        if (!isset($question['@type']) || $question['@type'] !== 'Question') {
            $schema['mainEntity'][$index]['@type'] = 'Question';
        }

        if (!isset($question['name']) || empty($question['name'])) {
            // Remove questions without a name
            unset($schema['mainEntity'][$index]);
            continue;
        }

        // Ensure acceptedAnswer is present and valid
        if (!isset($question['acceptedAnswer']) || !is_array($question['acceptedAnswer'])) {
            $schema['mainEntity'][$index]['acceptedAnswer'] = array(
                '@type' => 'Answer',
                'text' => 'Please contact us for more information.'
            );
        } else if (!isset($question['acceptedAnswer']['@type'])) {
            $schema['mainEntity'][$index]['acceptedAnswer']['@type'] = 'Answer';
        }

        // Ensure answer text is present
        if (!isset($question['acceptedAnswer']['text']) || empty($question['acceptedAnswer']['text'])) {
            $schema['mainEntity'][$index]['acceptedAnswer']['text'] = 'Please contact us for more information.';
        }
    }

    // Re-index array if items were removed
    $schema['mainEntity'] = array_values($schema['mainEntity']);

    return $schema;
}

/**
 * Validate WebPage schema with review content
 *
 * @param array $schema The schema to validate
 * @return array The validated schema
 */
function spa_validate_review_page($schema) {
    // Ensure mainEntity is present
    if (!isset($schema['mainEntity']) || !is_array($schema['mainEntity'])) {
        return $schema; // Cannot fix without a main entity
    }

    // Ensure mainEntity has required properties
    if (!isset($schema['mainEntity']['@type'])) {
        $schema['mainEntity']['@type'] = 'HealthAndBeautyBusiness';
    }

    if (!isset($schema['mainEntity']['name']) || empty($schema['mainEntity']['name'])) {
        $schema['mainEntity']['name'] = 'Spa in Barcelona';
    }

    // Ensure about property is present
    if (!isset($schema['about']) || !is_array($schema['about'])) {
        if (isset($schema['mainEntity']['name'])) {
            $schema['about'] = array(
                '@type' => 'HealthAndBeautyBusiness',
                'name' => $schema['mainEntity']['name']
            );
        }
    }

    // Ensure reviews are properly formatted
    if (isset($schema['mainEntity']['review'])) {
        foreach ($schema['mainEntity']['review'] as $index => $review) {
            if (!isset($review['@type']) || $review['@type'] !== 'Review') {
                $schema['mainEntity']['review'][$index]['@type'] = 'Review';
            }

            // Ensure author is present
            if (!isset($review['author']) || !is_array($review['author'])) {
                $schema['mainEntity']['review'][$index]['author'] = array(
                    '@type' => 'Person',
                    'name' => 'Customer'
                );
            } else if (!isset($review['author']['@type'])) {
                $schema['mainEntity']['review'][$index]['author']['@type'] = 'Person';
            }

            // Ensure reviewRating is present
            if (!isset($review['reviewRating']) || !is_array($review['reviewRating'])) {
                $schema['mainEntity']['review'][$index]['reviewRating'] = array(
                    '@type' => 'Rating',
                    'ratingValue' => 5,
                    'bestRating' => 5,
                    'worstRating' => 1
                );
            } else if (!isset($review['reviewRating']['@type'])) {
                $schema['mainEntity']['review'][$index]['reviewRating']['@type'] = 'Rating';
            }
        }
    }

    return $schema;
}

/**
 * Validate BreadcrumbList schema
 *
 * @param array $schema The schema to validate
 * @return array The validated schema
 */
function spa_validate_breadcrumb_list($schema) {
    // Ensure itemListElement is present
    if (!isset($schema['itemListElement']) || !is_array($schema['itemListElement'])) {
        $schema['itemListElement'] = array(
            array(
                '@type' => 'ListItem',
                'position' => 1,
                'name' => 'Home',
                'item' => home_url()
            )
        );
    }

    // Validate each list item
    foreach ($schema['itemListElement'] as $index => $item) {
        if (!isset($item['@type']) || $item['@type'] !== 'ListItem') {
            $schema['itemListElement'][$index]['@type'] = 'ListItem';
        }

        // Ensure position is present and numeric
        if (!isset($item['position']) || !is_numeric($item['position'])) {
            $schema['itemListElement'][$index]['position'] = $index + 1;
        } else {
            $schema['itemListElement'][$index]['position'] = intval($item['position']);
        }

        // Ensure name is present
        if (!isset($item['name']) || empty($item['name'])) {
            $schema['itemListElement'][$index]['name'] = 'Item ' . ($index + 1);
        }

        // Ensure item URL is present and valid
        if (!isset($item['item']) || empty($item['item']) || !filter_var($item['item'], FILTER_VALIDATE_URL)) {
            $schema['itemListElement'][$index]['item'] = home_url();
        }
    }

    return $schema;
}