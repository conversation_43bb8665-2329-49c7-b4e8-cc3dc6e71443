/**
 * Enhanced Page Title styles for Spas in Barcelona theme
 */

/* Page Title Card Container */
.page-header {
    text-align: center;
    margin-bottom: 50px;
    padding: 60px 30px;
    background-color: var(--light-color);
    border-radius: 15px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(210, 180, 140, 0.15);
    animation: fadeIn 0.8s ease-out forwards;
}

/* Add a subtle pattern overlay to the header */
.page-header:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('data:image/svg+xml;utf8,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><path d="M0 30 L30 0 L60 30 L30 60 Z" fill="none" stroke="rgba(104, 160, 160, 0.07)" stroke-width="1"/></svg>');
    opacity: 0.7;
    pointer-events: none;
}

/* Add a decorative accent to the top of the card */
.page-header:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(to right, var(--primary-color), var(--secondary-color), var(--primary-color));
    opacity: 0.8;
}

/* Page Title Styling */
.page-title {
    font-size: 2.8rem;
    margin-bottom: 20px;
    color: var(--dark-color);
    letter-spacing: 0.03em;
    position: relative;
    display: inline-block;
    font-weight: 700;
    line-height: 1.2;
    padding: 0 15px;
}

/* Style the page title */
.page-title {
    position: relative;
    z-index: 1;
    color: var(--dark-color);
    display: inline-block;
}

/* No decorative elements around the title for cleaner look */
.page-title:before {
    display: none;
}

/* Add a subtle underline effect */
.page-title:after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: var(--secondary-color);
    opacity: 0.3;
    display: block;
}

/* Archive Description */
.archive-description {
    max-width: 800px;
    margin: 25px auto 0;
    color: #777;
    font-size: 1.1rem;
    line-height: 1.8;
    letter-spacing: 0.01em;
    position: relative;
    z-index: 1;
}

/* No hover animation for page header */
.page-header:hover {
    /* No transform or box-shadow changes on hover */
}

/* Responsive Styles */
@media (max-width: 768px) {
    .page-header {
        padding: 40px 20px;
        margin-bottom: 30px;
    }

    .page-title {
        font-size: 2.2rem;
    }

    .archive-description {
        font-size: 1rem;
    }
}

@media (max-width: 576px) {
    .page-header {
        padding: 30px 15px;
        margin-bottom: 25px;
    }

    .page-title {
        font-size: 1.8rem;
    }

    .page-title-wrapper:after {
        width: 60px;
    }
}

/* Single Spa Page Title Enhancements */
.spa-single .page-header {
    background-color: var(--light-color);
}

/* Entry Title (for regular pages and posts) */
.entry-title {
    font-size: 2.5rem;
    margin-bottom: 20px;
    color: var(--dark-color);
    letter-spacing: 0.03em;
    position: relative;
    font-weight: 700;
    line-height: 1.2;
    padding-bottom: 15px;
}

.entry-title:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 80px;
    height: 2px;
    background-color: var(--secondary-color);
    opacity: 0.5;
}

/* Wrap title text in span for special styling */
.page-title-wrapper {
    position: relative;
    display: inline-block;
    padding: 0 10px;
    margin: 0 auto 20px;
}

/* No decorative quotes for cleaner look */
.page-title-wrapper:before,
.page-title-wrapper:after {
    display: none;
}

/* Simple decorative line below the title */
.page-title-wrapper:after {
    content: '';
    display: block;
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 2px;
    background-color: var(--secondary-color);
    opacity: 0.5;
}

/* No animation for the title wrapper */
.page-title-wrapper {
    /* No animation */
}
