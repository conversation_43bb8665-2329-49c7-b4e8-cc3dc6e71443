/**
 * Pros & Cons section styles for Spas in Barcelona theme
 */

/* Section Intro */
.spa-section-intro {
    margin-bottom: 25px;
    color: #666;
    line-height: 1.6;
    font-size: 17px;
    font-family: var(--font-primary);
    max-width: 100%;
    text-align: left;
}

/* Pros & Cons Container */
.spa-pros-cons-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-top: 20px;
}

/* Pros & Cons Columns */
.spa-pros-column,
.spa-cons-column {
    display: flex;
    flex-direction: column;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    border: 1px solid rgba(210, 180, 140, 0.2);
    position: relative; /* Required for shine effect */
}

.spa-pros-column:hover,
.spa-cons-column:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* Column Headers */
.spa-pros-header,
.spa-cons-header {
    display: flex;
    align-items: center;
    padding: 25px;
    color: #fff;
    font-size: 1.5rem;
    font-weight: 600;
    font-family: var(--font-secondary);
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
}

.spa-pros-header {
    background: linear-gradient(135deg, #8fb9aa, #68a0a0); /* Gradient green */
}

.spa-cons-header {
    background: linear-gradient(135deg, #c98276, #e6a28d); /* Gradient red */
}

.spa-pros-header:before,
.spa-cons-header:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%);
    z-index: 1;
}

.spa-pros-header i,
.spa-cons-header i {
    font-size: 2.5rem;
    margin-right: 20px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
    position: relative;
    z-index: 2;
}

.spa-pros-header span,
.spa-cons-header span {
    position: relative;
    z-index: 2;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
}

/* List Container */
.spa-pros-list,
.spa-cons-list {
    background-color: #fff;
    padding: 30px;
    flex-grow: 1;
    background: linear-gradient(to bottom, #ffffff, #f9f9f5);
}

/* List Items */
.spa-pros-list ul,
.spa-cons-list ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.spa-pros-list li,
.spa-cons-list li {
    position: relative;
    padding: 15px 0 15px 40px;
    margin-bottom: 15px;
    border-bottom: 1px solid rgba(210, 180, 140, 0.2);
    font-size: 1.1rem;
    line-height: 1.5;
    transition: all 0.3s ease;
}

.spa-pros-list li:hover,
.spa-cons-list li:hover {
    transform: translateX(5px);
    padding-left: 45px;
}

.spa-pros-list li:last-child,
.spa-cons-list li:last-child {
    margin-bottom: 0;
    border-bottom: none;
}

.spa-pros-list li:before,
.spa-cons-list li:before {
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    position: absolute;
    left: 0;
    top: 15px;
    font-size: 1.3rem;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.spa-pros-list li:before {
    content: '\f00c'; /* Check mark */
    color: #fff;
    background-color: #8fb9aa;
    box-shadow: 0 3px 10px rgba(143, 185, 170, 0.3);
}

.spa-cons-list li:before {
    content: '\f00d'; /* X mark */
    color: #fff;
    background-color: #c98276;
    box-shadow: 0 3px 10px rgba(201, 130, 118, 0.3);
}

.spa-pros-list li:hover:before,
.spa-cons-list li:hover:before {
    transform: scale(1.1) rotate(5deg);
}

/* Shine effect on hover */
.spa-pros-column:after,
.spa-cons-column:after {
    content: '';
    position: absolute;
    top: -50%;
    left: -60%;
    width: 20%;
    height: 200%;
    opacity: 0;
    transform: rotate(30deg);
    background: rgba(255, 255, 255, 0.13);
    background: linear-gradient(
        to right,
        rgba(255, 255, 255, 0.13) 0%,
        rgba(255, 255, 255, 0.13) 77%,
        rgba(255, 255, 255, 0.5) 92%,
        rgba(255, 255, 255, 0.0) 100%
    );
}

.spa-pros-column:hover:after,
.spa-cons-column:hover:after {
    opacity: 1;
    left: 130%;
    transition: all 0.7s ease;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .spa-pros-cons-container {
        grid-template-columns: 1fr;
    }

    .spa-pros-column,
    .spa-cons-column {
        margin-bottom: 20px;
    }

    .spa-pros-column:last-child,
    .spa-cons-column:last-child {
        margin-bottom: 0;
    }

    .spa-pros-header,
    .spa-cons-header {
        padding: 20px;
        font-size: 1.3rem;
    }

    .spa-pros-header i,
    .spa-cons-header i {
        font-size: 2rem;
        margin-right: 15px;
    }

    .spa-pros-list,
    .spa-cons-list {
        padding: 20px;
    }

    .spa-pros-list li,
    .spa-cons-list li {
        padding: 12px 0 12px 35px;
        font-size: 1rem;
    }

    .spa-pros-list li:before,
    .spa-cons-list li:before {
        top: 12px;
        font-size: 1.1rem;
        width: 25px;
        height: 25px;
    }
}
