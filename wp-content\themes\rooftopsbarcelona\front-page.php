<?php
/**
 * The front page template file
 *
 * This is the template for the site's front page
 */

get_header();
?>

<?php get_template_part( 'template-parts/hero-section' ); ?>

<div id="primary" class="content-area">
    <main id="main" class="site-main">
        <!-- Popular Tags Section (First) -->
        <section class="spa-tags">
            <div class="spa-container">
                <div class="spa-section-header">
                    <h2 class="spa-section-title">Most Popular Spa Features in Barcelona</h2>
                    <p class="spa-section-subtitle">Discover spas by commonly searched features</p>
                </div>

                <div class="spa-tags-grid">
                    <?php
                    // Get popular items from the 'popular' taxonomy
                    $popular_items = get_terms( array(
                        'taxonomy' => 'popular', // Use the new 'popular' taxonomy
                        'hide_empty' => true,
                        'number' => 8,
                        'orderby' => 'count',
                        'order' => 'DESC'
                    ) );

                    // If no popular items found, display a message
                    if (empty($popular_items) || is_wp_error($popular_items)) {
                        //echo '<div class="no-results"><p>No popular items found.</p></div>';
                        echo '<p>No spas found to suggest.</p>';
                    } else {
                        // Use the popular items from the database
                        foreach ($popular_items as $item) {
                            // Get icon from term meta or use default
                            $icon = get_term_meta($item->term_id, 'icon', true);
                            if (empty($icon)) {
                                $icon = 'fa-star'; // Default icon for popular
                            }
                            $term_link = get_term_link( $item );

                            echo '<a href="' . esc_url( $term_link ) . '" class="spa-tag-card">'; // spa-tag-card can be kept or renamed for consistency
                            echo '<div class="spa-tag-icon"><i class="fas ' . esc_attr($icon) . '"></i></div>';
                            echo '<h3 class="spa-tag-title">' . esc_html($item->name) . '</h3>';
                            echo '<span class="spa-tag-count">' . esc_html($item->count) . ' ' . esc_html(_n('Spa', 'Spas', $item->count, 'spasinbarcelona')) . '</span>';
                            echo '</a>';
                        }
                    }
                    ?>
                </div>

                <div class="spa-section-footer">
                    <a href="http://spasinbarcelona4.local/spa/" class="spa-cta-style-button">
                        View All Spas <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </section>

        <!-- Categories Section (Second) -->
        <section class="spa-categories">
            <div class="spa-container">
                <div class="spa-section-header">
                    <h2 class="spa-section-title">Explore Spa Services in Barcelona</h2>
                    <p class="spa-section-subtitle">Find the perfect spa experience for your needs</p>
                </div>

                <div class="spa-categories-grid">
                    <?php
                    $services_terms = get_terms( array(
                        'taxonomy' => 'services', // Use the new 'services' taxonomy
                        'hide_empty' => true,
                        'number' => 8, // Show 8 items
                        'orderby' => 'count',
                        'order' => 'DESC'
                    ) );

                    if ( ! empty( $services_terms ) && ! is_wp_error( $services_terms ) ) {
                        foreach ( $services_terms as $term ) {
                            $icon_class = 'fa-spa'; // Default icon

                            // Get icon from term meta or use default based on name
                            $meta_icon = get_term_meta($term->term_id, 'icon', true);
                            if (!empty($meta_icon)) {
                                $icon_class = $meta_icon;
                            } else {
                                // Assign different icons based on term name (fallback)
                                if ( stripos( $term->name, 'luxury' ) !== false ) {
                                    $icon_class = 'fa-gem';
                                } elseif ( stripos( $term->name, 'wellness' ) !== false ) {
                                    $icon_class = 'fa-heartbeat';
                                } elseif ( stripos( $term->name, 'beauty' ) !== false ) {
                                    $icon_class = 'fa-magic';
                                } elseif ( stripos( $term->name, 'massage' ) !== false ) {
                                    $icon_class = 'fa-hands';
                                } elseif ( stripos( $term->name, 'thermal' ) !== false || stripos( $term->name, 'water' ) !== false ) {
                                    $icon_class = 'fa-water';
                                }
                            }

                            $term_link = get_term_link( $term );
                            echo '<a href="' . esc_url( $term_link ) . '" class="spa-category-card">'; // spa-category-card can be kept or renamed
                            echo '<div class="spa-category-icon"><i class="fas ' . esc_attr( $icon_class ) . '"></i></div>';
                            echo '<h3 class="spa-category-title">' . esc_html( $term->name ) . '</h3>';

                            // Get count of spas in this term
                            $count = $term->count;
                            echo '<span class="spa-category-count">' . esc_html( $count ) . ' ' . esc_html( _n( 'Spa', 'Spas', $count, 'spasinbarcelona' ) ) . '</span>';

                            echo '</a>';
                        }
                    } else {
                        //echo '<div class="no-results"><p>No categories found.</p></div>';
                        echo '<p>No spas found to suggest.</p>';
                    }
                    ?>
                </div>

                <div class="spa-section-footer">
                    <a href="http://spasinbarcelona4.local/spa/" class="spa-cta-style-button">
                        View All Spas <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </section>

        <!-- Special Features Section (Third) -->
        <section class="spa-features">
            <div class="spa-container">
                <div class="spa-section-header">
                    <h2 class="spa-section-title">Find Barcelona Spas by Amenities</h2>
                    <p class="spa-section-subtitle">Find spas with these popular amenities and features</p>
                </div>

                <div class="spa-features-grid">
                    <?php
                    $amenities_terms = get_terms( array(
                        'taxonomy' => 'amenities', // Use the new 'amenities' taxonomy
                        'hide_empty' => true,
                        'orderby' => 'count',
                        'order' => 'DESC',
                        'number' => 8,
                    ) );

                    if ( ! empty( $amenities_terms ) && ! is_wp_error( $amenities_terms ) ) {
                        foreach ( $amenities_terms as $term ) {
                            $icon_class = 'fa-check-circle'; // Default icon

                            // Get icon from term meta or use default based on name
                            $meta_icon = get_term_meta($term->term_id, 'icon', true);
                            if (!empty($meta_icon)) {
                                $icon_class = $meta_icon;
                            } else {
                                // Assign different icons based on term name (fallback)
                                if ( stripos( $term->name, 'pool' ) !== false ) {
                                    $icon_class = 'fa-swimming-pool';
                                } elseif ( stripos( $term->name, 'couple' ) !== false ) {
                                    $icon_class = 'fa-heart';
                                } elseif ( stripos( $term->name, 'sauna' ) !== false ) {
                                    $icon_class = 'fa-hot-tub';
                                } elseif ( stripos( $term->name, 'gym' ) !== false || stripos( $term->name, 'fitness' ) !== false ) {
                                    $icon_class = 'fa-dumbbell';
                                } elseif ( stripos( $term->name, 'restaurant' ) !== false || stripos( $term->name, 'food' ) !== false ) {
                                    $icon_class = 'fa-utensils';
                                } elseif ( stripos( $term->name, 'parking' ) !== false ) {
                                    $icon_class = 'fa-parking';
                                } elseif ( stripos( $term->name, 'wifi' ) !== false ) {
                                    $icon_class = 'fa-wifi';
                                } elseif ( stripos( $term->name, 'wheelchair' ) !== false || stripos( $term->name, 'accessible' ) !== false ) {
                                    $icon_class = 'fa-wheelchair';
                                }
                            }
                            $term_link = get_term_link( $term );
                            echo '<a href="' . esc_url( $term_link ) . '" class="spa-feature-card">';
                            echo '<div class="spa-feature-icon"><i class="fas ' . esc_attr( $icon_class ) . '"></i></div>';
                            echo '<h3 class="spa-feature-title">' . esc_html( $term->name ) . '</h3>';

                            // Get count of spas with this amenity
                            $count = $term->count;
                            echo '<span class="spa-feature-count">' . esc_html( $count ) . ' ' . esc_html( _n( 'Spa', 'Spas', $count, 'spasinbarcelona' ) ) . '</span>';

                            echo '</a>';
                        }
                    } else {
                        //echo '<div class="no-results"><p>No features found.</p></div>';
                        echo '<p>No spas found to suggest.</p>';
                    }
                    ?>
                </div>

                <div class="spa-section-footer">
                    <a href="http://spasinbarcelona4.local/spa/" class="spa-cta-style-button">
                        View All Spas <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </section>

        <!-- Featured Spas Section (Moved to the end) -->
        <section class="spa-featured">
            <div class="spa-container">
                <div class="spa-section-header">
                    <h2 class="spa-section-title">Featured Spas in Barcelona</h2>
                    <p class="spa-section-subtitle">Discover our selection of the best spas in Barcelona</p>
                </div>

                <div class="spa-grid">
                    <?php
                    // Get featured spas from customizer setting
                    $featured_spas = get_theme_mod( 'spasinbarcelona_featured_spas', array() );

                    if ( ! empty( $featured_spas ) ) {
                        $args = array(
                            'post_type' => 'spa',
                            'post__in' => $featured_spas,
                            'posts_per_page' => 9,
                        );
                    } else {
                        // If no featured spas are set, get the most recent ones
                        $args = array(
                            'post_type' => 'spa',
                            'posts_per_page' => 9,
                        );
                    }

                    $featured_query = new WP_Query( $args );

                    if ( $featured_query->have_posts() ) {
                        while ( $featured_query->have_posts() ) {
                            $featured_query->the_post();
                            get_template_part( 'template-parts/content', 'spa-card' );
                        }
                        wp_reset_postdata();
                    } else {
                        //echo '<div class="no-results"><p>No featured spas found.</p></div>';
                        echo '<p>No spas found to suggest.</p>';
                    }
                    ?>
                </div>

                <div class="spa-section-footer">
                    <a href="http://spasinbarcelona4.local/spa/" class="spa-cta-style-button">
                        View All Spas <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </section>

        <!-- Neighborhoods Section -->
        <section class="spa-neighborhoods">
            <style>
                /* Inline styles for neighborhood cards - matching amenities cards */
                .spa-neighborhoods {
                    padding: 80px 0 !important;
                    background-color: #f5f5f0 !important;
                    position: relative !important;
                    overflow: hidden !important;
                    box-shadow: none !important;
                }

                /* Add a subtle pattern overlay to the neighborhoods section */
                .spa-neighborhoods:before {
                    content: '' !important;
                    position: absolute !important;
                    top: 0 !important;
                    left: 0 !important;
                    right: 0 !important;
                    bottom: 0 !important;
                    background-image: url('data:image/svg+xml;utf8,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><circle cx="30" cy="30" r="20" fill="none" stroke="rgba(210, 180, 140, 0.05)" stroke-width="1"/></svg>') !important;
                    opacity: 0.5 !important;
                    pointer-events: none !important;
                }

                /* We're using spa-features-grid class now, so this is no longer needed */

                /* We're using spa-feature-card class now, so these styles are no longer needed */
            </style>
            <div class="spa-container">
                <div class="spa-section-header">
                    <h2 class="spa-section-title">Explore Spas by Barcelona Neighborhoods</h2>
                    <p class="spa-section-subtitle">Discover spas in your favorite Barcelona neighborhoods</p>
                </div>

                <div class="spa-features-grid">
                    <?php
                    // Get all neighborhoods that have spas assigned to them
                    // We want to display only Vila Olímpica, Sants, and Pedralbes
                    $expected_neighborhoods = array('vila-olimpica', 'sants', 'pedralbes', 'leixample', 'poblenou', 'el-born', 'gothic-quarter', 'les-corts', 'gracia');

                    $neighborhoods_terms = get_terms(array(
                        'taxonomy' => 'neighborhoods', // Use the new 'neighborhoods' taxonomy
                        'hide_empty' => true,
                        'orderby' => 'count',
                        'order' => 'DESC',
                        'number' => 8, // Display 8 neighborhoods
                        // 'slug' => $expected_neighborhoods, // If you still want to limit to specific slugs
                    ));

                    // Define icons for known neighborhoods (can be expanded or moved to term meta)
                    $neighborhood_icons = array(
                        'poblenou' => 'fa-city', // Corrected icon class
                        'sants' => 'fa-train',
                        'el-born' => 'fa-tree',
                        'gothic-quarter' => 'fa-landmark',
                        'leixample' => 'fa-city',
                        'les-corts' => 'fa-building',
                        'la-barceloneta' => 'fa-umbrella-beach',
                        'gracia' => 'fa-coffee',
                        'vila-olimpica'=> 'fa-medal'
                    );

                    // Default icon if neighborhood isn't in our list
                    $default_icon = 'fa-map-marker-alt';

                    if (!empty($neighborhoods_terms) && !is_wp_error($neighborhoods_terms)) {
                        foreach ($neighborhoods_terms as $term) {

                            // Get icon from term meta or use predefined list
                            $icon = get_term_meta($term->term_id, 'icon', true);
                            if (empty($icon)) {
                                $icon = isset($neighborhood_icons[$term->slug]) ? $neighborhood_icons[$term->slug] : $default_icon;
                            }

                            $neighborhood_url = get_term_link($term);

                            // Debug info for administrators
                            if (current_user_can('administrator')) {
                                echo '<!-- Neighborhood: ' . esc_html($term->name) . ' | Slug: ' . esc_html($term->slug) . ' | URL: ' . esc_url($neighborhood_url) . ' -->';
                            }

                            // Match the style of the "Explore by Amenities" section
                            echo '<a href="' . esc_url($neighborhood_url) . '" class="spa-feature-card">';
                            echo '<div class="spa-feature-icon"><i class="fas ' . esc_attr($icon) . '"></i></div>';
                            echo '<h3 class="spa-feature-title">' . esc_html($term->name) . '</h3>';
                            echo '<span class="spa-feature-count">' . esc_html($term->count) . ' ' . esc_html(_n('Spa', 'Spas', $term->count, 'spasinbarcelona')) . '</span>';
                            echo '</a>';
                        }
                    
                    } else {
                        //echo '<div class="no-results"><p>No neighborhoods with spas found.</p></div>';
                        echo '<p>No spas found to suggest.</p>';
                    }
                    ?>
                </div>

                <div class="spa-section-footer">
                    <a href="http://spasinbarcelona4.local/spa/" class="spa-cta-style-button">
                        View All Spas <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </section>
    </main>
</div>

<!-- CTA Section (Outside of main content) -->
<div id="spa-cta-wrapper" class="spa-cta-wrapper">
    <?php get_template_part( 'template-parts/cta-section' ); ?>
</div>

<?php
// Get the footer
get_footer();
