/**
 * Rooftops in Barcelona custom scripts
 */
(function($) {
    'use strict';

    // Initialize the rooftop filter functionality
    function initRooftopFilter() {
        const $form = $('.spa-filter-form');
        const $results = $('.spa-results');
        const $pagination = $('.spa-pagination');
        const $loading = $('.spa-loading');

        if (!$form.length) {
            return;
        }

        // Handle form submission
        $form.on('submit', function(e) {
            e.preventDefault();

            // Log form values for debugging
            console.log('Form submitted with values:');
            console.log('Neighborhood:', $('#neighborhood_filter').val());
            console.log('Popular:', $('#popular_filter').val());
            console.log('Service:', $('#service_filter').val());
            console.log('Amenities:', $('#amenities_filter').val());
            console.log('Sort by:', $('#sort_by').val());

            loadRooftops(1);
        });

        // Handle pagination clicks
        $('body').on('click', '.spa-pagination a', function(e) {
            e.preventDefault();
            let pageNum;
            const pageDataAttr = $(this).attr('data-page');

            if (pageDataAttr !== undefined) {
                pageNum = pageDataAttr;
                // console.log('Pagination: Using data-page attribute:', pageNum);
            } else {
                const href = $(this).attr('href');
                // console.log('Pagination: data-page not found, trying href:', href);
                if (href) {
                    const match = href.match(/\/page\/(\d+)\/?$/); // Matches /page/NUMBER/ or /page/NUMBER at the end of the href
                    if (match && match[1]) {
                        pageNum = match[1];
                        // console.log('Pagination: Extracted pageNum from href:', pageNum);
                    } else {
                        // console.log('Pagination: Could not extract page number from href.');
                    }
                }
            }

            // console.log('Pagination click - Final pageNum to load:', pageNum);
            loadRooftops(pageNum);


            // Scroll to top of results
            $('html, body').animate({
                scrollTop: $results.offset().top - 100
            }, 500);
        });

        // Handle filter changes (auto-submit)
        $('.spa-filter-select').on('change', function() {
            // Log the changed select and its value
            console.log('Select changed:', this.id, 'Value:', $(this).val());
            $form.submit();
        });

        // Handle clear filters button
        $('.spa-clear-filter-button').on('click', function(e) {
            e.preventDefault();

            // Reset all form fields
            $form.find('select').val('');
            $form.find('input[type="text"]').val('');

            // Submit the form with cleared values
            $form.submit();
        });

        // Function to load rooftops via AJAX
        function loadRooftops(pageArg) {
            let currentPage = 1; // Default to 1 immediately
            if (pageArg !== undefined && pageArg !== null) {
                const parsedPage = parseInt(pageArg, 10);
                if (!isNaN(parsedPage) && parsedPage > 0) {
                    currentPage = parsedPage;
                }
            }
            // currentPage is now guaranteed to be a positive integer

            $loading.show();
            $results.addClass('loading');

            // Get form values directly instead of using serialize
            const neighborhoodValue = $('#neighborhood_filter').val();
            const popularValue = $('#popular_filter').val();
            const serviceValue = $('#service_filter').val();
            const amenitiesValue = $('#amenities_filter').val();
            const sortByValue = $('#sort_by').val();
            const searchValue = $('#search').val();

            // Create the data object for the AJAX request
            const ajaxData = {
                action: 'rooftopsbarcelona_filter_rooftops',
                nonce: rooftopsbarcelona_ajax.nonce,
                paged: currentPage, // Changed from 'page' to 'paged'
                neighborhood: neighborhoodValue,
                category: popularValue, // PHP expects 'category' for popular filter
                service: serviceValue,
                feature: amenitiesValue, // PHP expects 'feature' for amenities filter
                sort_by: sortByValue,
                search: searchValue
            };

            // Log the data being sent (for debugging)
            console.log('loadRooftops - pageArg:', pageArg, 'currentPage:', currentPage, 'ajaxData:', JSON.stringify(ajaxData)); // Enhanced log

            $.ajax({
                url: rooftopsbarcelona_ajax.ajax_url,
                type: 'POST',
                data: ajaxData,
                success: function(response) {
                    console.log('AJAX Success - response.data:', response.data); // Log the full response.data
                    if (response.success) {
                        // Check if the response already contains the spa-grid wrapper
                        let html = response.data.html;
                        if (html.indexOf('spa-grid') === -1 && !html.includes('no-results')) {
                            // Wrap the results in a spa-grid div if not already wrapped
                            html = '<div class="spa-grid">' + html + '</div>';
                        }

                        $results.html(html);
                        updatePagination(currentPage, response.data.max_pages); // Used currentPage from loadSpas scope

                        // Update URL with filter parameters
                        updateUrl();
                    } else {
                        $results.html('<div class="spa-error">Error loading spas. Please try again.</div>');
                    }
                },
                error: function() {
                    $results.html('<div class="spa-error">Error loading spas. Please try again.</div>');
                },
                complete: function() {
                    $loading.hide();
                    $results.removeClass('loading');
                }
            });
        }

        // Function to update pagination
        function updatePagination(currentPage, maxPages) {
            if (maxPages <= 1) {
                $pagination.empty();
                return;
            }

            let html = '';

            // Previous button
            if (currentPage > 1) {
                html += '<a href="#" data-page="' + (currentPage - 1) + '" class="prev">&laquo; Previous</a>';
            }

            // Page numbers
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(maxPages, startPage + 4);

            for (let i = startPage; i <= endPage; i++) {
                if (i === currentPage) {
                    html += '<span class="current">' + i + '</span>';
                } else {
                    html += '<a href="#" data-page="' + i + '">' + i + '</a>';
                }
            }

            // Next button
            if (currentPage < maxPages) {
                html += '<a href="#" data-page="' + (currentPage + 1) + '" class="next">Next &raquo;</a>';
            }

            $pagination.html(html);
        }

        // Function to update URL with filter parameters
        function updateUrl() {
            if (!history.pushState) {
                return;
            }

            const url = new URL(window.location);

            // Get form data
            const formData = new FormData($form[0]);

            // Update URL parameters
            for (const [key, value] of formData.entries()) {
                if (value) {
                    url.searchParams.set(key, value);
                } else {
                    url.searchParams.delete(key);
                }
            }

            // Update browser history
            history.pushState({}, '', url);
        }

        // Load filters from URL on page load
        function loadFiltersFromUrl() {
            const url = new URL(window.location);

            // Set form values from URL parameters
            for (const [key, value] of url.searchParams.entries()) {
                const $input = $form.find('[name="' + key + '"]');

                if ($input.length) {
                    $input.val(value);
                }
            }

            // Submit form if there are filter parameters
            if (url.searchParams.toString()) {
                $form.submit();
            }
        }

        // Initialize
        loadFiltersFromUrl();
    }

    // Initialize smooth scrolling for section links
    function initSmoothScroll() {
        // Check if we're on a single spa page
        if (!$('.spa-single').length) {
            return;
        }

        // Add section navigation
        const $mainContent = $('.spa-main-content');
        const $sections = $mainContent.find('.spa-section');

        if (!$sections.length) {
            return;
        }

        // Create section navigation
        const $nav = $('<div class="spa-section-nav"></div>');
        $sections.each(function() {
            const $section = $(this);
            const id = $section.attr('id');
            const title = $section.find('.spa-section-title').text();

            if (id && title) {
                // Extract just the main title without the spa name
                let displayTitle = title;
                if (title.includes('About ')) {
                    displayTitle = 'About';
                }
                $nav.append('<a href="#' + id + '" class="spa-section-nav-link">' + displayTitle + '</a>');
            }
        });

        // Insert navigation before main content
        $nav.insertBefore($mainContent);

        // Handle smooth scrolling
        $('.spa-section-nav-link').on('click', function(e) {
            e.preventDefault();

            const target = $(this).attr('href');
            const $target = $(target);

            if ($target.length) {
                $('html, body').animate({
                    scrollTop: $target.offset().top - 100
                }, 800);

                // Update active state
                $('.spa-section-nav-link').removeClass('active');
                $(this).addClass('active');
            }
        });

        // Update active nav item on scroll
        $(window).on('scroll', function() {
            const scrollPosition = $(window).scrollTop() + 150;

            $sections.each(function() {
                const $section = $(this);
                const sectionTop = $section.offset().top;
                const sectionBottom = sectionTop + $section.outerHeight();

                if (scrollPosition >= sectionTop && scrollPosition < sectionBottom) {
                    const id = $section.attr('id');
                    $('.spa-section-nav-link').removeClass('active');
                    $('.spa-section-nav-link[href="#' + id + '"]').addClass('active');
                }
            });
        });
    }

    // Initialize lightbox for spa images
    function initLightbox() {
        // Store all gallery items for navigation
        const $galleryItems = $('.spa-gallery-item');
        let currentIndex = 0;

        $galleryItems.on('click', function(e) {
            e.preventDefault();

            const $this = $(this);
            currentIndex = $galleryItems.index($this);
            openLightbox(currentIndex);
        });

        // Function to open lightbox with specific image
        function openLightbox(index) {
            const $item = $galleryItems.eq(index);
            const imageUrl = $item.attr('href');
            const caption = $item.data('caption') || ''; // This is the original caption from data attribute, potentially used by the (hidden) visual caption

            // Get the spa name for the alt text
            let spaName = '';
            // Attempt to find the spa name from common H1 or .entry-title within .spa-single context.
            // This searches for various common patterns for a page title.
            const $spaTitleElement = $('.spa-single h1.entry-title, .spa-single > .entry-title, .spa-single header h1, .spa-single .entry-title');
            if ($spaTitleElement.length) {
                spaName = $spaTitleElement.first().text().trim();
            } else {
                // Fallback to the first H1 inside .spa-single if specific title classes aren't found
                const $genericTitle = $('.spa-single h1');
                if ($genericTitle.length) {
                    spaName = $genericTitle.first().text().trim();
                }
            }
            // If spaName remains empty (e.g., title element not found), the alt tag will be empty.

            // Create lightbox elements
            const $lightbox = $('<div class="spa-lightbox"></div>');
            const $overlay = $('<div class="spa-lightbox-overlay"></div>');
            const $content = $('<div class="spa-lightbox-content"></div>');
            const $image = $('<img src="' + imageUrl + '" alt="' + spaName + '">'); // Use spaName for alt text
            const $caption = $('<div class="spa-lightbox-caption">' + caption + '</div>');
            const $close = $('<button class="spa-lightbox-close">&times;</button>');

            // Add navigation buttons if there are multiple images
            let $prev, $next;
            if ($galleryItems.length > 1) {
                $prev = $('<button class="spa-lightbox-nav spa-lightbox-prev"><i class="fas fa-chevron-left"></i></button>');
                $next = $('<button class="spa-lightbox-nav spa-lightbox-next"><i class="fas fa-chevron-right"></i></button>');
                $lightbox.append($prev, $next);

                // Handle navigation clicks
                $prev.on('click', function(e) {
                    e.stopPropagation();
                    navigateLightbox('prev');
                });

                $next.on('click', function(e) {
                    e.stopPropagation();
                    navigateLightbox('next');
                });
            }

            // Append elements
            $content.append($image, $close);
            $lightbox.append($overlay, $content);
            $('body').append($lightbox);

            // Show lightbox with slight delay for animation
            setTimeout(function() {
                $lightbox.addClass('active');
            }, 10);

            // Handle close
            $close.add($overlay).on('click', function() {
                closeLightbox($lightbox);
            });

            // Handle keyboard navigation
            $(document).on('keyup.lightbox', function(e) {
                if (e.key === 'Escape') {
                    closeLightbox($lightbox);
                } else if (e.key === 'ArrowLeft' && $galleryItems.length > 1) {
                    navigateLightbox('prev');
                } else if (e.key === 'ArrowRight' && $galleryItems.length > 1) {
                    navigateLightbox('next');
                }
            });

            // Function to navigate between images
            function navigateLightbox(direction) {
                let newIndex;
                if (direction === 'prev') {
                    newIndex = (currentIndex - 1 + $galleryItems.length) % $galleryItems.length;
                } else {
                    newIndex = (currentIndex + 1) % $galleryItems.length;
                }

                // Close current lightbox and open new one
                closeLightbox($lightbox, function() {
                    currentIndex = newIndex;
                    openLightbox(newIndex);
                });
            }
        }

        // Function to close lightbox
        function closeLightbox($lightbox, callback) {
            $lightbox.removeClass('active');
            $(document).off('keyup.lightbox');

            setTimeout(function() {
                $lightbox.remove();
                if (typeof callback === 'function') {
                    callback();
                }
            }, 300);
        }
    }

    // Initialize sticky header
    function initStickyHeader() {
        const $header = $('.site-header');
        const headerHeight = $header.outerHeight();
        let isSticky = false;

        $(window).on('scroll', function() {
            const scrollTop = $(window).scrollTop();

            if (scrollTop > headerHeight && !isSticky) {
                $header.addClass('sticky-header');
                $('body').css('padding-top', headerHeight);
                isSticky = true;
            } else if (scrollTop <= headerHeight && isSticky) {
                $header.removeClass('sticky-header');
                $('body').css('padding-top', 0);
                isSticky = false;
            }
        });
    }

    // Initialize on document ready
    $(document).ready(function() {
        initRooftopFilter();
        initSmoothScroll();
        initLightbox();
        initStickyHeader();
    });

})(jQuery);
