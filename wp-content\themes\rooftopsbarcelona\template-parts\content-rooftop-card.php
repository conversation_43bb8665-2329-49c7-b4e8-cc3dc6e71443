<?php
/**
 * Template part for displaying spa cards in the directory
 */

// Get spa data
$spa_id = get_the_ID();
$location = get_post_meta( $spa_id, 'location', true );
$reviews = get_post_meta( $spa_id, 'reviews', true );
$images = get_post_meta( $spa_id, 'images', true );

// Calculate average rating using the dedicated function
$rating_data = spasinbarcelona_calculate_rating(!empty($reviews['review_sources']) ? $reviews['review_sources'] : array());
$avg_rating = $rating_data['rating'];
$review_count = $rating_data['count'];

// Get featured image or first image from the spa data
$image_url = '';
if ( has_post_thumbnail() ) {
    $image_url = get_the_post_thumbnail_url( $spa_id, 'medium' );
} elseif ( ! empty( $images ) && is_array( $images ) && ! empty( $images[0]['url'] ) ) {
    $image_url = esc_url( $images[0]['url'] );
}

// Get categories
$categories = get_the_terms( $spa_id, 'spa_category' );
$category_name = '';

if ( $categories && ! is_wp_error( $categories ) ) {
    $category = reset( $categories );
    $category_name = $category->name;
}

// Get services (limit to 3)
$services = get_the_terms( $spa_id, 'spa_service' );
$service_list = array();

if ( $services && ! is_wp_error( $services ) ) {
    foreach ( $services as $service ) {
        $service_list[] = $service->name;

        if ( count( $service_list ) >= 3 ) {
            break;
        }
    }
}
?>

<article id="post-<?php the_ID(); ?>" <?php post_class( 'spa-card' ); ?> style="overflow:hidden !important; padding:0 !important; border-radius:12px !important;">
    <div class="spa-card-inner" style="width:100% !important; padding:0 !important; margin:0 !important;">
        <div class="spa-card-image" style="padding:0 !important; margin:0 !important; overflow:hidden !important; border-radius:12px 12px 0 0 !important; background-color:#000 !important;">
            <a href="<?php the_permalink(); ?>" style="display:block !important; padding:0 !important; margin:0 !important; height:100% !important; width:100% !important; line-height:0 !important; font-size:0 !important;">
                <?php if ( ! empty( $image_url ) ) : ?>
                    <img src="<?php echo esc_url( $image_url ); ?>" alt="<?php the_title_attribute(); ?>" style="display:block !important; width:100% !important; height:100% !important; padding:0 !important; margin:0 !important; object-fit:cover !important; border-radius:12px 12px 0 0 !important;">
                <?php else : ?>
                    <div class="spa-no-image">
                        <i class="fas fa-spa"></i>
                    </div>
                <?php endif; ?>

                <?php if ( $avg_rating > 0 ) : ?>
                    <div class="spa-rating-badge">
                        <div class="spa-rating-badge-stars"><?php echo number_format($avg_rating, 1); ?> <span class="star-icon">★</span></div>
                    </div>
                <?php endif; ?>
            </a>
        </div>

        <div class="spa-card-content">
            <h3 class="spa-card-title">
                <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
            </h3>

            <?php if ( ! empty( $location['city'] ) ) : ?>
                <div class="spa-location">
                    <i class="fas fa-map-marker-alt"></i> <?php echo esc_html( $location['city'] ); ?>
                </div>
            <?php endif; ?>

            <div class="spa-excerpt">
                <?php echo wp_trim_words( get_the_excerpt(), 30, '...' ); ?>
            </div>

            <a href="<?php the_permalink(); ?>" class="spa-read-more">View Details <i class="fas fa-arrow-right"></i></a>
        </div>
    </div>
</article>
