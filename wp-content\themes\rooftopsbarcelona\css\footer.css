/**
 * Footer styles for Spas in Barcelona theme
 */

/* Footer Container */
.site-footer {
    background-color: var(--dark-color, #3a4a4a) !important;
    color: var(--light-text-color, #ffffff) !important;
    padding: 60px 0 30px;
    font-family: var(--font-primary, 'Poppins', sans-serif) !important;
}

/* Footer Widgets */
.footer-widgets {
    display: grid !important;
    grid-template-columns: repeat(4, 1fr) !important;
    gap: 30px !important;
    margin-bottom: 40px !important;
}

.footer-widget {
    margin-bottom: 30px !important;
}

.footer-widget-title {
    color: #fff !important;
    font-size: 1.2rem !important;
    margin-bottom: 20px !important;
    padding-bottom: 10px !important;
    border-bottom: 2px solid var(--primary-color, #68a0a0) !important;
    display: inline-block !important;
    font-family: var(--font-secondary, 'Playfair Display', serif) !important;
    font-weight: 600 !important;
}

.footer-widget ul {
    list-style: none !important;
    padding: 0 !important;
    margin: 0 !important;
}

.footer-widget li {
    margin-bottom: 10px !important;
    color: var(--light-text-color, #ffffff) !important;
}

.footer-widget a {
    color: var(--light-text-color, #ffffff) !important;
    text-decoration: none !important;
    transition: var(--transition, all 0.4s ease) !important;
}

.footer-widget a:hover {
    color: var(--primary-color, #68a0a0) !important;
}

/* Footer Navigation */
.footer-navigation ul {
    display: flex !important;
    flex-wrap: wrap !important;
    list-style: none !important;
    padding: 0 !important;
    margin: 0 0 20px !important;
}

.footer-navigation li {
    margin-right: 20px !important;
    margin-bottom: 10px !important;
}

.footer-navigation a {
    color: var(--light-text-color, #ffffff) !important;
    text-decoration: none !important;
    transition: var(--transition, all 0.4s ease) !important;
}

.footer-navigation a:hover {
    color: var(--primary-color, #68a0a0) !important;
}

/* Footer Bottom */
.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
    padding-top: 20px !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    flex-wrap: wrap !important;
}

.footer-copyright {
    margin-bottom: 10px !important;
    color: var(--light-text-color, #ffffff) !important;
}

/* Footer Social Links */
.footer-social {
    display: flex !important;
    margin-top: 20px !important;
}

.footer-social a {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: 36px !important;
    height: 36px !important;
    background-color: rgba(255, 255, 255, 0.1) !important;
    color: var(--light-text-color, #ffffff) !important;
    border-radius: 50% !important;
    margin-left: 10px !important;
    transition: var(--transition, all 0.4s ease) !important;
}

.footer-social a:hover {
    background-color: var(--primary-color, #68a0a0) !important;
    color: #fff !important;
    transform: translateY(-3px) !important;
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 40px;
    height: 40px;
    background-color: var(--primary-color);
    color: var(--light-text-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
    z-index: 99;
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    background-color: var(--secondary-color);
    color: var(--light-text-color);
    transform: translateY(-5px);
}

/* Responsive Styles */
@media (max-width: 992px) {
    .footer-widgets {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 576px) {
    .footer-widgets {
        grid-template-columns: 1fr;
    }

    .footer-bottom {
        flex-direction: column;
        text-align: center;
    }

    .footer-social {
        margin-top: 15px;
        justify-content: center;
    }

    .footer-social a {
        margin: 0 5px;
    }
}
