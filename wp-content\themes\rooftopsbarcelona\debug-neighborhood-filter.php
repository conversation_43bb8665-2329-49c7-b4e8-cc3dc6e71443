<?php
/**
 * Debug Neighborhood Filter
 * 
 * This script displays all neighborhoods in the database and their associated spas.
 */

// Load WordPress
require_once( dirname( __FILE__ ) . '/../../../wp-load.php' );

// Check if user is logged in and has admin privileges
if ( ! current_user_can( 'manage_options' ) ) {
    wp_die( 'You do not have sufficient permissions to access this page.' );
}

echo '<h1>Neighborhood Debug Information</h1>';

// Get all districts (parent terms)
$districts = get_terms(array(
    'taxonomy' => 'spa_neighborhood',
    'hide_empty' => false,
    'parent' => 0,
));

echo '<h2>Districts (Parent Terms)</h2>';
echo '<table border="1" cellpadding="5" style="border-collapse: collapse;">';
echo '<tr><th>ID</th><th>Name</th><th>Slug</th><th>Count</th><th>Description</th></tr>';

if (!empty($districts) && !is_wp_error($districts)) {
    foreach ($districts as $district) {
        echo '<tr>';
        echo '<td>' . $district->term_id . '</td>';
        echo '<td>' . $district->name . '</td>';
        echo '<td>' . $district->slug . '</td>';
        echo '<td>' . $district->count . '</td>';
        echo '<td>' . $district->description . '</td>';
        echo '</tr>';
    }
} else {
    echo '<tr><td colspan="5">No districts found</td></tr>';
}
echo '</table>';

// Get all neighborhoods (child terms)
echo '<h2>Neighborhoods (Child Terms)</h2>';
echo '<table border="1" cellpadding="5" style="border-collapse: collapse;">';
echo '<tr><th>ID</th><th>Name</th><th>Slug</th><th>Parent</th><th>Count</th><th>Description</th></tr>';

$neighborhoods = get_terms(array(
    'taxonomy' => 'spa_neighborhood',
    'hide_empty' => false,
    'parent' => array_column($districts, 'term_id'),
));

if (!empty($neighborhoods) && !is_wp_error($neighborhoods)) {
    foreach ($neighborhoods as $neighborhood) {
        $parent = get_term($neighborhood->parent, 'spa_neighborhood');
        $parent_name = $parent ? $parent->name : 'None';
        
        echo '<tr>';
        echo '<td>' . $neighborhood->term_id . '</td>';
        echo '<td>' . $neighborhood->name . '</td>';
        echo '<td>' . $neighborhood->slug . '</td>';
        echo '<td>' . $parent_name . ' (' . $neighborhood->parent . ')</td>';
        echo '<td>' . $neighborhood->count . '</td>';
        echo '<td>' . $neighborhood->description . '</td>';
        echo '</tr>';
    }
} else {
    echo '<tr><td colspan="6">No neighborhoods found</td></tr>';
}
echo '</table>';

// Get orphan neighborhoods (no parent)
echo '<h2>Orphan Neighborhoods (No Parent)</h2>';
echo '<table border="1" cellpadding="5" style="border-collapse: collapse;">';
echo '<tr><th>ID</th><th>Name</th><th>Slug</th><th>Count</th><th>Description</th></tr>';

$orphans = get_terms(array(
    'taxonomy' => 'spa_neighborhood',
    'hide_empty' => false,
    'parent' => '',
    'exclude' => array_column($districts, 'term_id'),
));

if (!empty($orphans) && !is_wp_error($orphans)) {
    foreach ($orphans as $orphan) {
        echo '<tr>';
        echo '<td>' . $orphan->term_id . '</td>';
        echo '<td>' . $orphan->name . '</td>';
        echo '<td>' . $orphan->slug . '</td>';
        echo '<td>' . $orphan->count . '</td>';
        echo '<td>' . $orphan->description . '</td>';
        echo '</tr>';
    }
} else {
    echo '<tr><td colspan="5">No orphan neighborhoods found</td></tr>';
}
echo '</table>';

// Get all spas and their neighborhoods
echo '<h2>Spas and Their Neighborhoods</h2>';
echo '<table border="1" cellpadding="5" style="border-collapse: collapse;">';
echo '<tr><th>ID</th><th>Title</th><th>Neighborhoods</th></tr>';

$spas = get_posts(array(
    'post_type' => 'spa',
    'posts_per_page' => -1,
));

if (!empty($spas)) {
    foreach ($spas as $spa) {
        $terms = get_the_terms($spa->ID, 'spa_neighborhood');
        $neighborhood_names = array();
        
        if (!empty($terms) && !is_wp_error($terms)) {
            foreach ($terms as $term) {
                $neighborhood_names[] = $term->name . ' (' . $term->term_id . ')';
            }
        }
        
        echo '<tr>';
        echo '<td>' . $spa->ID . '</td>';
        echo '<td>' . $spa->post_title . '</td>';
        echo '<td>' . (empty($neighborhood_names) ? 'None' : implode(', ', $neighborhood_names)) . '</td>';
        echo '</tr>';
    }
} else {
    echo '<tr><td colspan="3">No spas found</td></tr>';
}
echo '</table>';

// Test our filter code
echo '<h2>Neighborhood Filter Test</h2>';
echo '<p>This shows what our filter code would display:</p>';

// Get districts
$test_districts = get_terms(array(
    'taxonomy' => 'spa_neighborhood',
    'hide_empty' => true,
    'parent' => 0,
));

$all_neighborhoods = array();

// First collect all neighborhoods with spas
if (!empty($test_districts) && !is_wp_error($test_districts)) {
    foreach ($test_districts as $district) {
        // Get child neighborhoods for this district
        $child_neighborhoods = get_terms(array(
            'taxonomy' => 'spa_neighborhood',
            'hide_empty' => true,
            'parent' => $district->term_id,
        ));

        if (!empty($child_neighborhoods) && !is_wp_error($child_neighborhoods)) {
            foreach ($child_neighborhoods as $neighborhood) {
                $all_neighborhoods[] = $neighborhood;
            }
        } else {
            // If district has spas but no child neighborhoods, include the district itself
            if ($district->count > 0) {
                $all_neighborhoods[] = $district;
            }
        }
    }
}

// Also get any neighborhoods that might not have a parent
$orphan_neighborhoods = get_terms(array(
    'taxonomy' => 'spa_neighborhood',
    'hide_empty' => true,
    'parent' => '',
));

if (!empty($orphan_neighborhoods) && !is_wp_error($orphan_neighborhoods)) {
    foreach ($orphan_neighborhoods as $neighborhood) {
        // Check if this neighborhood is already in our list
        $exists = false;
        foreach ($all_neighborhoods as $existing) {
            if ($existing->term_id === $neighborhood->term_id) {
                $exists = true;
                break;
            }
        }
        
        if (!$exists) {
            $all_neighborhoods[] = $neighborhood;
        }
    }
}

// Sort neighborhoods alphabetically
usort($all_neighborhoods, function($a, $b) {
    return strcasecmp($a->name, $b->name);
});

echo '<ul>';
foreach ($all_neighborhoods as $neighborhood) {
    echo '<li>' . $neighborhood->name . ' (' . $neighborhood->slug . ') - ' . $neighborhood->count . ' spas</li>';
}
echo '</ul>';
