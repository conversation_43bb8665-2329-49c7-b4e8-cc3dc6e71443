<?php
/**
 * Add Neighborhood menu item to primary menu
 *
 * Note: This functionality has been disabled as per client request.
 */

// Don't run this file directly
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Add Neighborhood menu item to primary menu
 * This function is now disabled.
 */
function spasinbarcelona_add_neighborhood_menu_item() {
    // This function is now disabled
    return;

    // The code below is kept for reference but will not execute

    // Get the primary menu location ID
    $locations = get_nav_menu_locations();

    // Check if primary menu exists
    if (!isset($locations['primary'])) {
        return;
    }

    $menu_id = $locations['primary'];
    $menu = wp_get_nav_menu_object($menu_id);

    if (!$menu) {
        return;
    }

    // Check if the menu item already exists
    $menu_items = wp_get_nav_menu_items($menu_id);
    $neighborhood_exists = false;

    if ($menu_items) {
        foreach ($menu_items as $item) {
            if ($item->title === 'Neighborhoods') {
                $neighborhood_exists = true;
                break;
            }
        }
    }

    // Add Neighborhoods menu item if it doesn't exist
    if (!$neighborhood_exists) {
        wp_update_nav_menu_item($menu_id, 0, array(
            'menu-item-title' => 'Neighborhoods',
            'menu-item-url' => home_url('/spa-neighborhood/'),
            'menu-item-status' => 'publish',
            'menu-item-position' => 5, // Position after other menu items
        ));
    }
}

// Function hooks are commented out to disable this functionality

// Run the function on theme activation
// add_action('after_switch_theme', 'spasinbarcelona_add_neighborhood_menu_item');

// Also run it now to add the menu item immediately
// spasinbarcelona_add_neighborhood_menu_item();
