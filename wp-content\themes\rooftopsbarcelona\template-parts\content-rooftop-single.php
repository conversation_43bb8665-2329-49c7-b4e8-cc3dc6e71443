<?php
/**
 * Template part for displaying single spa content
 */

// Get spa data
$spa_id = get_the_ID();
$location = get_post_meta( $spa_id, 'location', true );
$contact = get_post_meta( $spa_id, 'contact', true );
$social_media = get_post_meta( $spa_id, 'social_media', true );
$services = get_post_meta( $spa_id, 'services', true );
$amenities = get_post_meta( $spa_id, 'amenities', true );
$hours = get_post_meta( $spa_id, 'hours', true );
$hoursDisplay = get_post_meta( $spa_id, 'hoursDisplay', true );
$hoursSchema = get_post_meta( $spa_id, 'hoursSchema', true );
$specialties = get_post_meta( $spa_id, 'specialties', true );
$packages = get_post_meta( $spa_id, 'packages', true );
$reviews = get_post_meta( $spa_id, 'reviews', true );
$images = get_post_meta( $spa_id, 'images', true );
$popular = get_post_meta( $spa_id, 'popular', true );
$tags = !empty($popular) ? $popular : get_post_meta( $spa_id, 'tags', true ); // Use popular if available, otherwise fall back to tags
$transportation = get_post_meta( $spa_id, 'transportation', true );
$nearby_attractions = get_post_meta( $spa_id, 'nearby_attractions', true );
$sustainability = get_post_meta( $spa_id, 'sustainability', true );
$awards = get_post_meta( $spa_id, 'awards', true );
$accessibility = get_post_meta( $spa_id, 'accessibility', true );
$faq = get_post_meta( $spa_id, 'faq', true );
$pros_cons = get_post_meta( $spa_id, 'pros_cons', true );

// Calculate average rating using the dedicated function
$rating_data = spasinbarcelona_calculate_rating(!empty($reviews['review_sources']) ? $reviews['review_sources'] : array());
$avg_rating = $rating_data['rating'];
$review_count = $rating_data['count'];
?>

<article id="post-<?php the_ID(); ?>" <?php post_class( 'spa-single' ); ?>>
    <header class="page-header">
        <div class="page-title-wrapper">
            <h1 class="page-title"><?php the_title(); ?></h1>
        </div>
        <div class="archive-description">
            <?php if ( ! empty( $location['address'] ) || ! empty( $location['city'] ) ) : ?>
                <div class="spa-location">
                    <i class="fas fa-map-marker-alt"></i>
                    <?php
                    $address_parts = array();

                    if ( ! empty( $location['address'] ) ) {
                        $address_parts[] = $location['address'];
                    }

                    if ( ! empty( $location['city'] ) ) {
                        $address_parts[] = $location['city'];
                    }

                    if ( ! empty( $location['country'] ) ) {
                        $address_parts[] = $location['country'];
                    }

                    echo esc_html( implode( ', ', $address_parts ) );
                    ?>
                </div>
            <?php endif; ?>

            <?php if ( $avg_rating > 0 ) : ?>
                <div class="spa-rating">
                    <div class="spa-header-rating-badge">
                        <span class="spa-header-rating-number"><?php echo number_format($avg_rating, 1); ?></span>
                        <span class="spa-header-rating-star">★</span>
                    </div>
                </div>
            <?php endif; ?>

            <?php if ( ! empty( $tags ) && is_array( $tags ) ) : ?>
                <div class="spa-tags">
                    <?php foreach ( $tags as $tag ) : ?>
                        <span class="spa-tag"><?php echo esc_html( $tag ); ?></span>
                    <?php endforeach; ?>
                </div>
            <?php elseif ( ! empty( $popular ) && is_array( $popular ) ) : ?>
                <div class="spa-tags">
                    <?php foreach ( $popular as $tag ) : ?>
                        <span class="spa-tag"><?php echo esc_html( $tag ); ?></span>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>

            <?php /* Visit Website button removed as requested */ ?>
        </div>
    </header>

    <div class="spa-gallery">
        <?php if ( ! empty( $images ) && is_array( $images ) ) : ?>
            <div class="spa-gallery-grid">
                <?php foreach ( $images as $index => $image ) : ?>
                    <?php if ( ! empty( $image['url'] ) ) : ?>
                        <a href="<?php echo esc_url( $image['url'] ); ?>" class="spa-gallery-item" data-caption="<?php echo esc_attr( ! empty( $image['caption'] ) ? $image['caption'] : '' ); ?>">
                            <img src="<?php echo esc_url( $image['url'] ); ?>" alt="<?php echo esc_attr( ! empty( $image['alt'] ) ? $image['alt'] : get_the_title() ); ?>">
                            <div class="spa-gallery-item-overlay">
                                <span class="spa-gallery-zoom"><i class="fas fa-search-plus"></i></span>
                            </div>
                        </a>
                    <?php endif; ?>
                <?php endforeach; ?>
            </div>
        <?php elseif ( has_post_thumbnail() ) : ?>
            <div class="spa-gallery-grid">
                <a href="<?php echo esc_url( get_the_post_thumbnail_url( $spa_id, 'large' ) ); ?>" class="spa-gallery-item" data-caption="<?php the_title_attribute(); ?>">
                    <?php the_post_thumbnail( 'large' ); ?>
                    <div class="spa-gallery-item-overlay">
                        <span class="spa-gallery-zoom"><i class="fas fa-search-plus"></i></span>
                    </div>
                </a>
            </div>
        <?php endif; ?>
    </div>

    <div class="spa-content-wrapper">
            <!-- Overview Section -->
            <section id="overview" class="spa-section-content">
                <div class="spa-card-container">
                    <h2 class="spa-section-title">About <?php the_title(); ?></h2>
                    <div class="spa-description">
                        <?php the_content(); ?>

                        <?php
                        // Store neighborhood information for use elsewhere, but don't display it here
                        $neighborhood_terms = get_the_terms($spa_id, 'spa_neighborhood');
                        $neighborhood_name = '';

                        if (!empty($neighborhood_terms) && !is_wp_error($neighborhood_terms)) {
                            foreach ($neighborhood_terms as $term) {
                                // Use the first neighborhood term we find
                                if (empty($neighborhood_name)) {
                                    $neighborhood_name = $term->name;
                                }
                            }
                        }

                        // Fallback to meta values if taxonomy terms aren't available
                        if (empty($neighborhood_name) && !empty($location['neighborhood'])) {
                            $neighborhood_name = $location['neighborhood'];
                        }
                        ?>

                        <?php if ( ! empty( $contact['phone'] ) || ! empty( $contact['email'] ) || ! empty( $contact['website'] ) ) : ?>
                        <div class="spa-contact-text">
                            <?php if ( ! empty( $contact['phone'] ) ) : ?>
                                <p><i class="fas fa-phone"></i> <?php echo esc_html( $contact['phone'] ); ?></p>
                            <?php endif; ?>

                            <?php if ( ! empty( $contact['email'] ) ) : ?>
                                <p><i class="fas fa-envelope"></i> <?php echo esc_html( $contact['email'] ); ?></p>
                            <?php endif; ?>

                            <?php if ( ! empty( $contact['website'] ) ) : ?>
                                <p><i class="fas fa-globe"></i> <?php echo esc_html( preg_replace( '#^https?://#', '', $contact['website'] ) ); ?></p>
                            <?php endif; ?>
                        </div>
                        <?php endif; ?>
                    </div>

                    <div class="spa-about-details">
                        <div class="spa-hours">
                            <h3 class="spa-subsection-title">Opening Hours</h3>
                            <ul class="spa-hours-list">
                                <?php
                                // Define the days of the week in order
                                $days_of_week = array(
                                    'monday' => 'Monday',
                                    'tuesday' => 'Tuesday',
                                    'wednesday' => 'Wednesday',
                                    'thursday' => 'Thursday',
                                    'friday' => 'Friday',
                                    'saturday' => 'Saturday',
                                    'sunday' => 'Sunday'
                                );

                                // Initialize hours data
                                $hours_data = array();

                                // Parse from hoursDisplay if available
                                if (!empty($hoursDisplay)) {
                                    // Try to extract day-specific information from hoursDisplay
                                    $day_patterns = array(
                                        'monday' => '/\b(mon|monday)\b/i',
                                        'tuesday' => '/\b(tue|tues|tuesday)\b/i',
                                        'wednesday' => '/\b(wed|wednesday)\b/i',
                                        'thursday' => '/\b(thu|thur|thurs|thursday)\b/i',
                                        'friday' => '/\b(fri|friday)\b/i',
                                        'saturday' => '/\b(sat|saturday)\b/i',
                                        'sunday' => '/\b(sun|sunday)\b/i'
                                    );

                                    $hours_pattern = '/(\d{1,2}:\d{2}\s*(?:AM|PM))\s*[-–]\s*(\d{1,2}:\d{2}\s*(?:AM|PM))/i';
                                    $closed_pattern = '/\bclosed\b/i';

                                    // Split by semicolons if multiple day ranges
                                    $hours_parts = explode(';', $hoursDisplay);

                                    foreach ($hours_parts as $part) {
                                        $part = trim($part);
                                        if (empty($part)) continue;

                                        // Check which days this part applies to
                                        foreach ($day_patterns as $day_key => $pattern) {
                                            if (preg_match($pattern, $part)) {
                                                // This part applies to this day
                                                if (preg_match($closed_pattern, $part)) {
                                                    $hours_data[$day_key] = 'Closed';
                                                } elseif (preg_match($hours_pattern, $part, $matches)) {
                                                    $hours_data[$day_key] = $matches[0]; // Full hours range
                                                } else {
                                                    $hours_data[$day_key] = 'Hours not specified';
                                                }
                                            }
                                        }
                                    }

                                    // Handle day ranges like "Mon-Fri"
                                    foreach ($hours_parts as $part) {
                                        $part = trim($part);
                                        if (empty($part)) continue;

                                        // Look for day ranges
                                        if (preg_match('/\b(mon|monday|tue|tues|tuesday|wed|wednesday|thu|thur|thurs|thursday|fri|friday|sat|saturday|sun|sunday)\s*[-–]\s*(mon|monday|tue|tues|tuesday|wed|wednesday|thu|thur|thurs|thursday|fri|friday|sat|saturday|sun|sunday)\b/i', $part, $range_matches)) {
                                            $start_day = strtolower($range_matches[1]);
                                            $end_day = strtolower($range_matches[2]);

                                            // Map abbreviated days to full day keys
                                            $day_map = array(
                                                'mon' => 'monday', 'monday' => 'monday',
                                                'tue' => 'tuesday', 'tues' => 'tuesday', 'tuesday' => 'tuesday',
                                                'wed' => 'wednesday', 'wednesday' => 'wednesday',
                                                'thu' => 'thursday', 'thur' => 'thursday', 'thurs' => 'thursday', 'thursday' => 'thursday',
                                                'fri' => 'friday', 'friday' => 'friday',
                                                'sat' => 'saturday', 'saturday' => 'saturday',
                                                'sun' => 'sunday', 'sunday' => 'sunday'
                                            );

                                            if (isset($day_map[$start_day]) && isset($day_map[$end_day])) {
                                                $start_day = $day_map[$start_day];
                                                $end_day = $day_map[$end_day];

                                                // Get the hours for this range
                                                $range_hours = 'Hours not specified';
                                                if (preg_match($closed_pattern, $part)) {
                                                    $range_hours = 'Closed';
                                                } elseif (preg_match($hours_pattern, $part, $matches)) {
                                                    $range_hours = $matches[0];
                                                }

                                                // Apply to all days in the range
                                                $in_range = false;
                                                foreach ($days_of_week as $day_key => $day_name) {
                                                    if ($day_key === $start_day) {
                                                        $in_range = true;
                                                    }

                                                    if ($in_range && !isset($hours_data[$day_key])) {
                                                        $hours_data[$day_key] = $range_hours;
                                                    }

                                                    if ($day_key === $end_day) {
                                                        $in_range = false;
                                                    }
                                                }

                                                // Handle wrap-around ranges (e.g., Sun-Wed)
                                                if ($start_day === 'sunday' && $end_day !== 'sunday') {
                                                    $in_range = true;
                                                    foreach ($days_of_week as $day_key => $day_name) {
                                                        if ($in_range && !isset($hours_data[$day_key])) {
                                                            $hours_data[$day_key] = $range_hours;
                                                        }

                                                        if ($day_key === $end_day) {
                                                            $in_range = false;
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }

                                // Use hours array data if available and not already set from hoursDisplay
                                if (!empty($hours) && is_array($hours)) {
                                    foreach ($hours as $day_key => $time) {
                                        if (!isset($hours_data[$day_key])) {
                                            if (is_array($time)) {
                                                if (!empty($time['isClosed']) && $time['isClosed']) {
                                                    $hours_data[$day_key] = 'Closed';
                                                } elseif (!empty($time['open']) && !empty($time['close'])) {
                                                    $hours_data[$day_key] = $time['open'] . ' - ' . $time['close'];
                                                } else {
                                                    $hours_data[$day_key] = 'Hours not specified';
                                                }
                                            } else {
                                                $hours_data[$day_key] = $time;
                                            }
                                        }
                                    }
                                }

                                // Display all seven days, regardless of data availability
                                foreach ($days_of_week as $day_key => $day_name) :
                                    $time_display = isset($hours_data[$day_key]) ? $hours_data[$day_key] : 'Hours not specified';
                                ?>
                                    <li>
                                        <span class="spa-day"><?php echo esc_html($day_name); ?>:</span>
                                        <span class="spa-time"><?php echo esc_html($time_display); ?></span>
                                    </li>
                                <?php endforeach; ?>
                            </ul>
                        </div>



                        <?php if ( ! empty( $social_media ) && is_array( $social_media ) ) : ?>
                            <div class="spa-social">
                                <h3 class="spa-subsection-title">Social Media</h3>
                                <div class="spa-social-links">
                                    <?php foreach ( $social_media as $platform => $url ) : ?>
                                        <?php if ( ! empty( $url ) ) : ?>
                                            <a href="<?php echo esc_url( $url ); ?>" target="_blank" class="spa-social-link <?php echo esc_attr( $platform ); ?>" aria-label="<?php echo esc_attr( ucfirst( $platform ) ); ?>">
                                                <i class="fab fa-<?php echo esc_attr( $platform === 'x' ? 'twitter' : $platform ); ?>"></i>
                                            </a>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </section>

            <?php if ( ! empty( $specialties ) && is_array( $specialties ) ) : ?>
            <!-- Specialties Section -->
            <section id="specialties" class="spa-section-content">
                <div class="spa-card-container">
                    <h2 class="spa-section-title">Specialties</h2>
                    <div class="spa-specialties">
                        <ul class="spa-specialties-list">
                            <?php foreach ( $specialties as $specialty ) :
                                // Determine the appropriate icon based on the specialty
                                $icon = 'fa-gem'; // Default icon

                                // Check for common specialty keywords and assign appropriate icons
                                $specialty_lower = strtolower($specialty);

                                if (strpos($specialty_lower, 'couple') !== false || strpos($specialty_lower, 'romantic') !== false) {
                                    $icon = 'fa-heart';
                                } elseif (strpos($specialty_lower, 'personalized') !== false || strpos($specialty_lower, 'custom') !== false) {
                                    $icon = 'fa-magic';
                                } elseif (strpos($specialty_lower, 'private') !== false || strpos($specialty_lower, 'exclusive') !== false) {
                                    $icon = 'fa-key';
                                } elseif (strpos($specialty_lower, 'massage') !== false) {
                                    $icon = 'fa-hands';
                                } elseif (strpos($specialty_lower, 'pregnant') !== false || strpos($specialty_lower, 'maternity') !== false) {
                                    $icon = 'fa-baby';
                                } elseif (strpos($specialty_lower, 'facial') !== false || strpos($specialty_lower, 'face') !== false) {
                                    $icon = 'fa-smile';
                                } elseif (strpos($specialty_lower, 'body') !== false) {
                                    $icon = 'fa-child';
                                } elseif (strpos($specialty_lower, 'ayurved') !== false) {
                                    $icon = 'fa-leaf';
                                } elseif (strpos($specialty_lower, 'thai') !== false) {
                                    $icon = 'fa-spa';
                                } elseif (strpos($specialty_lower, 'detox') !== false || strpos($specialty_lower, 'cleanse') !== false) {
                                    $icon = 'fa-tint';
                                } elseif (strpos($specialty_lower, 'aromatherapy') !== false) {
                                    $icon = 'fa-air-freshener';
                                } elseif (strpos($specialty_lower, 'stone') !== false) {
                                    $icon = 'fa-circle';
                                } elseif (strpos($specialty_lower, 'group') !== false) {
                                    $icon = 'fa-users';
                                }
                            ?>
                                <li>
                                    <i class="fas <?php echo esc_attr($icon); ?>"></i>
                                    <span><?php echo esc_html( $specialty ); ?></span>
                                </li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                </div>
            </section>
            <?php endif; ?>

            <!-- Services & Amenities Section -->
            <section id="services" class="spa-section-content">
                <div class="spa-card-container">
                    <h2 class="spa-section-title">Services & Amenities</h2>
                    <div class="spa-services-amenities">
                        <?php if ( ! empty( $services ) && is_array( $services ) ) : ?>
                            <div class="spa-services">
                                <h3 class="spa-subsection-title">Services</h3>
                                <div class="spa-services-container">
                                    <?php foreach ( $services as $service ) : ?>
                                        <div class="spa-service-item">
                                            <h4 class="spa-service-title"><i class="fas fa-spa"></i> <?php echo esc_html( $service ); ?></h4>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php endif; ?>

                        <?php if ( ! empty( $amenities ) && is_array( $amenities ) ) : ?>
                            <div class="spa-amenities">
                                <h3 class="spa-subsection-title">Amenities</h3>
                                <div class="spa-amenities-container">
                                    <?php foreach ( $amenities as $amenity ) : ?>
                                        <div class="spa-amenity-item">
                                            <h4 class="spa-amenity-title"><i class="fas fa-check-circle"></i> <?php echo esc_html( $amenity ); ?></h4>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </section>

            <!-- Packages Section -->
            <section id="packages" class="spa-section-content">
                <div class="spa-card-container">
                    <h2 class="spa-section-title">Packages & Treatments</h2>
                    <?php if ( ! empty( $packages ) && is_array( $packages ) ) : ?>
                        <div class="spa-packages">
                            <?php foreach ( $packages as $package ) : ?>
                                <div class="spa-package">
                                    <?php if ( is_array( $package ) ) : ?>
                                        <!-- Handle array format (with name, description, price, duration keys) -->
                                        <h4 class="spa-package-title"><?php echo esc_html( $package['name'] ); ?></h4>

                                        <?php if ( ! empty( $package['description'] ) ) : ?>
                                            <div class="spa-package-description">
                                                <?php echo esc_html( $package['description'] ); ?>
                                            </div>
                                        <?php endif; ?>

                                        <div class="spa-package-details">
                                            <?php if ( ! empty( $package['price'] ) ) : ?>
                                                <div class="spa-package-price">
                                                    <i class="fas fa-tag"></i>
                                                    <?php
                                                    // Handle both numeric price (new format) and string price (old format)
                                                    if (is_numeric($package['price'])) {
                                                        $currency = !empty($package['currency']) ? $package['currency'] : 'EUR';
                                                        $price_display = $currency === 'EUR' ? '€' . number_format($package['price'], 2) : number_format($package['price'], 2) . ' ' . $currency;

                                                        // Add price type if available
                                                        if (!empty($package['priceType']) && $package['priceType'] === 'fixed') {
                                                            echo esc_html($price_display);
                                                        } else {
                                                            echo esc_html($price_display . ' ' . (!empty($package['priceType']) ? '(' . $package['priceType'] . ')' : ''));
                                                        }
                                                    } else {
                                                        echo esc_html($package['price']);
                                                    }
                                                    ?>
                                                </div>
                                            <?php endif; ?>

                                            <?php if ( ! empty( $package['durationMinutes'] ) || ! empty( $package['durationDisplay'] ) || ! empty( $package['duration'] ) ) : ?>
                                                <div class="spa-package-duration">
                                                    <i class="far fa-clock"></i>
                                                    <?php
                                                    // Handle both new and old duration formats
                                                    if (!empty($package['durationDisplay'])) {
                                                        echo esc_html($package['durationDisplay']);
                                                    } elseif (!empty($package['durationMinutes'])) {
                                                        $hours = floor($package['durationMinutes'] / 60);
                                                        $minutes = $package['durationMinutes'] % 60;

                                                        if ($hours > 0) {
                                                            echo esc_html($hours . ' hour' . ($hours > 1 ? 's' : ''));
                                                            if ($minutes > 0) {
                                                                echo esc_html(' ' . $minutes . ' min');
                                                            }
                                                        } else {
                                                            echo esc_html($minutes . ' minutes');
                                                        }
                                                    } else {
                                                        echo esc_html($package['duration']);
                                                    }
                                                    ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    <?php else : ?>
                                        <!-- Handle string format -->
                                        <h4 class="spa-package-title"><?php echo esc_html( $package ); ?></h4>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else : ?>
                        <p class="spa-no-content">No packages or treatments information available. Please contact the spa directly for details.</p>
                    <?php endif; ?>
                </div>
            </section>

            <!-- Testimonials Section -->
            <section id="reviews" class="spa-section-content spa-testimonials-section">
                <div class="spa-card-container">
                    <h2 class="spa-section-title spa-testimonials-title">Client Testimonials</h2>
                <?php if ( ! empty( $reviews ) ) : ?>
                    <div class="spa-reviews">
                        <?php if ( ! empty( $reviews['review_sources'] ) && is_array( $reviews['review_sources'] ) ) : ?>
                            <div class="spa-review-sources">
                                <h3 class="spa-subsection-title">Ratings from Review Sites</h3>
                                <div class="spa-review-sources-grid">
                                    <?php foreach ( $reviews['review_sources'] as $source ) : ?>
                                        <div class="spa-review-source">
                                            <div class="spa-review-source-name">
                                                <?php echo esc_html( $source['source'] ); ?>
                                            </div>
                                            <div class="spa-review-source-rating">
                                                <?php echo spasinbarcelona_star_rating( $source['rating'] ); ?>
                                                <?php /* Review count removed as requested */ ?>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php endif; ?>

                        <?php if ( ! empty( $reviews['featured_reviews'] ) && is_array( $reviews['featured_reviews'] ) ) : ?>
                            <div class="spa-featured-reviews">
                                <h3 class="spa-subsection-title">What Our Clients Say</h3>
                                <div class="spa-testimonials-grid">
                                    <?php foreach ( $reviews['featured_reviews'] as $review ) : ?>
                                        <div class="spa-testimonial-card">
                                            <div class="spa-testimonial-content">
                                                "<?php echo esc_html( $review['text'] ); ?>"
                                            </div>

                                            <div class="spa-testimonial-footer">
                                                <div class="spa-testimonial-author">
                                                    <div class="spa-testimonial-avatar">
                                                        <i class="fas fa-user"></i>
                                                    </div>
                                                    <div class="spa-testimonial-author-info">
                                                        <span class="spa-testimonial-author-name">
                                                            <?php echo esc_html( $review['author'] ); ?>
                                                        </span>

                                                        <?php if ( ! empty( $review['date'] ) ) : ?>
                                                            <span class="spa-testimonial-date">
                                                                <?php echo esc_html( $review['date'] ); ?>
                                                            </span>
                                                        <?php endif; ?>

                                                        <?php if ( ! empty( $review['source'] ) ) : ?>
                                                            <span class="spa-testimonial-source">
                                                                via <?php echo esc_html( $review['source'] ); ?>
                                                            </span>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>

                                                <?php if ( ! empty( $review['rating'] ) ) : ?>
                                                    <div class="spa-testimonial-rating">
                                                        <div class="spa-testimonial-stars">
                                                            <?php
                                                            $rating = intval($review['rating']);
                                                            for ($i = 1; $i <= 5; $i++) {
                                                                if ($i <= $rating) {
                                                                    echo '<i class="fas fa-star"></i>';
                                                                } else {
                                                                    echo '<i class="far fa-star"></i>';
                                                                }
                                                            }
                                                            ?>
                                                        </div>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php else : ?>
                    <p class="spa-no-content">No testimonials available yet.</p>
                <?php endif; ?>
                </div>
            </section>

            <!-- Location & Neighborhood Section -->
            <section id="location" class="spa-section-content">
                <div class="spa-card-container">
                    <h2 class="spa-section-title">Location & Neighborhood</h2>
                    <div class="spa-location-details">
                        <?php if ( ! empty( $location['address'] ) || ! empty( $location['city'] ) ) : ?>
                            <div class="spa-address">
                                <h3 class="spa-subsection-title">Address</h3>
                                <div class="spa-address-content">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <div class="spa-address-text">
                                        <?php
                                        $address_parts = array();

                                        if ( ! empty( $location['address'] ) ) {
                                            $address_parts[] = $location['address'];
                                        }

                                        if ( ! empty( $location['city'] ) ) {
                                            $address_parts[] = $location['city'];
                                        }

                                        if ( ! empty( $location['country'] ) ) {
                                            $address_parts[] = $location['country'];
                                        }

                                        echo esc_html( implode( ', ', $address_parts ) );
                                        ?>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>

                        <?php
                        // Get neighborhood information
                        $neighborhood_name = '';
                        $neighborhood_terms = get_the_terms($spa_id, 'spa_neighborhood');

                        if (!empty($neighborhood_terms) && !is_wp_error($neighborhood_terms)) {
                            foreach ($neighborhood_terms as $term) {
                                // Use the first neighborhood term we find
                                if (empty($neighborhood_name)) {
                                    $neighborhood_name = $term->name;
                                }
                            }
                        }

                        // Fallback to meta values if taxonomy terms aren't available
                        if (empty($neighborhood_name) && !empty($location['neighborhood'])) {
                            $neighborhood_name = $location['neighborhood'];
                        }
                        ?>

                        <?php if (!empty($neighborhood_name)) : ?>
                            <div class="spa-neighborhood">
                                <h3 class="spa-subsection-title">Neighborhood Information</h3>
                                <div class="spa-neighborhood-content">
                                    <i class="fas fa-map"></i>
                                    <div class="spa-neighborhood-text">
                                        <div class="spa-neighborhood-header">
                                            <span class="spa-neighborhood-name">
                                                <?php echo esc_html($neighborhood_name); ?>
                                            </span>
                                        </div>

                                        <div class="spa-neighborhood-description">
                                            <p>
                                                <?php
                                                // Display neighborhood description if available, otherwise generate one
                                                if (!empty($location['neighborhood_description'])) {
                                                    echo esc_html($location['neighborhood_description']);
                                                } else {
                                                    echo 'Looking for a spa near me in ' . esc_html($neighborhood_name) . '? ' .
                                                         esc_html(get_the_title()) . ' is conveniently located in the ' .
                                                         esc_html($neighborhood_name) . ' area of Barcelona, making it easily accessible for both locals and visitors seeking relaxation services nearby.';
                                                }
                                                ?>
                                            </p>

                                            <p class="spa-near-me-text">
                                                <i class="fas fa-search-location"></i>
                                                <a href="<?php echo esc_url(get_site_url() . '/neighborhoods/' . sanitize_title($neighborhood_name) . '/'); ?>">
                                                    Find more spas near me in <?php echo esc_html($neighborhood_name); ?>
                                                </a>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>

                        <?php if (!empty($nearby_attractions) && is_array($nearby_attractions)) : ?>
                            <div class="spa-nearby-landmarks">
                                <h3 class="spa-subsection-title">Nearby Landmarks & Attractions</h3>
                                <div class="spa-attractions-grid">
                                    <?php foreach ($nearby_attractions as $attraction) : ?>
                                        <?php if (is_array($attraction)) : ?>
                                        <div class="spa-attraction-card">
                                            <!-- Handle array format (with name, streetAddress, description, etc. keys) -->
                                            <h3 class="spa-attraction-name"><?php echo esc_html($attraction['name']); ?></h3>

                                            <?php if (!empty($attraction['travelTime'])) : ?>
                                                <div class="spa-attraction-distance">
                                                    <i class="fas fa-map-marker-alt"></i> <?php echo esc_html($attraction['travelTime']); ?>
                                                </div>
                                            <?php endif; ?>

                                            <?php if (!empty($attraction['description'])) : ?>
                                                <div class="spa-attraction-description">
                                                    <?php echo esc_html($attraction['description']); ?>
                                                </div>
                                            <?php endif; ?>

                                            <div class="spa-attraction-details">
                                                <?php if (!empty($attraction['streetAddress'])) : ?>
                                                    <div class="spa-attraction-address">
                                                        <i class="fas fa-map-pin"></i> <?php echo esc_html($attraction['streetAddress']); ?>
                                                    </div>
                                                <?php endif; ?>

                                                <?php if (!empty($attraction['category'])) : ?>
                                                    <div class="spa-attraction-category">
                                                        <i class="fas fa-tag"></i> <?php echo esc_html($attraction['category']); ?>
                                                    </div>
                                                <?php endif; ?>

                                                <?php
                                                // Display if the attraction is free or not
                                                if (isset($attraction['isAccessibleForFree'])) :
                                                    $is_free = $attraction['isAccessibleForFree'] ? true : false;
                                                ?>
                                                    <div class="spa-attraction-fee">
                                                        <?php if ($is_free) : ?>
                                                            <i class="fas fa-ticket-alt"></i> <span class="spa-free-entry">Free entry</span>
                                                        <?php else : ?>
                                                            <i class="fas fa-ticket-alt"></i> <span class="spa-paid-entry">Paid entry</span>
                                                        <?php endif; ?>
                                                    </div>
                                                <?php endif; ?>
                                            </div>

                                            <?php
                                            // Add Google Maps link if coordinates are available
                                            if (!empty($attraction['geo']) && !empty($attraction['geo']['latitude']) && !empty($attraction['geo']['longitude'])) :
                                                $maps_url = 'https://www.google.com/maps/search/?api=1&query=' .
                                                            urlencode($attraction['geo']['latitude'] . ',' . $attraction['geo']['longitude']);
                                            ?>
                                                <div class="spa-attraction-map-link">
                                                    <a href="<?php echo esc_url($maps_url); ?>" target="_blank" rel="noopener noreferrer">
                                                        <i class="fas fa-directions"></i> View on Google Maps
                                                    </a>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        <?php else : ?>
                                        <div class="spa-attraction-card">
                                            <!-- Handle string format -->
                                            <h3 class="spa-attraction-name"><?php echo esc_html($attraction); ?></h3>
                                            <div class="spa-attraction-distance">
                                                <i class="fas fa-map-marker-alt"></i> Nearby
                                            </div>
                                        </div>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                </div>

                                <?php if (!empty($neighborhood_name)) : ?>
                                <div class="spa-near-me-text spa-attractions-near-me">
                                    <i class="fas fa-info-circle"></i>
                                    Discover these popular attractions near <?php echo esc_html(get_the_title()); ?> in the <?php echo esc_html($neighborhood_name); ?> area of Barcelona.
                                </div>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>

                        <?php if (!empty($location['coordinates']['latitude']) && !empty($location['coordinates']['longitude'])) :
                            $map_query = urlencode(get_the_title() . ', ' . $location['address'] . ', ' . $location['city']);
                            $directions_url = 'https://www.google.com/maps/dir/?api=1&destination=' .
                                              urlencode($location['coordinates']['latitude'] . ',' . $location['coordinates']['longitude']) .
                                              '&destination_place_id=' . urlencode(get_the_title());
                        ?>
                            <div class="spa-map-section">
                                <h3 class="spa-subsection-title">Map & Directions</h3>
                                <div class="spa-map-container">
                                    <?php
                                    // Get Google Maps API key from theme settings
                                    $google_maps_api_key = get_theme_mod('spasinbarcelona_google_maps_api_key', '');

                                    if (!empty($google_maps_api_key)) :
                                    ?>
                                    <iframe
                                        width="100%"
                                        height="350"
                                        frameborder="0"
                                        style="border:0; border-radius: 12px; margin-top: 20px; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);"
                                        src="https://www.google.com/maps/embed/v1/place?key=<?php echo esc_attr($google_maps_api_key); ?>&q=<?php echo esc_attr($map_query); ?>&center=<?php echo esc_attr($location['coordinates']['latitude'] . ',' . $location['coordinates']['longitude']); ?>"
                                        allowfullscreen
                                        title="Location of <?php echo esc_attr(get_the_title()); ?> in <?php echo !empty($neighborhood_name) ? esc_attr($neighborhood_name) : 'Barcelona'; ?>"
                                        aria-label="Google Maps showing the location of <?php echo esc_attr(get_the_title()); ?>"
                                    ></iframe>
                                    <?php else : ?>
                                    <div class="spa-map-error">
                                        <p><i class="fas fa-exclamation-triangle"></i> Google Maps API key is missing. Please add your API key in the WordPress Customizer under "Spa Directory Settings".</p>
                                    </div>
                                    <?php endif; ?>
                                </div>
                                <div class="spa-directions-link">
                                    <a href="<?php echo esc_url($directions_url); ?>" target="_blank" rel="noopener noreferrer" class="spa-get-directions-button">
                                        <i class="fas fa-directions"></i> Get Directions to <?php echo esc_html(get_the_title()); ?>
                                    </a>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </section>

            <!-- How to Get There Section -->
            <?php if ( ( ! empty( $transportation ) && is_array( $transportation ) ) || ( ! empty( $accessibility ) && is_array( $accessibility ) ) ) : ?>
            <section id="how-to-get-there" class="spa-section-content">
                <div class="spa-card-container">
                    <h2 class="spa-section-title">How to Get There</h2>

                    <?php if (!empty($neighborhood_name)) : ?>
                    <div class="spa-transportation-intro">
                        <p>Looking for the best way to reach <?php echo esc_html(get_the_title()); ?> near me in <?php echo esc_html($neighborhood_name); ?>? Here are all the transportation options to help you get to this spa in <?php echo esc_html($neighborhood_name); ?> quickly and easily.</p>
                    </div>
                    <?php endif; ?>

                    <?php if ( ! empty( $transportation ) && is_array( $transportation ) ) : ?>
                        <h3 class="spa-subsection-title">Transportation Options</h3>
                        <div class="spa-transportation-cards">
                            <?php if ( ! empty( $transportation['metro'] ) ) : ?>
                                <div class="spa-transport-card metro">
                                    <div class="spa-transport-icon">
                                        <i class="fas fa-subway"></i>
                                    </div>
                                    <h4 class="spa-transport-title">Metro</h4>
                                    <div class="spa-transport-details">
                                        <?php echo esc_html( $transportation['metro'] ); ?>
                                        <?php if (!empty($neighborhood_name)) : ?>
                                            <div class="spa-transport-near-me">
                                                <i class="fas fa-info-circle"></i> The closest metro station to this spa in <?php echo esc_html($neighborhood_name); ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <?php if ( ! empty( $transportation['bus'] ) ) : ?>
                                <div class="spa-transport-card bus">
                                    <div class="spa-transport-icon">
                                        <i class="fas fa-bus"></i>
                                    </div>
                                    <h4 class="spa-transport-title">Bus</h4>
                                    <div class="spa-transport-details">
                                        <?php echo esc_html( $transportation['bus'] ); ?>
                                        <?php if (!empty($neighborhood_name)) : ?>
                                            <div class="spa-transport-near-me">
                                                <i class="fas fa-info-circle"></i> Bus options near this <?php echo esc_html($neighborhood_name); ?> spa
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <?php if ( ! empty( $transportation['train'] ) ) : ?>
                                <div class="spa-transport-card train">
                                    <div class="spa-transport-icon">
                                        <i class="fas fa-train"></i>
                                    </div>
                                    <h4 class="spa-transport-title">Train</h4>
                                    <div class="spa-transport-details">
                                        <?php echo esc_html( $transportation['train'] ); ?>
                                        <?php if (!empty($neighborhood_name)) : ?>
                                            <div class="spa-transport-near-me">
                                                <i class="fas fa-info-circle"></i> Train connections to <?php echo esc_html($neighborhood_name); ?> area
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <?php if ( ! empty( $transportation['parking'] ) ) : ?>
                                <div class="spa-transport-card parking">
                                    <div class="spa-transport-icon">
                                        <i class="fas fa-parking"></i>
                                    </div>
                                    <h4 class="spa-transport-title">Parking</h4>
                                    <div class="spa-transport-details">
                                        <?php echo esc_html( $transportation['parking'] ); ?>
                                        <?php if (!empty($neighborhood_name)) : ?>
                                            <div class="spa-transport-near-me">
                                                <i class="fas fa-info-circle"></i> Parking options near this spa in <?php echo esc_html($neighborhood_name); ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>

                        <?php if (!empty($location['coordinates']['latitude']) && !empty($location['coordinates']['longitude'])) : ?>
                        <div class="spa-public-transport-link">
                            <a href="https://www.google.com/maps/dir/?api=1&destination=<?php echo urlencode($location['coordinates']['latitude'] . ',' . $location['coordinates']['longitude']); ?>&travelmode=transit" target="_blank" rel="noopener noreferrer" class="spa-transport-directions-button">
                                <i class="fas fa-route"></i> Get Public Transport Directions
                            </a>
                        </div>
                        <?php endif; ?>
                    <?php endif; ?>

                    <?php if ( ! empty( $accessibility ) && is_array( $accessibility ) ) : ?>
                        <h3 class="spa-subsection-title">Accessibility</h3>
                        <ul class="spa-accessibility-list">
                            <?php if ( isset( $accessibility['wheelchair_accessible'] ) && $accessibility['wheelchair_accessible'] ) : ?>
                                <li><i class="fas fa-wheelchair"></i> <span>Wheelchair accessible</span></li>
                            <?php endif; ?>

                            <?php if ( isset( $accessibility['service_animals_allowed'] ) && $accessibility['service_animals_allowed'] ) : ?>
                                <li><i class="fas fa-paw"></i> <span>Service animals allowed</span></li>
                            <?php endif; ?>

                            <?php if ( isset( $accessibility['accessible_parking'] ) && $accessibility['accessible_parking'] ) : ?>
                                <li><i class="fas fa-parking"></i> <span>Accessible parking</span></li>
                            <?php endif; ?>

                            <?php if ( isset( $accessibility['accessible_rooms'] ) && $accessibility['accessible_rooms'] ) : ?>
                                <li><i class="fas fa-door-open"></i> <span>Accessible rooms</span></li>
                            <?php endif; ?>
                        </ul>
                    <?php endif; ?>
                </div>
            </section>
            <?php endif; ?>

            <?php
            // Check if there are any advice entries in the accessibility data
            $has_advice = false;
            if (!empty($accessibility) && is_array($accessibility)) {
                for ($i = 1; $i <= 9; $i++) {
                    $advice_key = "piece of advice {$i}";
                    if (!empty($accessibility[$advice_key])) {
                        $has_advice = true;
                        break;
                    }
                }
            }
            ?>

            <?php if ($has_advice) : ?>
            <!-- Preparation Tips Section -->
            <section id="preparation-tips" class="spa-section-content">
                <div class="spa-card-container">
                    <h2 class="spa-section-title">How to Prepare for Your Spa Visit</h2>

                    <div class="spa-advice-intro">
                        <p>Planning your visit to <?php echo esc_html(get_the_title()); ?>? Follow these essential preparation tips to maximize your relaxation experience and ensure a seamless spa journey:</p>
                    </div>

                    <div class="spa-advice-cards">
                        <?php
                        // Display all advice entries from accessibility
                        for ($i = 1; $i <= 9; $i++) {
                            $advice_key = "piece of advice {$i}";
                            if (!empty($accessibility[$advice_key])) :
                                // Determine the appropriate icon based on the advice content
                                $advice_text = strtolower($accessibility[$advice_key]);
                                $icon = 'fa-lightbulb'; // Default icon

                                // Check for common advice keywords and assign appropriate icons
                                if (strpos($advice_text, 'arrive') !== false || strpos($advice_text, 'early') !== false || strpos($advice_text, 'time') !== false) {
                                    $icon = 'fa-clock';
                                } elseif (strpos($advice_text, 'water') !== false || strpos($advice_text, 'hydrate') !== false || strpos($advice_text, 'drink') !== false) {
                                    $icon = 'fa-tint';
                                } elseif (strpos($advice_text, 'eat') !== false || strpos($advice_text, 'food') !== false || strpos($advice_text, 'meal') !== false) {
                                    $icon = 'fa-utensils';
                                } elseif (strpos($advice_text, 'wear') !== false || strpos($advice_text, 'clothing') !== false || strpos($advice_text, 'dress') !== false) {
                                    $icon = 'fa-tshirt';
                                } elseif (strpos($advice_text, 'phone') !== false || strpos($advice_text, 'mobile') !== false || strpos($advice_text, 'device') !== false) {
                                    $icon = 'fa-mobile-alt';
                                } elseif (strpos($advice_text, 'shower') !== false || strpos($advice_text, 'bath') !== false || strpos($advice_text, 'clean') !== false) {
                                    $icon = 'fa-shower';
                                } elseif (strpos($advice_text, 'book') !== false || strpos($advice_text, 'reserv') !== false || strpos($advice_text, 'appoint') !== false) {
                                    $icon = 'fa-calendar-check';
                                } elseif (strpos($advice_text, 'medical') !== false || strpos($advice_text, 'health') !== false || strpos($advice_text, 'condition') !== false) {
                                    $icon = 'fa-heartbeat';
                                } elseif (strpos($advice_text, 'relax') !== false || strpos($advice_text, 'stress') !== false || strpos($advice_text, 'calm') !== false) {
                                    $icon = 'fa-spa';
                                }
                        ?>
                            <div class="spa-advice-card">
                                <div class="spa-advice-icon">
                                    <i class="fas <?php echo esc_attr($icon); ?>"></i>
                                </div>
                                <div class="spa-advice-content">
                                    <span><?php echo esc_html($accessibility[$advice_key]); ?></span>
                                </div>
                            </div>
                        <?php
                            endif;
                        }
                        ?>
                    </div>

                    <div class="spa-advice-footer">
                        <p><i class="fas fa-info-circle"></i> Following these preparation guidelines will help you get the most out of your spa treatments and ensure a truly relaxing experience at <?php echo esc_html(get_the_title()); ?>.</p>
                    </div>
                </div>
            </section>
            <?php endif; ?>

            <?php if ( ! empty( $sustainability ) && ( ! empty( $sustainability['practices'] ) || ! empty( $sustainability['certifications'] ) ) ) : ?>
            <!-- Sustainability Section -->
            <section id="sustainability" class="spa-section-content">
                <div class="spa-card-container">
                    <h2 class="spa-section-title">Sustainability</h2>

                <?php if ( ! empty( $sustainability['practices'] ) && is_array( $sustainability['practices'] ) ) : ?>
                    <div class="spa-sustainability-practices">
                        <h3 class="spa-subsection-title">Sustainable Practices</h3>
                        <ul class="spa-sustainability-list">
                            <?php foreach ( $sustainability['practices'] as $practice ) : ?>
                                <li><i class="fas fa-leaf"></i> <span><?php echo esc_html( $practice ); ?></span></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>

                <?php if ( ! empty( $sustainability['certifications'] ) && is_array( $sustainability['certifications'] ) ) : ?>
                    <div class="spa-sustainability-certifications">
                        <h3 class="spa-subsection-title">Certifications</h3>
                        <ul class="spa-certifications-list">
                            <?php foreach ( $sustainability['certifications'] as $certification ) : ?>
                                <li><i class="fas fa-certificate"></i> <span><?php echo esc_html( $certification ); ?></span></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>
                </div>
            </section>
            <?php endif; ?>

            <?php if ( ! empty( $awards ) && is_array( $awards ) ) : ?>
            <!-- Awards Section -->
            <section id="awards" class="spa-section-content">
                <div class="spa-card-container">
                    <h2 class="spa-section-title">Awards & Recognition</h2>
                <div class="spa-awards-list">
                    <?php foreach ( $awards as $award ) : ?>
                        <div class="spa-award">
                            <div class="spa-award-icon">
                                <i class="fas fa-award"></i>
                            </div>
                            <div class="spa-award-details">
                                <?php if ( is_array( $award ) ) : ?>
                                    <!-- Handle array format (with name, year, description keys) -->
                                    <h3 class="spa-award-name"><?php echo esc_html( $award['name'] ); ?></h3>

                                    <?php if ( ! empty( $award['year'] ) ) : ?>
                                        <div class="spa-award-year"><?php echo esc_html( $award['year'] ); ?></div>
                                    <?php endif; ?>

                                    <?php if ( ! empty( $award['description'] ) ) : ?>
                                        <div class="spa-award-description"><?php echo esc_html( $award['description'] ); ?></div>
                                    <?php endif; ?>
                                <?php else : ?>
                                    <!-- Handle string format -->
                                    <h3 class="spa-award-name"><?php echo esc_html( $award ); ?></h3>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                </div>
            </section>
            <?php endif; ?>

            <?php if (!empty($pros_cons) &&
                  ((!empty($pros_cons['pros']) && is_array($pros_cons['pros'])) ||
                   (!empty($pros_cons['cons']) && is_array($pros_cons['cons'])))) : ?>
            <!-- Pros & Cons Section -->
            <section id="pros-cons" class="spa-section-content">
                <div class="spa-card-container">
                    <h2 class="spa-section-title">Pros & Cons</h2>

                    <div class="spa-section-intro">
                        <p>Discover the highlights and considerations for your visit to <?php echo esc_html(get_the_title()); ?>. Our expert assessment helps you make an informed decision about your spa experience.</p>
                    </div>

                    <div class="spa-pros-cons-container">
                        <?php if (!empty($pros_cons['pros']) && is_array($pros_cons['pros'])) : ?>
                        <div class="spa-pros-column">
                            <div class="spa-pros-header">
                                <i class="fas fa-gem"></i>
                                <span>Pros</span>
                            </div>
                            <div class="spa-pros-list">
                                <ul>
                                    <?php foreach ($pros_cons['pros'] as $pro) : ?>
                                        <li><?php echo esc_html($pro); ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        </div>
                        <?php endif; ?>

                        <?php if (!empty($pros_cons['cons']) && is_array($pros_cons['cons'])) : ?>
                        <div class="spa-cons-column">
                            <div class="spa-cons-header">
                                <i class="fas fa-exclamation-circle"></i>
                                <span>Cons</span>
                            </div>
                            <div class="spa-cons-list">
                                <ul>
                                    <?php foreach ($pros_cons['cons'] as $con) : ?>
                                        <li><?php echo esc_html($con); ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </section>
            <?php endif; ?>

            <?php
            // Only display FAQ section if there are valid FAQ items from the JSON data
            if ( ! empty( $faq ) && is_array( $faq ) && count($faq) > 0 ) :
                // Verify that each FAQ item has both question and answer
                $valid_faq_items = array_filter($faq, function($item) {
                    return !empty($item['question']) && !empty($item['answer']);
                });

                if (!empty($valid_faq_items)) :
            ?>
            <!-- FAQ Section -->
            <section id="faq" class="spa-section-content">
                <div class="spa-card-container">
                    <h2 class="spa-section-title">Q&A</h2>
                    <div class="spa-faq-container">
                        <?php foreach ( $valid_faq_items as $item ) : ?>
                            <div class="spa-faq-item">
                                <h3 class="spa-faq-question">
                                    <i class="fas fa-question-circle"></i> <?php echo esc_html( $item['question'] ); ?>
                                </h3>
                                <div class="spa-faq-answer">
                                    <?php echo esc_html( $item['answer'] ); ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </section>
            <?php
                endif;
            endif;
            ?>

            <?php
            // Display nearby spas in the same neighborhood
            $neighborhood_terms = get_the_terms($spa_id, 'spa_neighborhood');
            if (!empty($neighborhood_terms) && !is_wp_error($neighborhood_terms)) {
                $neighborhood_ids = wp_list_pluck($neighborhood_terms, 'term_id');

                // Get spas in the same neighborhood(s)
                $nearby_spas_args = array(
                    'post_type' => 'spa',
                    'posts_per_page' => 4,
                    'post__not_in' => array($spa_id), // Exclude current spa
                    'tax_query' => array(
                        array(
                            'taxonomy' => 'spa_neighborhood',
                            'field' => 'term_id',
                            'terms' => $neighborhood_ids,
                        ),
                    ),
                );

                $nearby_spas = new WP_Query($nearby_spas_args);

                if ($nearby_spas->have_posts()) :
                ?>
                <section id="nearby-spas" class="spa-section-content">
                    <div class="spa-card-container">
                        <h2 class="spa-section-title">Nearby Spas in the Same Neighborhood</h2>
                        <div class="spa-grid">
                            <?php
                            while ($nearby_spas->have_posts()) :
                                $nearby_spas->the_post();
                                get_template_part('template-parts/content', 'spa-card');
                            endwhile;
                            wp_reset_postdata();
                            ?>
                        </div>
                    </div>
                </section>
                <?php
                endif;
            }
            ?>

            <!-- Navigation Buttons -->
            <div class="spa-navigation-buttons">
                <a href="<?php echo esc_url( get_post_type_archive_link( 'spa' ) ); ?>" class="spa-button">
                   View All Spas <i class="fas fa-arrow-right"></i>
                </a>
                <a href="<?php echo esc_url( home_url() ); ?>" class="spa-button">
                    <i class="fas fa-home"></i> Back To Homepage
                </a>
            </div>
    </div>
</article>
