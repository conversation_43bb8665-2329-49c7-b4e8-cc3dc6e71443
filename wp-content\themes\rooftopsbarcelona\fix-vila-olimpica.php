<?php
/**
 * Fix Vila Olímpica Neighborhood
 * 
 * This script specifically fixes the Vila Olímpica neighborhood assignments
 */

// Load WordPress
require_once( dirname( __FILE__ ) . '/../../wp-load.php' );

// Security check - only administrators can access this page
if (!current_user_can('administrator')) {
    wp_die('You do not have permission to access this page.');
}

echo '<h1>Fix Vila Olímpica Neighborhood</h1>';

// First, ensure the Vila Olímpica term exists with the correct name and slug
$vila_olimpica = get_term_by('name', 'Vila Olímpica', 'spa_neighborhood');

if (!$vila_olimpica) {
    // Try to find by slug
    $vila_olimpica = get_term_by('slug', 'vila-olimpica', 'spa_neighborhood');
    
    if (!$vila_olimpica) {
        // Create the term
        $result = wp_insert_term(
            'Vila Olímpica',
            'spa_neighborhood',
            array(
                'description' => 'Discover the best spas in the Vila Olímpica neighborhood of Barcelona. This modern area near the beach offers luxury spa experiences with sea views.',
                'slug' => 'vila-olimpica'
            )
        );
        
        if (is_wp_error($result)) {
            echo '<div style="background: #ffd1d1; padding: 10px; margin: 10px 0; border: 1px solid #a00000;">';
            echo '<p>Error creating Vila Olímpica term: ' . $result->get_error_message() . '</p>';
            echo '</div>';
            die();
        } else {
            $vila_olimpica_id = $result['term_id'];
            echo '<div style="background: #d1ffd1; padding: 10px; margin: 10px 0; border: 1px solid #00a000;">';
            echo '<p>Created Vila Olímpica term with ID: ' . $vila_olimpica_id . '</p>';
            echo '</div>';
            
            // Get the term object
            $vila_olimpica = get_term($vila_olimpica_id, 'spa_neighborhood');
        }
    } else {
        // Update the term to ensure correct name with accent
        wp_update_term(
            $vila_olimpica->term_id,
            'spa_neighborhood',
            array(
                'name' => 'Vila Olímpica',
                'description' => 'Discover the best spas in the Vila Olímpica neighborhood of Barcelona. This modern area near the beach offers luxury spa experiences with sea views.'
            )
        );
        
        echo '<div style="background: #d1ffd1; padding: 10px; margin: 10px 0; border: 1px solid #00a000;">';
        echo '<p>Updated Vila Olímpica term with ID: ' . $vila_olimpica->term_id . '</p>';
        echo '</div>';
        
        // Refresh the term object
        $vila_olimpica = get_term($vila_olimpica->term_id, 'spa_neighborhood');
    }
}

// Now find all spas that should be in Vila Olímpica
$spas_to_check = array(
    'the-spa-at-mandarin-oriental-barcelona' => 'The Spa at Mandarin Oriental Barcelona',
    // Add more spas here if needed
);

$assigned_count = 0;

foreach ($spas_to_check as $slug => $name) {
    // Get the spa by slug
    $spa = get_page_by_path($slug, OBJECT, 'spa');
    
    if (!$spa) {
        echo '<div style="background: #fff3d1; padding: 10px; margin: 10px 0; border: 1px solid #dda000;">';
        echo '<p>Spa not found: ' . esc_html($name) . ' (slug: ' . $slug . ')</p>';
        echo '</div>';
        continue;
    }
    
    // Check if the spa has location meta with Vila Olímpica
    $location = get_post_meta($spa->ID, 'location', true);
    
    echo '<div style="background: #e0f0ff; padding: 10px; margin: 10px 0; border: 1px solid #0073aa;">';
    echo '<h3>Checking spa: ' . esc_html($spa->post_title) . ' (ID: ' . $spa->ID . ')</h3>';
    
    if (!empty($location)) {
        echo '<p>Location data: ' . (is_array($location) ? print_r($location, true) : esc_html($location)) . '</p>';
    } else {
        echo '<p>No location data found.</p>';
        
        // Create location data
        $location = array(
            'address' => '',
            'city' => 'Barcelona',
            'state' => 'Catalonia',
            'country' => 'Spain',
            'postal_code' => '',
            'neighborhood' => 'Vila Olímpica',
            'district' => 'Sant Martí'
        );
        
        update_post_meta($spa->ID, 'location', $location);
        echo '<p>Created new location data with Vila Olímpica neighborhood.</p>';
    }
    
    // Ensure the neighborhood is set to Vila Olímpica
    if (empty($location['neighborhood']) || strtolower($location['neighborhood']) !== 'vila olímpica') {
        $location['neighborhood'] = 'Vila Olímpica';
        update_post_meta($spa->ID, 'location', $location);
        echo '<p>Updated location data with Vila Olímpica neighborhood.</p>';
    }
    
    // Assign the Vila Olímpica term to the spa
    $result = wp_set_object_terms($spa->ID, $vila_olimpica->term_id, 'spa_neighborhood', true);
    
    if (is_wp_error($result)) {
        echo '<p>Error assigning Vila Olímpica term: ' . $result->get_error_message() . '</p>';
    } else {
        echo '<p>Successfully assigned Vila Olímpica term to spa.</p>';
        $assigned_count++;
    }
    
    // Get current terms for verification
    $current_terms = wp_get_object_terms($spa->ID, 'spa_neighborhood');
    
    if (!empty($current_terms) && !is_wp_error($current_terms)) {
        echo '<p>Current neighborhood terms:</p>';
        echo '<ul>';
        foreach ($current_terms as $term) {
            echo '<li>' . esc_html($term->name) . ' (ID: ' . $term->term_id . ', Slug: ' . $term->slug . ')</li>';
        }
        echo '</ul>';
    } else {
        echo '<p>No neighborhood terms currently assigned.</p>';
    }
    
    echo '</div>';
}

// Flush rewrite rules
flush_rewrite_rules();

echo '<div style="background: #d1ffd1; padding: 10px; margin: 10px 0; border: 1px solid #00a000;">';
echo '<p><strong>Fixed ' . $assigned_count . ' spa assignments to Vila Olímpica.</strong></p>';
echo '</div>';

// Add links for testing
echo '<p><a href="' . esc_url(home_url('/spa-neighborhood/vila-olimpica/')) . '" target="_blank">Test Vila Olímpica Neighborhood Page</a></p>';
echo '<p><a href="' . esc_url(home_url('/wp-content/themes/spasinbarcelona/debug-neighborhoods.php')) . '">Back to Neighborhood Debug</a></p>';
