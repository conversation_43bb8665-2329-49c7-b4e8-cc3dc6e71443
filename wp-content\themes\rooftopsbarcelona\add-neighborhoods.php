<?php
/**
 * <PERSON><PERSON><PERSON> to add neighborhood terms
 */

// Load WordPress
require_once( dirname( __FILE__ ) . '/../../wp-load.php' );

// List of neighborhoods to add
$neighborhoods = array(
    'Gothic Quarter' => 'The Gothic Quarter (Barri Gòtic) is the center of the old city of Barcelona. It stretches from La Rambla to Via Laietana, and from the Mediterranean seafront to Ronda de Sant Pere.',
    'Sants' => 'Sants is a neighborhood in the southern part of Barcelona, known for its working-class roots and vibrant local atmosphere.',
    'Eixample' => 'Eixample is a district of Barcelona characterized by its grid-like street pattern with long straight streets, wide avenues, and square blocks with chamfered corners.',
    'Vila Olímpica' => 'Vila Olímpica is a neighborhood built for the 1992 Olympic Games, featuring modern architecture and beautiful beaches.',
    'Les Corts' => 'Les Corts is an upscale district in Barcelona, home to FC Barcelona\'s Camp Nou stadium and numerous business centers.',
    'Pedralbes' => 'Pedralbes is one of the most exclusive and affluent neighborhoods in Barcelona, known for its luxury residences and green spaces.',
    'La Barceloneta' => 'La Barceloneta is a seaside neighborhood with a traditional fishing village atmosphere, famous for its beaches and seafood restaurants.',
    'Gràcia' => 'Gràcia is a charming district known for its narrow streets, plazas, and bohemian atmosphere, with a strong local identity.'
);

// Add each neighborhood
foreach ($neighborhoods as $name => $description) {
    // Check if the term already exists
    $term = term_exists($name, 'spa_neighborhood');
    
    if (!$term) {
        // Create the term
        $result = wp_insert_term(
            $name,
            'spa_neighborhood',
            array(
                'description' => $description,
                'slug' => sanitize_title($name)
            )
        );
        
        if (is_wp_error($result)) {
            echo "Error creating term {$name}: " . $result->get_error_message() . "<br>";
        } else {
            echo "Created term {$name} successfully.<br>";
        }
    } else {
        echo "Term {$name} already exists.<br>";
        
        // Update the description if it's empty
        $term_obj = get_term($term['term_id'], 'spa_neighborhood');
        if (empty($term_obj->description)) {
            wp_update_term(
                $term['term_id'],
                'spa_neighborhood',
                array('description' => $description)
            );
            echo "Updated description for {$name}.<br>";
        }
    }
}

echo "Neighborhood terms setup complete.";
