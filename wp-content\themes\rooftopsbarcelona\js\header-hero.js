/**
 * <PERSON>er and Hero Section JavaScript
 */

(function($) {
    'use strict';

    // DOM ready
    $(function() {
        // Sticky Header
        var header = $('.site-header');
        var headerHeight = header.outerHeight();
        var scrollPosition = $(window).scrollTop();

        function handleStickyHeader() {
            scrollPosition = $(window).scrollTop();
            
            if (scrollPosition > 100) {
                header.addClass('sticky-header');
                $('body').css('padding-top', headerHeight + 'px');
            } else {
                header.removeClass('sticky-header');
                $('body').css('padding-top', '0');
            }
        }

        // Initialize on page load
        handleStickyHeader();

        // Handle on scroll
        $(window).on('scroll', function() {
            handleStickyHeader();
        });

        // Smooth scroll for hero category links
        $('.hero-category').on('click', function(e) {
            var target = $(this).attr('href');
            
            if (target && target.startsWith('#')) {
                e.preventDefault();
                
                $('html, body').animate({
                    scrollTop: $(target).offset().top - headerHeight
                }, 800);
            }
        });

        // Search form enhancement
        $('.hero-search input[type="search"]').on('focus', function() {
            $(this).parent().addClass('search-focused');
        }).on('blur', function() {
            $(this).parent().removeClass('search-focused');
        });

        // Add animation to hero section
        function animateHero() {
            $('.hero-title').addClass('animated fadeInDown');
            $('.hero-subtitle').addClass('animated fadeInUp');
            $('.hero-search').addClass('animated fadeIn');
            
            // Staggered animation for category links
            $('.hero-category').each(function(index) {
                var $this = $(this);
                setTimeout(function() {
                    $this.addClass('animated fadeInUp');
                }, 100 * index);
            });
        }

        // Run hero animations after a short delay
        setTimeout(function() {
            animateHero();
        }, 300);
    });

})(jQuery);
