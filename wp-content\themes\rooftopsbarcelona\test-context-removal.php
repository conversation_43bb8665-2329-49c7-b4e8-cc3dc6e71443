<?php
/**
 * Template Name: Test Context Removal
 * 
 * This template tests the removal of redundant @context entries from schema.org markup.
 */

get_header();
?>

<div class="container">
    <div class="content-area">
        <main id="main" class="site-main">
            <article id="post-<?php the_ID(); ?>" <?php post_class(); ?>>
                <header class="entry-header">
                    <h1 class="entry-title">Testing @context Removal</h1>
                </header>

                <div class="entry-content">
                    <?php
                    // Get the post ID for a spa
                    $args = array(
                        'post_type' => 'spa',
                        'posts_per_page' => 1,
                    );
                    $spa_query = new WP_Query($args);
                    
                    if ($spa_query->have_posts()) {
                        $spa_query->the_post();
                        $post_id = get_the_ID();
                        
                        echo "<h2>Testing @context Removal for: " . get_the_title($post_id) . " (ID: " . $post_id . ")</h2>";
                        
                        // Get the schema directly from the function
                        $schema_html = spasinbarcelona_get_spa_schema($post_id);
                        
                        // Extract JSON from the HTML
                        preg_match_all('/<script type="application\/ld\+json">(.*?)<\/script>/s', $schema_html, $matches);
                        
                        if (empty($matches[1])) {
                            echo "<p>No schema.org JSON-LD data generated for this post.</p>";
                        } else {
                            echo "<p>Generated " . count($matches[1]) . " schema.org JSON-LD blocks:</p>";
                            
                            $context_count = 0;
                            $nested_context_count = 0;
                            
                            foreach ($matches[1] as $index => $json) {
                                $schema = json_decode($json, true);
                                $type = isset($schema['@type']) ? 
                                    (is_array($schema['@type']) ? implode(', ', $schema['@type']) : $schema['@type']) : 
                                    (isset($schema['@graph']) ? '@graph structure' : 'Unknown Type');
                                
                                echo "<h3>Block " . ($index + 1) . ": " . htmlspecialchars($type) . "</h3>";
                                
                                // Count @context entries
                                $count_context = function($obj) use (&$context_count, &$nested_context_count, &$count_context) {
                                    if (!is_array($obj)) return;
                                    
                                    foreach ($obj as $key => $value) {
                                        if ($key === '@context') {
                                            $context_count++;
                                            
                                            // If this is not the root level (has @type), it's a nested context
                                            if (isset($obj['@type'])) {
                                                $nested_context_count++;
                                            }
                                        }
                                        
                                        if (is_array($value)) {
                                            $count_context($value);
                                        }
                                    }
                                };
                                
                                $count_context($schema);
                                
                                // Display the schema
                                echo "<div style='max-height: 300px; overflow: auto; background-color: #f5f5f5; padding: 10px; margin-bottom: 20px;'>";
                                echo "<pre>" . htmlspecialchars(json_encode($schema, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES)) . "</pre>";
                                echo "</div>";
                            }
                            
                            echo "<h3>Context Analysis</h3>";
                            echo "<p>Total @context entries found: " . $context_count . "</p>";
                            echo "<p>Nested @context entries found: " . $nested_context_count . "</p>";
                            
                            if ($nested_context_count > 0) {
                                echo "<p style='color: red; font-weight: bold;'>❌ There are still nested @context entries in the schema!</p>";
                            } else {
                                echo "<p style='color: green; font-weight: bold;'>✅ No nested @context entries found. The schema is optimized!</p>";
                            }
                        }
                        
                        // Add a link to view the spa page
                        echo "<p><a href='" . get_permalink($post_id) . "' target='_blank' class='button'>View Spa Page</a></p>";
                        
                        // Reset post data
                        wp_reset_postdata();
                    } else {
                        echo "<p>No spa posts found.</p>";
                    }
                    ?>
                </div>
            </article>
        </main>
    </div>
</div>

<?php
get_footer();
?>
