<?php
/**
 * Force Assign Mandarin Oriental to Vila Olímpica
 * 
 * This script directly assigns the Mandarin Oriental spa to the Vila Olímpica neighborhood
 * using direct database queries to bypass any caching or other issues
 */

// Load WordPress
require_once( dirname( __FILE__ ) . '/../../wp-load.php' );

// Security check - only administrators can access this page
if (!current_user_can('administrator')) {
    wp_die('You do not have permission to access this page.');
}

echo '<h1>Force Assign Mandarin Oriental to Vila Olímpica</h1>';

global $wpdb;

// Step 1: Find the Mandarin Oriental spa
$mandarin = get_page_by_path('the-spa-at-mandarin-oriental-barcelona', OBJECT, 'spa');

if (!$mandarin) {
    echo '<div style="background: #ffd1d1; padding: 15px; margin: 15px 0; border: 1px solid #a00000;">';
    echo '<p>Mandarin Oriental Spa not found!</p>';
    
    // Try to find it by title
    $spas = get_posts(array(
        'post_type' => 'spa',
        'posts_per_page' => -1,
        'title' => 'Mandarin Oriental',
        'title_like' => 'Mandarin Oriental',
    ));
    
    if (!empty($spas)) {
        echo '<p>Found ' . count($spas) . ' spas with "Mandarin Oriental" in the title:</p>';
        echo '<ul>';
        foreach ($spas as $spa) {
            echo '<li>ID: ' . $spa->ID . ' - Title: ' . esc_html($spa->post_title) . ' - Slug: ' . esc_html($spa->post_name) . '</li>';
            $mandarin = $spa; // Use the first one found
        }
        echo '</ul>';
    } else {
        // Try direct database query
        $spa_posts = $wpdb->get_results(
            "SELECT ID, post_title, post_name FROM {$wpdb->posts} 
            WHERE post_type = 'spa' 
            AND (post_title LIKE '%Mandarin%' OR post_title LIKE '%Oriental%')"
        );
        
        if (!empty($spa_posts)) {
            echo '<p>Found ' . count($spa_posts) . ' spas with "Mandarin" or "Oriental" in the title via direct query:</p>';
            echo '<ul>';
            foreach ($spa_posts as $spa) {
                echo '<li>ID: ' . $spa->ID . ' - Title: ' . esc_html($spa->post_title) . ' - Slug: ' . esc_html($spa->post_name) . '</li>';
                $mandarin = get_post($spa->ID); // Use the first one found
            }
            echo '</ul>';
        } else {
            echo '<p>No spas found with "Mandarin" or "Oriental" in the title.</p>';
            
            // List all spas
            $all_spas = get_posts(array(
                'post_type' => 'spa',
                'posts_per_page' => -1,
            ));
            
            echo '<p>All spas in the database (' . count($all_spas) . '):</p>';
            echo '<ul>';
            foreach ($all_spas as $spa) {
                echo '<li>ID: ' . $spa->ID . ' - Title: ' . esc_html($spa->post_title) . ' - Slug: ' . esc_html($spa->post_name) . '</li>';
            }
            echo '</ul>';
            
            echo '</div>';
            echo '<p>Please select a spa to assign to Vila Olímpica:</p>';
            echo '<form method="get">';
            echo '<select name="spa_id">';
            foreach ($all_spas as $spa) {
                echo '<option value="' . $spa->ID . '">' . esc_html($spa->post_title) . '</option>';
            }
            echo '</select>';
            echo '<button type="submit">Assign Selected Spa</button>';
            echo '</form>';
            
            // Check if a spa was selected
            if (isset($_GET['spa_id'])) {
                $mandarin = get_post(intval($_GET['spa_id']));
                echo '<p>Selected spa: ' . esc_html($mandarin->post_title) . ' (ID: ' . $mandarin->ID . ')</p>';
            } else {
                exit;
            }
        }
    }
    
    echo '</div>';
}

// Step 2: Find the Vila Olímpica term
$vila_olimpica = get_term_by('slug', 'vila-olimpica', 'spa_neighborhood');

if (!$vila_olimpica) {
    echo '<div style="background: #ffd1d1; padding: 15px; margin: 15px 0; border: 1px solid #a00000;">';
    echo '<p>Vila Olímpica term not found with slug "vila-olimpica"!</p>';
    
    // Create the term
    $result = wp_insert_term(
        'Vila Olímpica',
        'spa_neighborhood',
        array(
            'description' => 'Discover the best spas in the Vila Olímpica neighborhood of Barcelona. This modern area near the beach offers luxury spa experiences with sea views.',
            'slug' => 'vila-olimpica'
        )
    );
    
    if (is_wp_error($result)) {
        echo '<p>Error creating term: ' . $result->get_error_message() . '</p>';
        
        // Try to find any term with Vila Olímpica in the name
        $terms = get_terms(array(
            'taxonomy' => 'spa_neighborhood',
            'hide_empty' => false,
            'name__like' => 'Vila',
        ));
        
        if (!empty($terms)) {
            echo '<p>Found ' . count($terms) . ' terms with "Vila" in the name:</p>';
            echo '<ul>';
            foreach ($terms as $term) {
                echo '<li>ID: ' . $term->term_id . ' - Name: ' . esc_html($term->name) . ' - Slug: ' . esc_html($term->slug) . '</li>';
                if (strpos(strtolower($term->name), 'vila ol') !== false) {
                    $vila_olimpica = $term;
                    echo '<p>Using this term as Vila Olímpica.</p>';
                }
            }
            echo '</ul>';
        }
        
        if (!$vila_olimpica) {
            echo '<p>Could not find or create Vila Olímpica term. Exiting.</p>';
            echo '</div>';
            exit;
        }
    } else {
        echo '<p>Created Vila Olímpica term with ID: ' . $result['term_id'] . '</p>';
        $vila_olimpica = get_term($result['term_id'], 'spa_neighborhood');
    }
    
    echo '</div>';
}

echo '<div style="background: #e0f0ff; padding: 15px; margin: 15px 0; border: 1px solid #0073aa;">';
echo '<h2>Assignment Process</h2>';

// Step 3: Assign the spa to the term using direct database queries
echo '<p><strong>Spa:</strong> ' . esc_html($mandarin->post_title) . ' (ID: ' . $mandarin->ID . ')</p>';
echo '<p><strong>Term:</strong> ' . esc_html($vila_olimpica->name) . ' (ID: ' . $vila_olimpica->term_id . ', Taxonomy ID: ' . $vila_olimpica->term_taxonomy_id . ')</p>';

// Check if the relationship already exists
$existing = $wpdb->get_var($wpdb->prepare(
    "SELECT COUNT(*) FROM {$wpdb->term_relationships} 
    WHERE object_id = %d AND term_taxonomy_id = %d",
    $mandarin->ID,
    $vila_olimpica->term_taxonomy_id
));

if ($existing) {
    echo '<p>Relationship already exists in the database. Refreshing...</p>';
    
    // Delete the existing relationship
    $wpdb->delete(
        $wpdb->term_relationships,
        array(
            'object_id' => $mandarin->ID,
            'term_taxonomy_id' => $vila_olimpica->term_taxonomy_id
        )
    );
}

// Insert the relationship
$result = $wpdb->insert(
    $wpdb->term_relationships,
    array(
        'object_id' => $mandarin->ID,
        'term_taxonomy_id' => $vila_olimpica->term_taxonomy_id,
        'term_order' => 0
    )
);

if ($result === false) {
    echo '<p style="color: red;">Error inserting relationship: ' . $wpdb->last_error . '</p>';
} else {
    echo '<p style="color: green;">Successfully inserted relationship.</p>';
}

// Update the term count
wp_update_term_count_now(array($vila_olimpica->term_taxonomy_id), 'spa_neighborhood');
echo '<p>Updated term count.</p>';

// Step 4: Update the spa's location meta
$location = get_post_meta($mandarin->ID, 'location', true);

if (empty($location) || !is_array($location)) {
    $location = array();
}

$location['neighborhood'] = 'Vila Olímpica';
$location['district'] = 'Sant Martí';

update_post_meta($mandarin->ID, 'location', $location);
echo '<p>Updated location metadata.</p>';

// Step 5: Clear caches
clean_object_term_cache($mandarin->ID, 'spa');
clean_term_cache($vila_olimpica->term_id, 'spa_neighborhood');
wp_cache_flush();
echo '<p>Cleared caches.</p>';

// Step 6: Flush rewrite rules
flush_rewrite_rules();
echo '<p>Flushed rewrite rules.</p>';

echo '</div>';

// Verify the assignment
echo '<h2>Verification</h2>';
echo '<div style="background: #d1ffd1; padding: 15px; margin: 15px 0; border: 1px solid #00a000;">';

// Check the term relationships in the database
$relationships = $wpdb->get_results($wpdb->prepare(
    "SELECT * FROM {$wpdb->term_relationships} 
    WHERE object_id = %d AND term_taxonomy_id = %d",
    $mandarin->ID,
    $vila_olimpica->term_taxonomy_id
));

echo '<h3>Database Term Relationships</h3>';
if (!empty($relationships)) {
    echo '<p style="color: green;">Found ' . count($relationships) . ' relationships in the database.</p>';
    echo '<pre>' . print_r($relationships, true) . '</pre>';
} else {
    echo '<p style="color: red;">No relationships found in the database!</p>';
}

// Check using WordPress functions
$terms = wp_get_object_terms($mandarin->ID, 'spa_neighborhood');
echo '<h3>WordPress Object Terms</h3>';
if (!empty($terms) && !is_wp_error($terms)) {
    echo '<p style="color: green;">Found ' . count($terms) . ' terms assigned to the spa.</p>';
    echo '<ul>';
    foreach ($terms as $term) {
        echo '<li>' . esc_html($term->name) . ' (ID: ' . $term->term_id . ', Slug: ' . esc_html($term->slug) . ')</li>';
    }
    echo '</ul>';
} else {
    echo '<p style="color: red;">No terms found using wp_get_object_terms!</p>';
}

// Check the term count
$updated_term = get_term($vila_olimpica->term_id, 'spa_neighborhood');
echo '<h3>Updated Term Count</h3>';
echo '<p>Term count: ' . $updated_term->count . '</p>';

// Get spas in this neighborhood
$spas = get_posts(array(
    'post_type' => 'spa',
    'posts_per_page' => -1,
    'tax_query' => array(
        array(
            'taxonomy' => 'spa_neighborhood',
            'field' => 'term_id',
            'terms' => $vila_olimpica->term_id,
        ),
    ),
));

echo '<h3>Spas in Vila Olímpica</h3>';
if (!empty($spas)) {
    echo '<p style="color: green;">Found ' . count($spas) . ' spas in this neighborhood.</p>';
    echo '<ul>';
    foreach ($spas as $spa) {
        echo '<li>' . esc_html($spa->post_title) . ' (ID: ' . $spa->ID . ')</li>';
    }
    echo '</ul>';
} else {
    echo '<p style="color: red;">No spas found in this neighborhood using WP_Query!</p>';
    
    // Try direct database query
    $spa_ids = $wpdb->get_col($wpdb->prepare(
        "SELECT p.ID FROM {$wpdb->posts} p
        JOIN {$wpdb->term_relationships} tr ON p.ID = tr.object_id
        WHERE p.post_type = 'spa'
        AND tr.term_taxonomy_id = %d",
        $vila_olimpica->term_taxonomy_id
    ));
    
    if (!empty($spa_ids)) {
        echo '<p style="color: green;">Found ' . count($spa_ids) . ' spas using direct database query.</p>';
        echo '<ul>';
        foreach ($spa_ids as $spa_id) {
            $spa = get_post($spa_id);
            echo '<li>' . esc_html($spa->post_title) . ' (ID: ' . $spa->ID . ')</li>';
        }
        echo '</ul>';
    } else {
        echo '<p style="color: red;">No spas found using direct database query!</p>';
    }
}

echo '</div>';

// Add links for testing
echo '<h2>Test Links</h2>';
echo '<p><a href="' . esc_url(home_url('/spa-neighborhood/vila-olimpica/')) . '" target="_blank">Test Vila Olímpica Neighborhood Page</a></p>';
echo '<p><a href="' . esc_url(home_url('/wp-content/themes/spasinbarcelona/debug-neighborhoods.php')) . '">Go to Neighborhood Debug</a></p>';
echo '<p><a href="' . esc_url(home_url('/wp-content/themes/spasinbarcelona/update-permalinks.php')) . '">Update Permalinks</a></p>';
