<?php
/**
 * Add a menu item to flush rewrite rules from the admin area
 */

// Add admin menu
add_action('admin_menu', 'spasinbarcelona_flush_rewrites_menu');

function spasinbarcelona_flush_rewrites_menu() {
    add_management_page(
        'Flush Rewrite Rules',
        'Flush Rewrite Rules',
        'manage_options',
        'flush-rewrites',
        'spasinbarcelona_flush_rewrites_page'
    );
}

// Admin page callback
function spasinbarcelona_flush_rewrites_page() {
    $message = '';

    // Check if the form was submitted
    if (isset($_POST['flush_rewrites']) && check_admin_referer('spasinbarcelona_flush_rewrites')) {
        // Delete the transient
        delete_transient('spasinbarcelona_flushed_rewrites');

        // Flush rewrite rules
        flush_rewrite_rules(true);

        $message = '<div class="notice notice-success"><p>Rewrite rules flushed successfully!</p></div>';
    }

    // Output the admin page
    ?>
    <div class="wrap">
        <h1>Flush Rewrite Rules</h1>
        <?php echo $message; ?>
        <p>Click the button below to flush the rewrite rules and activate the new URL structure.</p>
        <div class="notice notice-info">
            <p><strong>New Feature:</strong> Neighborhood taxonomy has been added. Flush rewrite rules to ensure the new URLs work correctly.</p>
            <p>The new URL structure is: <code>/spa-neighborhood/[neighborhood-name]/</code></p>
        </div>
        <form method="post">
            <?php wp_nonce_field('spasinbarcelona_flush_rewrites'); ?>
            <p>
                <input type="submit" name="flush_rewrites" class="button button-primary" value="Flush Rewrite Rules">
            </p>
        </form>
    </div>
    <?php
}
