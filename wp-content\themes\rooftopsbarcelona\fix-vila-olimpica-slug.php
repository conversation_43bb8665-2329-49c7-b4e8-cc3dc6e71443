<?php
/**
 * Fix Vila Olímpica Slug
 * 
 * This script specifically fixes the Vila Olímpica term slug issue
 */

// Load WordPress
require_once( dirname( __FILE__ ) . '/../../wp-load.php' );

// Security check - only administrators can access this page
if (!current_user_can('administrator')) {
    wp_die('You do not have permission to access this page.');
}

echo '<h1>Fix Vila Olímpica Slug</h1>';

// First, check if the term exists with either slug
$term_with_accent = get_term_by('slug', 'vila-olímpica', 'spa_neighborhood');
$term_without_accent = get_term_by('slug', 'vila-olimpica', 'spa_neighborhood');

echo '<div style="background: #e0f0ff; padding: 15px; margin: 15px 0; border: 1px solid #0073aa;">';

// Display current terms
echo '<h2>Current Terms</h2>';
echo '<table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">';
echo '<tr style="background: #f0f0f0;">';
echo '<th style="border: 1px solid #ccc; padding: 8px; text-align: left;">Term</th>';
echo '<th style="border: 1px solid #ccc; padding: 8px; text-align: left;">ID</th>';
echo '<th style="border: 1px solid #ccc; padding: 8px; text-align: left;">Name</th>';
echo '<th style="border: 1px solid #ccc; padding: 8px; text-align: left;">Slug</th>';
echo '<th style="border: 1px solid #ccc; padding: 8px; text-align: left;">Count</th>';
echo '</tr>';

if ($term_with_accent) {
    echo '<tr>';
    echo '<td style="border: 1px solid #ccc; padding: 8px;">Term with accent</td>';
    echo '<td style="border: 1px solid #ccc; padding: 8px;">' . $term_with_accent->term_id . '</td>';
    echo '<td style="border: 1px solid #ccc; padding: 8px;">' . esc_html($term_with_accent->name) . '</td>';
    echo '<td style="border: 1px solid #ccc; padding: 8px;">' . esc_html($term_with_accent->slug) . '</td>';
    echo '<td style="border: 1px solid #ccc; padding: 8px;">' . $term_with_accent->count . '</td>';
    echo '</tr>';
}

if ($term_without_accent) {
    echo '<tr>';
    echo '<td style="border: 1px solid #ccc; padding: 8px;">Term without accent</td>';
    echo '<td style="border: 1px solid #ccc; padding: 8px;">' . $term_without_accent->term_id . '</td>';
    echo '<td style="border: 1px solid #ccc; padding: 8px;">' . esc_html($term_without_accent->name) . '</td>';
    echo '<td style="border: 1px solid #ccc; padding: 8px;">' . esc_html($term_without_accent->slug) . '</td>';
    echo '<td style="border: 1px solid #ccc; padding: 8px;">' . $term_without_accent->count . '</td>';
    echo '</tr>';
}

if (!$term_with_accent && !$term_without_accent) {
    echo '<tr><td colspan="5" style="border: 1px solid #ccc; padding: 8px;">No Vila Olímpica terms found!</td></tr>';
}

echo '</table>';

// Fix the issue
echo '<h2>Fix Process</h2>';

// Determine which term to keep
$term_to_keep = null;
$term_to_remove = null;

if ($term_with_accent && $term_without_accent) {
    // Both terms exist, keep the one with more spas or the one with accent if equal
    if ($term_with_accent->count >= $term_without_accent->count) {
        $term_to_keep = $term_with_accent;
        $term_to_remove = $term_without_accent;
        echo '<p>Both terms exist. Keeping term with accent (ID: ' . $term_with_accent->term_id . ') as it has ' . $term_with_accent->count . ' spas.</p>';
    } else {
        $term_to_keep = $term_without_accent;
        $term_to_remove = $term_with_accent;
        echo '<p>Both terms exist. Keeping term without accent (ID: ' . $term_without_accent->term_id . ') as it has ' . $term_without_accent->count . ' spas.</p>';
    }
    
    // Merge the terms
    echo '<p>Merging terms...</p>';
    
    // Get spas from the term to remove
    $spas_to_reassign = get_posts(array(
        'post_type' => 'spa',
        'posts_per_page' => -1,
        'tax_query' => array(
            array(
                'taxonomy' => 'spa_neighborhood',
                'field' => 'term_id',
                'terms' => $term_to_remove->term_id,
            ),
        ),
    ));
    
    // Assign them to the term to keep
    foreach ($spas_to_reassign as $spa) {
        wp_set_object_terms($spa->ID, $term_to_keep->term_id, 'spa_neighborhood', true);
        echo '<p>Reassigned spa: ' . esc_html($spa->post_title) . ' to term ID: ' . $term_to_keep->term_id . '</p>';
    }
    
    // Delete the term to remove
    wp_delete_term($term_to_remove->term_id, 'spa_neighborhood');
    echo '<p>Deleted term ID: ' . $term_to_remove->term_id . '</p>';
    
    // Update the term to keep to ensure it has the correct name and slug
    wp_update_term(
        $term_to_keep->term_id,
        'spa_neighborhood',
        array(
            'name' => 'Vila Olímpica',
            'slug' => 'vila-olimpica' // Use without accent for URL compatibility
        )
    );
    echo '<p>Updated term ID: ' . $term_to_keep->term_id . ' with name "Vila Olímpica" and slug "vila-olimpica"</p>';
} elseif ($term_with_accent) {
    // Only term with accent exists
    echo '<p>Only term with accent exists (ID: ' . $term_with_accent->term_id . '). Updating slug for URL compatibility...</p>';
    
    wp_update_term(
        $term_with_accent->term_id,
        'spa_neighborhood',
        array(
            'name' => 'Vila Olímpica',
            'slug' => 'vila-olimpica' // Use without accent for URL compatibility
        )
    );
    echo '<p>Updated term ID: ' . $term_with_accent->term_id . ' with name "Vila Olímpica" and slug "vila-olimpica"</p>';
    
    $term_to_keep = $term_with_accent;
} elseif ($term_without_accent) {
    // Only term without accent exists
    echo '<p>Only term without accent exists (ID: ' . $term_without_accent->term_id . '). Updating name to include accent...</p>';
    
    wp_update_term(
        $term_without_accent->term_id,
        'spa_neighborhood',
        array(
            'name' => 'Vila Olímpica',
            'slug' => 'vila-olimpica'
        )
    );
    echo '<p>Updated term ID: ' . $term_without_accent->term_id . ' with name "Vila Olímpica" and slug "vila-olimpica"</p>';
    
    $term_to_keep = $term_without_accent;
} else {
    // No term exists, create it
    echo '<p>No Vila Olímpica term found. Creating new term...</p>';
    
    $result = wp_insert_term(
        'Vila Olímpica',
        'spa_neighborhood',
        array(
            'description' => 'Discover the best spas in the Vila Olímpica neighborhood of Barcelona. This modern area near the beach offers luxury spa experiences with sea views.',
            'slug' => 'vila-olimpica'
        )
    );
    
    if (is_wp_error($result)) {
        echo '<p>Error creating term: ' . $result->get_error_message() . '</p>';
    } else {
        echo '<p>Created new term with ID: ' . $result['term_id'] . '</p>';
        $term_to_keep = get_term($result['term_id'], 'spa_neighborhood');
    }
}

// Now assign the Mandarin Oriental spa to this term
if ($term_to_keep) {
    $mandarin = get_page_by_path('the-spa-at-mandarin-oriental-barcelona', OBJECT, 'spa');
    
    if ($mandarin) {
        echo '<h2>Assigning Mandarin Oriental Spa</h2>';
        
        // Assign the term
        $result = wp_set_object_terms($mandarin->ID, $term_to_keep->term_id, 'spa_neighborhood', true);
        
        if (is_wp_error($result)) {
            echo '<p>Error assigning term: ' . $result->get_error_message() . '</p>';
        } else {
            echo '<p>Successfully assigned Vila Olímpica term to Mandarin Oriental Spa.</p>';
        }
        
        // Update the spa's location meta
        $location = get_post_meta($mandarin->ID, 'location', true);
        
        if (empty($location) || !is_array($location)) {
            $location = array();
        }
        
        $location['neighborhood'] = 'Vila Olímpica';
        $location['district'] = 'Sant Martí';
        
        update_post_meta($mandarin->ID, 'location', $location);
        echo '<p>Updated location metadata for Mandarin Oriental Spa.</p>';
    } else {
        echo '<p>Mandarin Oriental Spa not found!</p>';
    }
}

// Flush rewrite rules
flush_rewrite_rules();
echo '<p>Flushed rewrite rules.</p>';

echo '</div>';

// Display the current state after fixes
echo '<h2>Current State After Fixes</h2>';

// Get the Vila Olímpica term
$vila_olimpica = get_term_by('slug', 'vila-olimpica', 'spa_neighborhood');

if ($vila_olimpica) {
    echo '<div style="background: #d1ffd1; padding: 15px; margin: 15px 0; border: 1px solid #00a000;">';
    echo '<p><strong>Term ID:</strong> ' . $vila_olimpica->term_id . '</p>';
    echo '<p><strong>Name:</strong> ' . esc_html($vila_olimpica->name) . '</p>';
    echo '<p><strong>Slug:</strong> ' . esc_html($vila_olimpica->slug) . '</p>';
    echo '<p><strong>Count:</strong> ' . $vila_olimpica->count . '</p>';
    
    // Get the term link
    $term_link = get_term_link($vila_olimpica);
    echo '<p><strong>Term Link:</strong> <a href="' . esc_url($term_link) . '" target="_blank">' . esc_html($term_link) . '</a></p>';
    
    // Get spas in this neighborhood
    $spas = get_posts(array(
        'post_type' => 'spa',
        'posts_per_page' => -1,
        'tax_query' => array(
            array(
                'taxonomy' => 'spa_neighborhood',
                'field' => 'term_id',
                'terms' => $vila_olimpica->term_id,
            ),
        ),
    ));
    
    echo '<h3>Spas in Vila Olímpica (' . count($spas) . ')</h3>';
    
    if (!empty($spas)) {
        echo '<ul>';
        foreach ($spas as $spa) {
            echo '<li><a href="' . get_permalink($spa->ID) . '" target="_blank">' . esc_html($spa->post_title) . '</a> (ID: ' . $spa->ID . ')</li>';
        }
        echo '</ul>';
    } else {
        echo '<p>No spas found in this neighborhood.</p>';
    }
    
    echo '</div>';
} else {
    echo '<div style="background: #ffd1d1; padding: 15px; margin: 15px 0; border: 1px solid #a00000;">';
    echo '<p>Vila Olímpica term not found after fixes!</p>';
    echo '</div>';
}

// Add links for testing
echo '<p><a href="' . esc_url(home_url('/spa-neighborhood/vila-olimpica/')) . '" target="_blank">Test Vila Olímpica Neighborhood Page</a></p>';
echo '<p><a href="' . esc_url(home_url('/wp-content/themes/spasinbarcelona/debug-vila-olimpica.php')) . '">Refresh This Page</a></p>';
echo '<p><a href="' . esc_url(home_url('/wp-content/themes/spasinbarcelona/debug-neighborhoods.php')) . '">Back to Neighborhood Debug</a></p>';
