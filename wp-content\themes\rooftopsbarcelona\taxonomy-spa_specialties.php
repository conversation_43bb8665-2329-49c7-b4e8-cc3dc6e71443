<?php
/**
 * The template for displaying spa specialties archives
 * This template is used for the /spa-specialties/ URL structure
 */

get_header();
?>

<?php
// Debug information
if (current_user_can('administrator')) {
    echo '<div style="background: #fff; padding: 20px; margin: 20px; border: 1px solid #ddd;">';
    echo '<h2>Debug Information</h2>';

    echo '<h3>Current Taxonomy</h3>';
    echo '<pre>' . print_r(get_queried_object(), true) . '</pre>';

    echo '<h3>Query Variables</h3>';
    echo '<pre>' . print_r($wp_query->query_vars, true) . '</pre>';

    echo '</div>';
}
?>

<div id="primary" class="content-area">
    <main id="main" class="site-main">
        <header class="page-header">
            <div class="page-title-wrapper">
                <?php
                // Get the current term slug from the URL
                $current_url = $_SERVER['REQUEST_URI'];
                $url_parts = explode('/', trim($current_url, '/'));
                $term_slug = end($url_parts);

                // Get the term object
                $term = get_term_by('slug', $term_slug, 'spa_service');

                if ($term) {
                    echo '<h1 class="page-title">Spas in Barcelona with ' . esc_html($term->name) . '</h1>';
                } else {
                    echo '<h1 class="page-title">' . wp_kses_post(get_the_archive_title()) . '</h1>';
                }
                ?>
            </div>
            <div class="archive-description">
                <?php
                if ($term) {
                    echo '<p>Discover the best spas in Barcelona offering ' . esc_html($term->name) . '. Browse our selection of spas with this popular service.</p>';
                    
                } else {
                    echo wp_kses_post(get_the_archive_description());
                }
                ?>
            </div>
        </header>

        <div class="spa-results" style="width: 100%;">
            <?php
            // Get the current term slug from the URL
            $current_url = $_SERVER['REQUEST_URI'];
            $url_parts = explode('/', trim($current_url, '/'));
            $term_slug = end($url_parts);

            // Create a custom query to get spas with this service
            $args = array(
                'post_type' => 'spa',
                'posts_per_page' => -1,
                'tax_query' => array(
                    array(
                        'taxonomy' => 'spa_service',
                        'field' => 'slug',
                        'terms' => $term_slug,
                    ),
                ),
            );

            $spa_query = new WP_Query($args);

            if ($spa_query->have_posts()) :
            ?>
                <div class="spa-grid">
                    <?php
                    while ($spa_query->have_posts()) :
                        $spa_query->the_post();
                        get_template_part('template-parts/content', 'spa-card');
                    endwhile;
                    wp_reset_postdata();
                    ?>
                </div>
            <?php else : ?>
                <div class="no-results">
                    <p><?php esc_html_e('No spas found matching your criteria.', 'spasinbarcelona'); ?></p>
                </div>
            <?php endif; ?>
        </div>

        <?php if ($spa_query->max_num_pages > 1) : ?>
        <div class="spa-pagination">
            <?php
            $big = 999999999;
            echo paginate_links( array(
                'base' => str_replace( $big, '%#%', esc_url( get_pagenum_link( $big ) ) ),
                'format' => '?paged=%#%',
                'current' => max( 1, get_query_var( 'paged' ) ),
                'total' => $spa_query->max_num_pages,
                'prev_text' => '<i class="fas fa-chevron-left"></i> Previous',
                'next_text' => 'Next <i class="fas fa-chevron-right"></i>',
            ) );
            ?>
        </div>
        <?php endif; ?>
    </main>
</div>

<?php
get_footer();
