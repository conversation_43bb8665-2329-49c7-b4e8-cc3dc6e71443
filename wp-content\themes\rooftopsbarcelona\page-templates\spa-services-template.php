<?php
/**
 * Template Name: Spa Services Template
 *
 * This template is used to display spas by service
 */

get_header();

// Get the service slug from query var
$term_slug = get_query_var('service_slug');

// If no query var, try to get from URL
if (empty($term_slug)) {
    $current_url = $_SERVER['REQUEST_URI'];
    $url_parts = explode('/', trim($current_url, '/'));
    $term_slug = end($url_parts);

    // If the last part is the page name, there's no slug
    if ($term_slug === 'spa-services') {
        $term_slug = '';
    }
}

// Get the term object if we have a slug
$term = !empty($term_slug) ? get_term_by('slug', $term_slug, 'spa_service') : null;
?>

<div id="primary" class="content-area">
    <main id="main" class="site-main">
        <header class="page-header">
            <div class="page-title-wrapper">
                <?php
                if ($term) {
                    echo '<h1 class="page-title">Spas in Barcelona with ' . esc_html($term->name) . '</h1>';
                } else {
                    echo '<h1 class="page-title">Spas in Barcelona Services</h1>';
                }
                ?>
            </div>
            <div class="archive-description">
                <?php
                if ($term && !empty($term->description)) {
                    echo wp_kses_post($term->description);
                } else if ($term) {
                    echo '<p>Discover the best spas in Barcelona offering ' . esc_html($term->name) . '. Browse our selection of spas with this popular service.</p>';
                } else {
                    echo '<p>Browse our selection of spas in Barcelona by services offered.</p>';
                }
                ?>
            </div>
        </header>



        <div class="spa-results" style="width: 100%;">
            <?php
            if ($term) {
                // Create a custom query to get spas with this service
                $args = array(
                    'post_type' => 'spa',
                    'posts_per_page' => -1,
                    'tax_query' => array(
                        array(
                            'taxonomy' => 'spa_service',
                            'field' => 'slug',
                            'terms' => $term_slug,
                            'operator' => 'IN',
                            'include_children' => true
                        ),
                    ),
                    'orderby' => 'title',
                    'order' => 'ASC'
                );

                // For debugging
                if (current_user_can('administrator')) {
                    echo '<!-- Debug: Service query for "' . esc_html($term_slug) . '" -->';
                }

                $spa_query = new WP_Query($args);

                if ($spa_query->have_posts()) :
                ?>
                    <div class="spa-grid">
                        <?php
                        while ($spa_query->have_posts()) :
                            $spa_query->the_post();
                            get_template_part('template-parts/content', 'spa-card');
                        endwhile;
                        wp_reset_postdata();
                        ?>
                    </div>
                <?php else : ?>
                    <div class="no-results">
                        <p><?php esc_html_e('No spas found matching your criteria.', 'spasinbarcelona'); ?></p>
                    </div>
                <?php endif;
            } else {
                // Display all services
                $services = get_terms(array(
                    'taxonomy' => 'spa_service',
                    'hide_empty' => true,
                ));

                if (!empty($services) && !is_wp_error($services)) :
                ?>
                    <div class="spa-services-grid">
                        <?php foreach ($services as $service) : ?>
                            <a href="<?php echo esc_url(home_url('/spa-services/' . $service->slug . '/')); ?>" class="spa-service-card">
                                <h3 class="spa-service-title"><?php echo esc_html($service->name); ?></h3>
                                <span class="spa-service-count"><?php echo esc_html($service->count); ?> <?php echo esc_html(_n('Spa', 'Spas', $service->count, 'spasinbarcelona')); ?></span>
                            </a>
                        <?php endforeach; ?>
                    </div>
                <?php else : ?>
                    <div class="no-results">
                        <p><?php esc_html_e('No spa services found.', 'spasinbarcelona'); ?></p>
                    </div>
                <?php endif;
            }
            ?>
        </div>
    </main>
</div>

<style>
    .spa-services-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 20px;
        margin-top: 30px;
    }

    .spa-service-card {
        background-color: #f5f5f0;
        border-radius: 8px;
        padding: 20px;
        text-align: center;
        text-decoration: none;
        color: #333;
        transition: all 0.3s ease;
        border: 1px solid rgba(0,0,0,0.05);
    }

    .spa-service-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }

    .spa-service-title {
        margin-top: 0;
        margin-bottom: 10px;
        font-size: 1.2rem;
    }

    .spa-service-count {
        font-size: 0.9rem;
        color: #666;
    }
</style>

<?php
get_footer();
