<?php
/**
 * Debug Taxonomy Details
 * 
 * This script displays detailed information about the spa_neighborhood taxonomy
 */

// Load WordPress
require_once( dirname( __FILE__ ) . '/../../wp-load.php' );

// Security check - only administrators can access this page
if (!current_user_can('administrator')) {
    wp_die('You do not have permission to access this page.');
}

echo '<h1>Debug Taxonomy Details</h1>';

// Get all terms in the spa_neighborhood taxonomy
$all_terms = get_terms(array(
    'taxonomy' => 'spa_neighborhood',
    'hide_empty' => false,
));

echo '<div style="background: #e0f0ff; padding: 15px; margin: 15px 0; border: 1px solid #0073aa;">';
echo '<h2>All Terms in spa_neighborhood Taxonomy (' . count($all_terms) . ')</h2>';

if (!empty($all_terms) && !is_wp_error($all_terms)) {
    echo '<table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">';
    echo '<tr style="background: #f0f0f0;">';
    echo '<th style="border: 1px solid #ccc; padding: 8px; text-align: left;">ID</th>';
    echo '<th style="border: 1px solid #ccc; padding: 8px; text-align: left;">Term ID</th>';
    echo '<th style="border: 1px solid #ccc; padding: 8px; text-align: left;">Name</th>';
    echo '<th style="border: 1px solid #ccc; padding: 8px; text-align: left;">Slug</th>';
    echo '<th style="border: 1px solid #ccc; padding: 8px; text-align: left;">Parent</th>';
    echo '<th style="border: 1px solid #ccc; padding: 8px; text-align: left;">Count</th>';
    echo '<th style="border: 1px solid #ccc; padding: 8px; text-align: left;">Term Taxonomy ID</th>';
    echo '</tr>';
    
    foreach ($all_terms as $term) {
        $parent_name = '';
        if ($term->parent) {
            $parent_term = get_term($term->parent, 'spa_neighborhood');
            if ($parent_term && !is_wp_error($parent_term)) {
                $parent_name = $parent_term->name;
            }
        }
        
        echo '<tr>';
        echo '<td style="border: 1px solid #ccc; padding: 8px;">' . $term->term_id . '</td>';
        echo '<td style="border: 1px solid #ccc; padding: 8px;">' . $term->term_id . '</td>';
        echo '<td style="border: 1px solid #ccc; padding: 8px;">' . esc_html($term->name) . '</td>';
        echo '<td style="border: 1px solid #ccc; padding: 8px;">' . esc_html($term->slug) . '</td>';
        echo '<td style="border: 1px solid #ccc; padding: 8px;">' . esc_html($parent_name) . ' (' . $term->parent . ')</td>';
        echo '<td style="border: 1px solid #ccc; padding: 8px;">' . $term->count . '</td>';
        echo '<td style="border: 1px solid #ccc; padding: 8px;">' . $term->term_taxonomy_id . '</td>';
        echo '</tr>';
    }
    
    echo '</table>';
} else {
    echo '<p>No terms found.</p>';
}

echo '</div>';

// Get all spas
$all_spas = get_posts(array(
    'post_type' => 'spa',
    'posts_per_page' => -1,
));

echo '<div style="background: #fff0e0; padding: 15px; margin: 15px 0; border: 1px solid #ffa500;">';
echo '<h2>All Spas and Their Neighborhoods (' . count($all_spas) . ')</h2>';

if (!empty($all_spas)) {
    echo '<table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">';
    echo '<tr style="background: #f0f0f0;">';
    echo '<th style="border: 1px solid #ccc; padding: 8px; text-align: left;">ID</th>';
    echo '<th style="border: 1px solid #ccc; padding: 8px; text-align: left;">Title</th>';
    echo '<th style="border: 1px solid #ccc; padding: 8px; text-align: left;">Neighborhoods (Term Relationships)</th>';
    echo '<th style="border: 1px solid #ccc; padding: 8px; text-align: left;">Location Meta</th>';
    echo '</tr>';
    
    foreach ($all_spas as $spa) {
        echo '<tr>';
        echo '<td style="border: 1px solid #ccc; padding: 8px;">' . $spa->ID . '</td>';
        echo '<td style="border: 1px solid #ccc; padding: 8px;">' . esc_html($spa->post_title) . '</td>';
        
        // Get neighborhoods for this spa using direct database query
        global $wpdb;
        $term_relationships = $wpdb->get_results($wpdb->prepare(
            "SELECT tr.*, tt.taxonomy, tt.term_id, t.name, t.slug
            FROM {$wpdb->term_relationships} tr
            JOIN {$wpdb->term_taxonomy} tt ON tr.term_taxonomy_id = tt.term_taxonomy_id
            JOIN {$wpdb->terms} t ON tt.term_id = t.term_id
            WHERE tr.object_id = %d
            AND tt.taxonomy = 'spa_neighborhood'",
            $spa->ID
        ));
        
        echo '<td style="border: 1px solid #ccc; padding: 8px;">';
        if (!empty($term_relationships)) {
            echo '<ul>';
            foreach ($term_relationships as $rel) {
                echo '<li>' . esc_html($rel->name) . ' (ID: ' . $rel->term_id . ', Slug: ' . esc_html($rel->slug) . ', TTI: ' . $rel->term_taxonomy_id . ')</li>';
            }
            echo '</ul>';
        } else {
            echo 'No term relationships found';
        }
        echo '</td>';
        
        // Get location meta
        $location = get_post_meta($spa->ID, 'location', true);
        
        echo '<td style="border: 1px solid #ccc; padding: 8px;">';
        if (!empty($location) && is_array($location)) {
            echo '<ul>';
            foreach ($location as $key => $value) {
                echo '<li><strong>' . esc_html($key) . ':</strong> ' . esc_html($value) . '</li>';
            }
            echo '</ul>';
        } else {
            echo 'No location meta found';
        }
        echo '</td>';
        
        echo '</tr>';
    }
    
    echo '</table>';
} else {
    echo '<p>No spas found.</p>';
}

echo '</div>';

// Check for Sant Martí specifically
$sant_marti = get_term_by('slug', 'sant-marti', 'spa_neighborhood');

echo '<div style="background: #ffd1d1; padding: 15px; margin: 15px 0; border: 1px solid #a00000;">';
echo '<h2>Sant Martí Term Details</h2>';

if ($sant_marti && !is_wp_error($sant_marti)) {
    echo '<p><strong>ID:</strong> ' . $sant_marti->term_id . '</p>';
    echo '<p><strong>Name:</strong> ' . esc_html($sant_marti->name) . '</p>';
    echo '<p><strong>Slug:</strong> ' . esc_html($sant_marti->slug) . '</p>';
    echo '<p><strong>Count:</strong> ' . $sant_marti->count . '</p>';
    echo '<p><strong>Parent:</strong> ' . $sant_marti->parent . '</p>';
    echo '<p><strong>Term Taxonomy ID:</strong> ' . $sant_marti->term_taxonomy_id . '</p>';
    
    // Get spas in Sant Martí
    $spas_in_sant_marti = get_posts(array(
        'post_type' => 'spa',
        'posts_per_page' => -1,
        'tax_query' => array(
            array(
                'taxonomy' => 'spa_neighborhood',
                'field' => 'term_id',
                'terms' => $sant_marti->term_id,
            ),
        ),
    ));
    
    echo '<h3>Spas in Sant Martí (' . count($spas_in_sant_marti) . ')</h3>';
    
    if (!empty($spas_in_sant_marti)) {
        echo '<ul>';
        foreach ($spas_in_sant_marti as $spa) {
            echo '<li>' . esc_html($spa->post_title) . ' (ID: ' . $spa->ID . ')</li>';
        }
        echo '</ul>';
    } else {
        echo '<p>No spas found in Sant Martí using WP_Query.</p>';
    }
    
    // Check direct database relationships
    global $wpdb;
    $direct_relationships = $wpdb->get_results($wpdb->prepare(
        "SELECT p.ID, p.post_title
        FROM {$wpdb->posts} p
        JOIN {$wpdb->term_relationships} tr ON p.ID = tr.object_id
        WHERE p.post_type = 'spa'
        AND tr.term_taxonomy_id = %d",
        $sant_marti->term_taxonomy_id
    ));
    
    echo '<h3>Direct Database Relationships (' . count($direct_relationships) . ')</h3>';
    
    if (!empty($direct_relationships)) {
        echo '<ul>';
        foreach ($direct_relationships as $rel) {
            echo '<li>' . esc_html($rel->post_title) . ' (ID: ' . $rel->ID . ')</li>';
        }
        echo '</ul>';
    } else {
        echo '<p>No direct relationships found in the database.</p>';
    }
} else {
    echo '<p>Sant Martí term not found.</p>';
}

echo '</div>';

// Add a fix button
echo '<div style="background: #d1ffd1; padding: 15px; margin: 15px 0; border: 1px solid #00a000;">';
echo '<h2>Fix Sant Martí Issue</h2>';

if (isset($_GET['fix']) && $_GET['fix'] === 'sant-marti') {
    // Fix the issue by removing Sant Martí from the taxonomy
    if ($sant_marti && !is_wp_error($sant_marti)) {
        // First, get all spas assigned to Sant Martí
        $spas_to_fix = get_posts(array(
            'post_type' => 'spa',
            'posts_per_page' => -1,
            'tax_query' => array(
                array(
                    'taxonomy' => 'spa_neighborhood',
                    'field' => 'term_id',
                    'terms' => $sant_marti->term_id,
                ),
            ),
        ));
        
        echo '<h3>Fixing ' . count($spas_to_fix) . ' spas</h3>';
        
        foreach ($spas_to_fix as $spa) {
            // Remove Sant Martí from this spa
            wp_remove_object_terms($spa->ID, $sant_marti->term_id, 'spa_neighborhood');
            echo '<p>Removed Sant Martí from ' . esc_html($spa->post_title) . '</p>';
        }
        
        // Also check direct database relationships
        global $wpdb;
        $wpdb->delete(
            $wpdb->term_relationships,
            array(
                'term_taxonomy_id' => $sant_marti->term_taxonomy_id
            )
        );
        
        // Update the term count
        wp_update_term_count_now(array($sant_marti->term_taxonomy_id), 'spa_neighborhood');
        
        echo '<p>Removed any direct database relationships and updated term count.</p>';
        
        // Clear caches
        clean_term_cache($sant_marti->term_id, 'spa_neighborhood');
        echo '<p>Cleared term cache.</p>';
        
        echo '<p style="color: green; font-weight: bold;">Fix completed. <a href="' . esc_url($_SERVER['PHP_SELF']) . '">Refresh this page</a> to see the results.</p>';
    } else {
        echo '<p>Sant Martí term not found, nothing to fix.</p>';
    }
} else {
    echo '<p>Click the button below to fix the Sant Martí issue by removing it from any spas:</p>';
    echo '<p><a href="' . esc_url($_SERVER['PHP_SELF'] . '?fix=sant-marti') . '" style="display: inline-block; padding: 10px 15px; background-color: #4CAF50; color: white; text-decoration: none; border-radius: 4px;">Fix Sant Martí Issue</a></p>';
}

echo '</div>';

// Add links for testing
echo '<p><a href="' . esc_url(home_url('/')) . '">Back to Homepage</a></p>';
