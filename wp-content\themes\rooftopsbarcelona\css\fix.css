/**
 * Enhanced fix for footer and CTA section visibility
 * Also fixes white background issues on spa pages
 */

/* Ensure CTA section is visible */
.spa-cta-wrapper {
    display: block;
    visibility: visible;
    opacity: 1;
    margin-top: 40px;
    margin-bottom: 0;
    z-index: 10;
    position: relative;
    clear: both;
}

.cta-section {
    display: block;
    visibility: visible;
    opacity: 1;
    margin-top: 40px;
    margin-bottom: 0;
    z-index: 10;
    position: relative;
    clear: both;
}

/* Fix any potential z-index issues */
#page {
    position: relative;
    z-index: 1;
    overflow: visible !important;
    display: flex !important;
    flex-direction: column !important;
    min-height: 100vh !important;
    background-color: #f5f5f0 !important;
}

/* Ensure proper spacing between sections */
.spa-features {
    margin-bottom: 0;
}

/* Fix for any potential overflow issues */
body {
    overflow-x: hidden;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: #f5f5f0 !important;
}

/* Ensure content area doesn't hide footer */
#primary {
    margin-bottom: 0;
    flex: 1 0 auto;
    background-color: #f5f5f0 !important;
}

/* Fix for any potential positioning issues */
.site-content {
    position: relative;
    z-index: 1;
    flex: 1 0 auto;
    background-color: #f5f5f0 !important;
}

/* Force footer to bottom if content is short */
.site-footer {
    flex-shrink: 0;
}

/* Fix for any potential display issues with main content */
.site-main {
    position: relative;
    z-index: 1;
    background-color: #f5f5f0 !important;
}

/* Ensure proper display of CTA content */
.cta-content {
    position: relative;
    z-index: 5;
}

/* Fix for any potential issues with footer widgets */
.footer-widgets {
    /* display: grid; */ /* Let footer.css handle this */
}

/* Fix for any potential issues with footer bottom */
.footer-bottom {
    /* Let footer.css handle display, alignment, margin, padding, border */
    /* display: flex; */
    /* justify-content: space-between; */
    /* align-items: center; */
    /* margin-top: 30px; */
    /* padding-top: 20px; */
    /* border-top: 1px solid rgba(255, 255, 255, 0.1); */
}
