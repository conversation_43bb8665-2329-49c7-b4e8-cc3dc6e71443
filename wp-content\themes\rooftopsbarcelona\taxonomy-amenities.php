<?php
/**
 * The template for displaying spa feature archives (now Amenities)
 */

get_header();
?>

<div id="primary" class="content-area">
    <main id="main" class="site-main">
        <header class="page-header">
            <div class="page-title-wrapper">
                <h1 class="page-title"><?php echo wp_kses_post( get_the_archive_title() ); ?></h1>
            </div>
            <div class="archive-description">
                <?php echo wp_kses_post( get_the_archive_description() ); ?>
            </div>
        </header>



        <div class="spa-results" style="width: 100%;">
            <?php
            $current_term = get_queried_object();
            $paged = ( get_query_var( 'paged' ) ) ? get_query_var( 'paged' ) : 1;
            $args = array(
                'post_type' => 'spa',
                'posts_per_page' => -1, // Display all matching spas
                'paged' => $paged,
                'tax_query' => array(
                    array(
                        'taxonomy' => $current_term->taxonomy,
                        'field'    => 'slug',
                        'terms'    => $current_term->slug,
                    ),
                ),
            );
            $spa_query = new WP_Query( $args );

            if ( $spa_query->have_posts() ) :
            ?>
                <div class="spa-grid">
                    <?php
                    while ( $spa_query->have_posts() ) :
                        $spa_query->the_post();
                        get_template_part( 'template-parts/content', 'spa-card' );
                    endwhile;
                    ?>
                </div>
            <?php else : ?>
                <div class="no-results">
                    <p><?php esc_html_e( 'No spas found matching your criteria.', 'spasinbarcelona' ); ?></p>
                </div>
            <?php endif; ?>
        </div>

        <?php if ($spa_query->max_num_pages > 1) : // Check if pagination is needed ?>
        <div class="spa-pagination">
            <?php
            $big = 999999999; // need an unlikely integer
            echo paginate_links( array(
                'base' => str_replace( $big, '%#%', esc_url( get_pagenum_link( $big ) ) ),
                'format' => '?paged=%#%',
                'current' => max( 1, $paged ),
                'total' => $spa_query->max_num_pages,
                'prev_text' => '<i class="fas fa-chevron-left"></i> Previous',
                'next_text' => 'Next <i class="fas fa-chevron-right"></i>',
            ) );
            ?>
        </div>
        <?php endif; ?>
        <?php wp_reset_postdata(); // Important: reset post data after custom query ?>

        <!-- Navigation Buttons -->
        <div class="spa-navigation-buttons">
            <a href="<?php echo esc_url( get_post_type_archive_link( 'spa' ) ); ?>" class="spa-button">
                View All Spas <i class="fas fa-arrow-right"></i>
            </a>
            <a href="<?php echo esc_url( home_url() ); ?>" class="spa-button">
                <i class="fas fa-home"></i> Back To Homepage
            </a>
        </div>
    </main>
</div>

<?php
get_footer();