<?php
/**
 * Admin Schema Validator
 * 
 * Adds an admin page to validate and fix schema for all spa pages.
 */

// Add admin menu
function spasinbarcelona_add_schema_validator_page() {
    add_submenu_page(
        'edit.php?post_type=spa',
        'Schema Validator',
        'Schema Validator',
        'manage_options',
        'schema-validator',
        'spasinbarcelona_schema_validator_page'
    );
}
add_action('admin_menu', 'spasinbarcelona_add_schema_validator_page');

// Render the admin page
function spasinbarcelona_schema_validator_page() {
    // Check if we should validate or fix
    $action = isset($_GET['action']) ? sanitize_text_field($_GET['action']) : '';
    $spa_id = isset($_GET['spa_id']) ? intval($_GET['spa_id']) : 0;
    
    echo '<div class="wrap">';
    echo '<h1>Schema Validator</h1>';
    
    // Display action buttons
    echo '<div class="schema-actions" style="margin-bottom: 20px;">';
    echo '<a href="' . admin_url('edit.php?post_type=spa&page=schema-validator&action=validate') . '" class="button button-primary">Validate All Schemas</a> ';
    echo '<a href="' . admin_url('edit.php?post_type=spa&page=schema-validator&action=fix') . '" class="button">Fix All Schemas</a>';
    echo '</div>';
    
    // Process the selected action
    if ($action === 'validate') {
        spasinbarcelona_validate_all_schemas();
    } elseif ($action === 'fix') {
        spasinbarcelona_fix_all_schemas();
    } elseif ($action === 'view' && $spa_id > 0) {
        spasinbarcelona_view_spa_schema($spa_id);
    } else {
        // Show instructions
        echo '<div class="schema-instructions">';
        echo '<p>Use this page to validate and fix schema markup for all spa pages.</p>';
        echo '<ul>';
        echo '<li><strong>Validate All Schemas</strong> - Check all spa pages for complete schema information</li>';
        echo '<li><strong>Fix All Schemas</strong> - Attempt to fix any missing schema elements on all spa pages</li>';
        echo '</ul>';
        echo '</div>';
    }
    
    echo '</div>';
}

/**
 * Validate schemas for all spa pages
 */
function spasinbarcelona_validate_all_schemas() {
    // Get all spa posts
    $args = array(
        'post_type' => 'spa',
        'posts_per_page' => -1,
        'post_status' => 'publish'
    );
    
    $spa_query = new WP_Query($args);
    
    // Initialize counters for summary
    $total_spas = 0;
    $spas_with_complete_schema = 0;
    $spas_with_issues = 0;
    $missing_entities = array();
    $missing_properties = array();
    
    // Start output
    echo '<h2>Schema Validation Results</h2>';
    
    // Check if we have spa posts
    if ($spa_query->have_posts()) {
        echo '<p>Found ' . $spa_query->found_posts . ' spa pages to validate.</p>';
        
        // Create a table for summary
        echo '<table class="wp-list-table widefat fixed striped">';
        echo '<thead>';
        echo '<tr>';
        echo '<th>Spa Name</th>';
        echo '<th>Schema Status</th>';
        echo '<th>Missing Entities</th>';
        echo '<th>Missing Properties</th>';
        echo '<th>Actions</th>';
        echo '</tr>';
        echo '</thead>';
        echo '<tbody>';
        
        // Loop through each spa post
        while ($spa_query->have_posts()) {
            $spa_query->the_post();
            $post_id = get_the_ID();
            $post_title = get_the_title();
            
            $total_spas++;
            
            // Get the schema for this spa
            $schema_html = spasinbarcelona_get_spa_schema($post_id);
            
            // Extract JSON from the HTML
            preg_match_all('/<script type="application\/ld\+json">(.*?)<\/script>/s', $schema_html, $matches);
            
            echo '<tr>';
            echo '<td>' . esc_html($post_title) . '</td>';
            
            if (empty($matches[1])) {
                // No schema found
                echo '<td style="color: red;">No schema found</td>';
                echo '<td style="color: red;">All entities missing</td>';
                echo '<td style="color: red;">All properties missing</td>';
                $spas_with_issues++;
            } else {
                // Get the main schema
                $schema = json_decode($matches[1][0], true);
                
                if (!$schema) {
                    // Invalid JSON
                    echo '<td style="color: red;">Invalid JSON</td>';
                    echo '<td style="color: red;">Cannot determine</td>';
                    echo '<td style="color: red;">Cannot determine</td>';
                    $spas_with_issues++;
                } else {
                    // Check if the schema has a @graph property
                    if (!isset($schema['@graph'])) {
                        echo '<td style="color: red;">No @graph structure</td>';
                        echo '<td style="color: red;">All entities missing</td>';
                        echo '<td style="color: red;">All properties missing</td>';
                        $spas_with_issues++;
                    } else {
                        // Define the required entity types
                        $required_entities = array(
                            'HealthAndBeautyBusiness' => false,
                            'WebPage' => false,
                            'BreadcrumbList' => false,
                            'FAQPage' => false,
                            'TouristAttraction' => false
                        );
                        
                        // Track the business entity
                        $business_entity = null;
                        
                        // Check each entity in the graph
                        foreach ($schema['@graph'] as $entity) {
                            if (!isset($entity['@type'])) {
                                continue;
                            }
                            
                            $types = is_array($entity['@type']) ? $entity['@type'] : array($entity['@type']);
                            
                            // Check for business entity
                            if (in_array('HealthAndBeautyBusiness', $types)) {
                                $required_entities['HealthAndBeautyBusiness'] = true;
                                $business_entity = $entity;
                            }
                            
                            // Check for WebPage entity
                            if (in_array('WebPage', $types)) {
                                $required_entities['WebPage'] = true;
                            }
                            
                            // Check for BreadcrumbList entity
                            if (in_array('BreadcrumbList', $types)) {
                                $required_entities['BreadcrumbList'] = true;
                            }
                            
                            // Check for FAQPage entity
                            if (in_array('FAQPage', $types)) {
                                $required_entities['FAQPage'] = true;
                            }
                            
                            // Check for TouristAttraction entity
                            if (in_array('TouristAttraction', $types)) {
                                $required_entities['TouristAttraction'] = true;
                            }
                        }
                        
                        // Check if all required entities are present
                        $missing_entity_list = array();
                        foreach ($required_entities as $entity_type => $found) {
                            if (!$found) {
                                $missing_entity_list[] = $entity_type;
                                if (!isset($missing_entities[$entity_type])) {
                                    $missing_entities[$entity_type] = 0;
                                }
                                $missing_entities[$entity_type]++;
                            }
                        }
                        
                        // Check business entity properties if it exists
                        $missing_property_list = array();
                        if ($business_entity) {
                            $required_properties = array(
                                'name' => isset($business_entity['name']),
                                'alternateName' => isset($business_entity['alternateName']),
                                'description' => isset($business_entity['description']),
                                'url' => isset($business_entity['url']),
                                'address' => isset($business_entity['address']),
                                'geo' => isset($business_entity['geo']),
                                'hasMap' => isset($business_entity['hasMap']),
                                'telephone' => isset($business_entity['telephone']),
                                'email' => isset($business_entity['email']),
                                'sameAs' => isset($business_entity['sameAs']),
                                'image' => isset($business_entity['image']),
                                'openingHoursSpecification' => isset($business_entity['openingHoursSpecification']),
                                'amenityFeature' => isset($business_entity['amenityFeature']),
                                'additionalProperty' => isset($business_entity['additionalProperty']),
                                'areaServed' => isset($business_entity['areaServed'])
                            );
                            
                            foreach ($required_properties as $property => $found) {
                                if (!$found) {
                                    $missing_property_list[] = $property;
                                    if (!isset($missing_properties[$property])) {
                                        $missing_properties[$property] = 0;
                                    }
                                    $missing_properties[$property]++;
                                }
                            }
                        } else {
                            $missing_property_list[] = 'All (no business entity)';
                        }
                        
                        // Determine overall status
                        if (empty($missing_entity_list) && empty($missing_property_list)) {
                            echo '<td style="color: green;">Complete</td>';
                            $spas_with_complete_schema++;
                        } else {
                            echo '<td style="color: orange;">Incomplete</td>';
                            $spas_with_issues++;
                        }
                        
                        // Display missing entities
                        echo '<td>';
                        if (empty($missing_entity_list)) {
                            echo '<span style="color: green;">None</span>';
                        } else {
                            echo '<span style="color: orange;">' . implode(', ', $missing_entity_list) . '</span>';
                        }
                        echo '</td>';
                        
                        // Display missing properties
                        echo '<td>';
                        if (empty($missing_property_list)) {
                            echo '<span style="color: green;">None</span>';
                        } else {
                            echo '<span style="color: orange;">' . implode(', ', $missing_property_list) . '</span>';
                        }
                        echo '</td>';
                    }
                }
            }
            
            // Actions column
            echo '<td>';
            echo '<a href="' . admin_url('edit.php?post_type=spa&page=schema-validator&action=view&spa_id=' . $post_id) . '" class="button button-small">View Schema</a> ';
            echo '<a href="' . get_permalink($post_id) . '" target="_blank" class="button button-small">View Page</a>';
            echo '</td>';
            
            echo '</tr>';
        }
        
        echo '</tbody>';
        echo '</table>';
        
        // Reset post data
        wp_reset_postdata();
        
        // Display summary
        echo '<div class="schema-summary" style="margin-top: 20px; padding: 15px; background-color: #f5f5f5; border-radius: 5px;">';
        echo '<h3>Summary</h3>';
        echo '<p>Total Spa Pages: ' . $total_spas . '</p>';
        echo '<p>Pages with Complete Schema: ' . $spas_with_complete_schema . ' (' . round(($spas_with_complete_schema / $total_spas) * 100, 1) . '%)</p>';
        echo '<p>Pages with Schema Issues: ' . $spas_with_issues . ' (' . round(($spas_with_issues / $total_spas) * 100, 1) . '%)</p>';
        
        // Display missing entities summary
        if (!empty($missing_entities)) {
            echo '<h4>Missing Entities</h4>';
            echo '<ul>';
            foreach ($missing_entities as $entity => $count) {
                echo '<li>' . $entity . ': ' . $count . ' pages (' . round(($count / $total_spas) * 100, 1) . '%)</li>';
            }
            echo '</ul>';
        }
        
        // Display missing properties summary
        if (!empty($missing_properties)) {
            echo '<h4>Missing Properties</h4>';
            echo '<ul>';
            foreach ($missing_properties as $property => $count) {
                echo '<li>' . $property . ': ' . $count . ' pages (' . round(($count / $total_spas) * 100, 1) . '%)</li>';
            }
            echo '</ul>';
        }
        
        echo '</div>';
    } else {
        echo '<p style="color: red;">No spa pages found.</p>';
    }
}

/**
 * Fix schemas for all spa pages
 */
function spasinbarcelona_fix_all_schemas() {
    // Get all spa posts
    $args = array(
        'post_type' => 'spa',
        'posts_per_page' => -1,
        'post_status' => 'publish'
    );
    
    $spa_query = new WP_Query($args);
    
    // Initialize counters for summary
    $total_spas = 0;
    $spas_processed = 0;
    $spas_with_issues = 0;
    $spas_fixed = 0;
    
    // Start output
    echo '<h2>Schema Fix Results</h2>';
    
    // Check if we have spa posts
    if ($spa_query->have_posts()) {
        echo '<p>Found ' . $spa_query->found_posts . ' spa pages to process.</p>';
        
        // Create a table for results
        echo '<table class="wp-list-table widefat fixed striped">';
        echo '<thead>';
        echo '<tr>';
        echo '<th>Spa Name</th>';
        echo '<th>Initial Status</th>';
        echo '<th>Issues Found</th>';
        echo '<th>Fix Status</th>';
        echo '<th>Actions</th>';
        echo '</tr>';
        echo '</thead>';
        echo '<tbody>';
        
        // Loop through each spa post
        while ($spa_query->have_posts()) {
            $spa_query->the_post();
            $post_id = get_the_ID();
            $post_title = get_the_title();
            
            $total_spas++;
            
            echo '<tr>';
            echo '<td>' . esc_html($post_title) . '</td>';
            
            // Get the schema for this spa
            $schema_html = spasinbarcelona_get_spa_schema($post_id);
            
            // Extract JSON from the HTML
            preg_match_all('/<script type="application\/ld\+json">(.*?)<\/script>/s', $schema_html, $matches);
            
            $issues_found = array();
            
            if (empty($matches[1])) {
                // No schema found
                echo '<td style="color: red;">No schema found</td>';
                $issues_found[] = 'No schema generated';
                $spas_with_issues++;
            } else {
                // Get the main schema
                $schema = json_decode($matches[1][0], true);
                
                if (!$schema) {
                    // Invalid JSON
                    echo '<td style="color: red;">Invalid JSON</td>';
                    $issues_found[] = 'Invalid JSON in schema';
                    $spas_with_issues++;
                } else {
                    // Check if the schema has a @graph property
                    if (!isset($schema['@graph'])) {
                        echo '<td style="color: red;">No @graph structure</td>';
                        $issues_found[] = 'Missing @graph structure';
                        $spas_with_issues++;
                    } else {
                        // Define the required entity types
                        $required_entities = array(
                            'HealthAndBeautyBusiness' => false,
                            'WebPage' => false,
                            'BreadcrumbList' => false,
                            'FAQPage' => false,
                            'TouristAttraction' => false
                        );
                        
                        // Track the business entity
                        $business_entity = null;
                        
                        // Check each entity in the graph
                        foreach ($schema['@graph'] as $entity) {
                            if (!isset($entity['@type'])) {
                                continue;
                            }
                            
                            $types = is_array($entity['@type']) ? $entity['@type'] : array($entity['@type']);
                            
                            // Check for business entity
                            if (in_array('HealthAndBeautyBusiness', $types)) {
                                $required_entities['HealthAndBeautyBusiness'] = true;
                                $business_entity = $entity;
                            }
                            
                            // Check for WebPage entity
                            if (in_array('WebPage', $types)) {
                                $required_entities['WebPage'] = true;
                            }
                            
                            // Check for BreadcrumbList entity
                            if (in_array('BreadcrumbList', $types)) {
                                $required_entities['BreadcrumbList'] = true;
                            }
                            
                            // Check for FAQPage entity
                            if (in_array('FAQPage', $types)) {
                                $required_entities['FAQPage'] = true;
                            }
                            
                            // Check for TouristAttraction entity
                            if (in_array('TouristAttraction', $types)) {
                                $required_entities['TouristAttraction'] = true;
                            }
                        }
                        
                        // Check if all required entities are present
                        $missing_entity_list = array();
                        foreach ($required_entities as $entity_type => $found) {
                            if (!$found) {
                                $missing_entity_list[] = $entity_type;
                            }
                        }
                        
                        // Check business entity properties if it exists
                        $missing_property_list = array();
                        if ($business_entity) {
                            $required_properties = array(
                                'name' => isset($business_entity['name']),
                                'alternateName' => isset($business_entity['alternateName']),
                                'description' => isset($business_entity['description']),
                                'url' => isset($business_entity['url']),
                                'address' => isset($business_entity['address']),
                                'geo' => isset($business_entity['geo']),
                                'hasMap' => isset($business_entity['hasMap']),
                                'telephone' => isset($business_entity['telephone']),
                                'email' => isset($business_entity['email']),
                                'sameAs' => isset($business_entity['sameAs']),
                                'image' => isset($business_entity['image']),
                                'openingHoursSpecification' => isset($business_entity['openingHoursSpecification']),
                                'amenityFeature' => isset($business_entity['amenityFeature']),
                                'additionalProperty' => isset($business_entity['additionalProperty']),
                                'areaServed' => isset($business_entity['areaServed'])
                            );
                            
                            foreach ($required_properties as $property => $found) {
                                if (!$found) {
                                    $missing_property_list[] = $property;
                                }
                            }
                        } else {
                            $missing_property_list[] = 'All (no business entity)';
                        }
                        
                        // Determine overall status
                        if (empty($missing_entity_list) && empty($missing_property_list)) {
                            echo '<td style="color: green;">Complete</td>';
                            echo '<td>None</td>';
                            echo '<td style="color: green;">No fixes needed</td>';
                            $spas_processed++;
                        } else {
                            echo '<td style="color: orange;">Incomplete</td>';
                            
                            // Collect issues
                            if (!empty($missing_entity_list)) {
                                $issues_found[] = 'Missing entities: ' . implode(', ', $missing_entity_list);
                            }
                            if (!empty($missing_property_list)) {
                                $issues_found[] = 'Missing properties: ' . implode(', ', $missing_property_list);
                            }
                            
                            // Display issues
                            echo '<td>' . implode('<br>', $issues_found) . '</td>';
                            
                            // Fix the issues by regenerating the schema
                            // This will use the latest version of spasinbarcelona_get_spa_schema
                            // which should include all the fixes we've made
                            
                            // Clear any post meta caches
                            clean_post_cache($post_id);
                            
                            // Regenerate the schema
                            $new_schema_html = spasinbarcelona_get_spa_schema($post_id);
                            
                            // Check if the new schema has all required elements
                            preg_match_all('/<script type="application\/ld\+json">(.*?)<\/script>/s', $new_schema_html, $new_matches);
                            
                            if (!empty($new_matches[1])) {
                                $new_schema = json_decode($new_matches[1][0], true);
                                
                                if ($new_schema && isset($new_schema['@graph'])) {
                                    // Check if all required entities are now present
                                    $new_required_entities = array(
                                        'HealthAndBeautyBusiness' => false,
                                        'WebPage' => false,
                                        'BreadcrumbList' => false,
                                        'FAQPage' => false,
                                        'TouristAttraction' => false
                                    );
                                    
                                    foreach ($new_schema['@graph'] as $entity) {
                                        if (!isset($entity['@type'])) {
                                            continue;
                                        }
                                        
                                        $types = is_array($entity['@type']) ? $entity['@type'] : array($entity['@type']);
                                        
                                        // Check for each required entity type
                                        foreach ($new_required_entities as $entity_type => $found) {
                                            if (in_array($entity_type, $types)) {
                                                $new_required_entities[$entity_type] = true;
                                            }
                                        }
                                    }
                                    
                                    // Check if all required entities are now present
                                    $still_missing_entities = array();
                                    foreach ($new_required_entities as $entity_type => $found) {
                                        if (!$found) {
                                            $still_missing_entities[] = $entity_type;
                                        }
                                    }
                                    
                                    if (empty($still_missing_entities)) {
                                        echo '<td style="color: green;">Fixed</td>';
                                        $spas_fixed++;
                                    } else {
                                        echo '<td style="color: orange;">Partially fixed, still missing: ' . implode(', ', $still_missing_entities) . '</td>';
                                        $spas_with_issues++;
                                    }
                                } else {
                                    echo '<td style="color: red;">Failed to fix</td>';
                                    $spas_with_issues++;
                                }
                            } else {
                                echo '<td style="color: red;">Failed to fix</td>';
                                $spas_with_issues++;
                            }
                        }
                    }
                }
            }
            
            // Actions column
            echo '<td>';
            echo '<a href="' . admin_url('edit.php?post_type=spa&page=schema-validator&action=view&spa_id=' . $post_id) . '" class="button button-small">View Schema</a> ';
            echo '<a href="' . get_permalink($post_id) . '" target="_blank" class="button button-small">View Page</a>';
            echo '</td>';
            
            echo '</tr>';
        }
        
        echo '</tbody>';
        echo '</table>';
        
        // Reset post data
        wp_reset_postdata();
        
        // Display summary
        echo '<div class="schema-summary" style="margin-top: 20px; padding: 15px; background-color: #f5f5f5; border-radius: 5px;">';
        echo '<h3>Summary</h3>';
        echo '<p>Total Spa Pages: ' . $total_spas . '</p>';
        echo '<p>Pages Already Complete: ' . $spas_processed . ' (' . round(($spas_processed / $total_spas) * 100, 1) . '%)</p>';
        echo '<p>Pages Fixed: ' . $spas_fixed . ' (' . round(($spas_fixed / $total_spas) * 100, 1) . '%)</p>';
        echo '<p>Pages Still With Issues: ' . $spas_with_issues . ' (' . round(($spas_with_issues / $total_spas) * 100, 1) . '%)</p>';
        echo '</div>';
    } else {
        echo '<p style="color: red;">No spa pages found.</p>';
    }
}

/**
 * View schema for a specific spa
 */
function spasinbarcelona_view_spa_schema($spa_id) {
    // Get the spa post
    $spa_post = get_post($spa_id);
    
    if (!$spa_post || $spa_post->post_type !== 'spa') {
        echo '<p style="color: red;">Invalid spa ID or spa not found.</p>';
        return;
    }
    
    echo '<h2>Schema for: ' . get_the_title($spa_id) . '</h2>';
    echo '<p><a href="' . get_permalink($spa_id) . '" target="_blank" class="button">View Spa Page</a></p>';
    
    // Get the schema for this spa
    $schema_html = spasinbarcelona_get_spa_schema($spa_id);
    
    // Extract JSON from the HTML
    preg_match_all('/<script type="application\/ld\+json">(.*?)<\/script>/s', $schema_html, $matches);
    
    if (empty($matches[1])) {
        echo '<p style="color: red;">No schema found for this spa.</p>';
        return;
    }
    
    // Process each schema block
    foreach ($matches[1] as $index => $json) {
        $schema = json_decode($json, true);
        
        if (!$schema) {
            echo '<div style="margin-bottom: 30px; border: 1px solid #ddd; padding: 15px; border-radius: 5px;">';
            echo '<p style="color: red;">Invalid JSON in schema block #' . ($index + 1) . '</p>';
            echo '</div>';
            continue;
        }
        
        $schema_type = isset($schema['@type']) ? 
            (is_array($schema['@type']) ? implode(', ', $schema['@type']) : $schema['@type']) : 
            (isset($schema['@graph']) ? '@graph structure' : 'Unknown');
        
        echo '<div style="margin-bottom: 30px; border: 1px solid #ddd; padding: 15px; border-radius: 5px;">';
        echo '<h3>Schema Block #' . ($index + 1) . ': ' . esc_html($schema_type) . '</h3>';
        
        // If this is a @graph structure, show the entities
        if (isset($schema['@graph'])) {
            echo '<h4>Graph Structure</h4>';
            echo '<p>This graph contains ' . count($schema['@graph']) . ' entities:</p>';
            
            echo '<ul style="margin-left: 20px;">';
            foreach ($schema['@graph'] as $entity_index => $entity) {
                $entity_type = isset($entity['@type']) ? 
                    (is_array($entity['@type']) ? implode(', ', $entity['@type']) : $entity['@type']) : 
                    'Unknown';
                
                echo '<li style="margin-bottom: 10px;">';
                echo '<strong>Entity #' . ($entity_index + 1) . ':</strong> ' . esc_html($entity_type);
                
                // Show entity ID if available
                if (isset($entity['@id'])) {
                    echo ' (ID: ' . esc_html($entity['@id']) . ')';
                }
                
                echo '</li>';
            }
            echo '</ul>';
        }
        
        // Show the raw JSON
        echo '<h4>Raw JSON</h4>';
        echo '<div style="background-color: #f5f5f5; padding: 10px; overflow: auto; max-height: 400px;">';
        echo '<pre>' . esc_html(json_encode($schema, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES)) . '</pre>';
        echo '</div>';
        
        echo '</div>';
    }
}
