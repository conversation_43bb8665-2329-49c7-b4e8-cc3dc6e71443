<?php
/**
 * The template for displaying spa services archives
 * This template is used for the /spa-services/ URL structure
 */

get_header();
?>



<div id="primary" class="content-area">
    <main id="main" class="site-main">
        <header class="page-header">
            <div class="page-title-wrapper">
                <?php
                // Get the current term slug from the URL
                $current_url = $_SERVER['REQUEST_URI'];
                $url_parts = explode('/', trim($current_url, '/'));
                $term_slug = end($url_parts);

                // Get the term object
                $term = get_term_by('slug', $term_slug, 'spa_service');

                if ($term) {
                    echo '<h1 class="page-title">Spas in Barcelona with ' . esc_html($term->name) . '</h1>';
                } else {
                    echo '<h1 class="page-title">' . wp_kses_post(get_the_archive_title()) . '</h1>';
                }
                ?>
            </div>
            <div class="archive-description">
                <?php
                if ($term) {
                    echo '<p>Discover the best spas in Barcelona offering ' . esc_html($term->name) . '.</p>';
                    echo '<p>Browse our selection of spas with this service.</p>';
                } else {
                    echo wp_kses_post(get_the_archive_description());
                }
                ?>
            </div>
            
        </header>

        <div class="spa-results" style="width: 100%;">
            <?php
            // Get the term slug from the query var
            $term_slug = get_query_var('spa_service_term_slug');
            if (empty($term_slug)) {
                // Fallback to URL parsing if query var is not set
                $current_url = $_SERVER['REQUEST_URI'];
                $url_parts = explode('/', trim($current_url, '/'));
                $term_slug = end($url_parts);
            }

            // Get the query args from the query var or create new ones
            $args = get_query_var('spa_service_query_args');
            if (empty($args)) {
                // Fallback to creating our own args
                $args = array(
                    'post_type' => 'spa',
                    'posts_per_page' => -1,
                    'tax_query' => array(
                        array(
                            'taxonomy' => 'spa_service',
                            'field' => 'slug',
                            'terms' => $term_slug,
                        ),
                    ),
                );
            }

            // Debug output removed

            $spa_query = new WP_Query($args);

            if ($spa_query->have_posts()) :
            ?>
                <div class="spa-grid">
                    <?php
                    while ($spa_query->have_posts()) :
                        $spa_query->the_post();
                        get_template_part('template-parts/content', 'spa-card');
                    endwhile;
                    wp_reset_postdata();
                    ?>
                </div>
            <?php else : ?>
                <div class="no-results">
                    <p><?php esc_html_e('No spas found matching your criteria.', 'spasinbarcelona'); ?></p>
                </div>
            <?php endif; ?>
        </div>

        <!-- Navigation Buttons -->
        <div class="spa-navigation-buttons">
            <a href="<?php echo esc_url( get_post_type_archive_link( 'spa' ) ); ?>" class="spa-button">
                View All Spas <i class="fas fa-arrow-right"></i>
            </a>
            <a href="<?php echo esc_url( home_url() ); ?>" class="spa-button">
                <i class="fas fa-home"></i> Back To Homepage
            </a>
        </div>

        <?php if ($spa_query->max_num_pages > 1) : ?>
        <div class="spa-pagination">
            <?php
            $big = 999999999;
            echo paginate_links( array(
                'base' => str_replace( $big, '%#%', esc_url( get_pagenum_link( $big ) ) ),
                'format' => '?paged=%#%',
                'current' => max( 1, get_query_var( 'paged' ) ),
                'total' => $spa_query->max_num_pages,
                'prev_text' => '<i class="fas fa-chevron-left"></i> Previous',
                'next_text' => 'Next <i class="fas fa-chevron-right"></i>',
            ) );
            ?>
        </div>
        <?php endif; ?>
    </main>
</div>

<?php
get_footer();
